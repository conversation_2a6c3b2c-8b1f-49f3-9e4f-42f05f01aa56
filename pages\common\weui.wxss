/*!
 * WeUI v2.3.0 (https://github.com/weui/weui-wxss)
 * Copyright 2020 Tencent, Inc.
 * Licensed under the MIT license
 */
[data-weui-theme=light],
page {
  --weui-BTN-DISABLED-FONT-COLOR: rgba(0, 0, 0, .2)
}

@media(prefers-color-scheme: dark) {
  page{
    --weui-BTN-DISABLED-FONT-COLOR: hsla(0, 0%, 100%, .2)
  }
}

[data-weui-theme=light],
page {
  --weui-BTN-DEFAULT-BG: #f2f2f2
}

@media(prefers-color-scheme: dark) {
  page{
    --weui-BTN-DEFAULT-BG: hsla(0, 0%, 100%, .08)
  }
}

[data-weui-theme=light],
page {
  --weui-BTN-DEFAULT-COLOR: #06ae56
}

@media(prefers-color-scheme: dark) {
  page{
    --weui-BTN-DEFAULT-COLOR: hsla(0, 0%, 100%, .8)
  }
}

[data-weui-theme=light],
page {
  --weui-BTN-DEFAULT-ACTIVE-BG: #e6e6e6
}

@media(prefers-color-scheme: dark) {
  page{
    --weui-BTN-DEFAULT-ACTIVE-BG: hsla(0, 0%, 100%, .126)
  }
}

[data-weui-theme=light],
page {
  --weui-DIALOG-LINE-COLOR: rgba(0, 0, 0, .1)
}

@media(prefers-color-scheme: dark) {
  page{
    --weui-DIALOG-LINE-COLOR: hsla(0, 0%, 100%, .1)
  }
}

page {
  line-height: 1.6;
  font-family: -apple-system-font, Helvetica Neue, sans-serif
}

icon {
  vertical-align: middle
}

[data-weui-theme=light],
page {
  --weui-BG-0: #ededed;
  --weui-BG-1: #f7f7f7;
  --weui-BG-2: #fff;
  --weui-BG-3: #f7f7f7;
  --weui-BG-4: #4c4c4c;
  --weui-BG-5: #fff;
  --weui-FG-0: rgba(0, 0, 0, .9);
  --weui-FG-HALF: rgba(0, 0, 0, .9);
  --weui-FG-1: rgba(0, 0, 0, .5);
  --weui-FG-2: rgba(0, 0, 0, .3);
  --weui-FG-3: rgba(0, 0, 0, .1);
  --weui-RED: #fa5151;
  --weui-ORANGE: #fa9d3b;
  --weui-YELLOW: #ffc300;
  --weui-GREEN: #91d300;
  --weui-LIGHTGREEN: #95ec69;
  --weui-BRAND: #07c160;
  --weui-BLUE: #10aeff;
  --weui-INDIGO: #1485ee;
  --weui-PURPLE: #6467f0;
  --weui-WHITE: #fff;
  --weui-LINK: #576b95;
  --weui-TEXTGREEN: #06ae56;
  --weui-FG: #000;
  --weui-BG: #fff;
  --weui-TAG-TEXT-ORANGE: #fa9d3b;
  --weui-TAG-BACKGROUND-ORANGE: rgba(250, 157, 59, .1);
  --weui-TAG-TEXT-GREEN: #06ae56;
  --weui-TAG-BACKGROUND-GREEN: rgba(6, 174, 86, .1);
  --weui-TAG-TEXT-BLUE: #10aeff;
  --weui-TAG-BACKGROUND-BLUE: rgba(16, 174, 255, .1);
  --weui-TAG-TEXT-BLACK: rgba(0, 0, 0, .5);
  --weui-TAG-BACKGROUND-BLACK: rgba(0, 0, 0, .05)
}

@media(prefers-color-scheme: dark) {
  page{
    --weui-BG-0: #191919;
    --weui-BG-1: #1f1f1f;
    --weui-BG-2: #232323;
    --weui-BG-3: #2f2f2f;
    --weui-BG-4: #606060;
    --weui-BG-5: #2c2c2c;
    --weui-FG-0: hsla(0, 0%, 100%, .8);
    --weui-FG-HALF: hsla(0, 0%, 100%, .6);
    --weui-FG-1: hsla(0, 0%, 100%, .5);
    --weui-FG-2: hsla(0, 0%, 100%, .3);
    --weui-FG-3: hsla(0, 0%, 100%, .05);
    --weui-RED: #fa5151;
    --weui-ORANGE: #c87d2f;
    --weui-YELLOW: #cc9c00;
    --weui-GREEN: #74a800;
    --weui-LIGHTGREEN: #28b561;
    --weui-BRAND: #07c160;
    --weui-BLUE: #10aeff;
    --weui-INDIGO: #1196ff;
    --weui-PURPLE: #8183ff;
    --weui-WHITE: hsla(0, 0%, 100%, .8);
    --weui-LINK: #7d90a9;
    --weui-TEXTGREEN: #259c5c;
    --weui-FG: #fff;
    --weui-BG: #000;
    --weui-TAG-TEXT-ORANGE: rgba(250, 157, 59, .6);
    --weui-TAG-BACKGROUND-ORANGE: rgba(250, 157, 59, .1);
    --weui-TAG-TEXT-GREEN: rgba(6, 174, 86, .6);
    --weui-TAG-BACKGROUND-GREEN: rgba(6, 174, 86, .1);
    --weui-TAG-TEXT-BLUE: rgba(16, 174, 255, .6);
    --weui-TAG-BACKGROUND-BLUE: rgba(16, 174, 255, .1);
    --weui-TAG-TEXT-BLACK: hsla(0, 0%, 100%, .5);
    --weui-TAG-BACKGROUND-BLACK: hsla(0, 0%, 100%, .05)
  }
}

[data-weui-theme=light],
page {
  --weui-BG-COLOR-ACTIVE: #ececec
}

@media(prefers-color-scheme: dark) {
  page{
    --weui-BG-COLOR-ACTIVE: #373737
  }
}

[class*=" weui-icon-"],
[class^=weui-icon-] {
  display: inline-block;
  vertical-align: middle;
  width: 24px;
  height: 24px;
  -webkit-mask-position: 50% 50%;
  mask-position: 50% 50%;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100%;
  mask-size: 100%;
  background-color: currentColor
}

.weui-icon-circle {
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%221000%22%20height%3D%221000%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M500%20916.667C269.881%20916.667%2083.333%20730.119%2083.333%20500%2083.333%20269.881%20269.881%2083.333%20500%2083.333c230.119%200%20416.667%20186.548%20416.667%20416.667%200%20230.119-186.548%20416.667-416.667%20416.667zm0-50c202.504%200%20366.667-164.163%20366.667-366.667%200-202.504-164.163-366.667-366.667-366.667-202.504%200-366.667%20164.163-366.667%20366.667%200%20202.504%20164.163%20366.667%20366.667%20366.667z%22%20fill-rule%3D%22evenodd%22%20fill-opacity%3D%22.9%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%221000%22%20height%3D%221000%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M500%20916.667C269.881%20916.667%2083.333%20730.119%2083.333%20500%2083.333%20269.881%20269.881%2083.333%20500%2083.333c230.119%200%20416.667%20186.548%20416.667%20416.667%200%20230.119-186.548%20416.667-416.667%20416.667zm0-50c202.504%200%20366.667-164.163%20366.667-366.667%200-202.504-164.163-366.667-366.667-366.667-202.504%200-366.667%20164.163-366.667%20366.667%200%20202.504%20164.163%20366.667%20366.667%20366.667z%22%20fill-rule%3D%22evenodd%22%20fill-opacity%3D%22.9%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-download {
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M11.25%2012.04l-1.72-1.72-1.06%201.06%202.828%202.83a1%201%200%20001.414-.001l2.828-2.828-1.06-1.061-1.73%201.73V7h-1.5v5.04zm0-5.04V2h1.5v5h6.251c.55%200%20.999.446.999.996v13.008a.998.998%200%2001-.996.996H4.996A.998.998%200%20014%2021.004V7.996A1%201%200%20014.999%207h6.251z%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M11.25%2012.04l-1.72-1.72-1.06%201.06%202.828%202.83a1%201%200%20001.414-.001l2.828-2.828-1.06-1.061-1.73%201.73V7h-1.5v5.04zm0-5.04V2h1.5v5h6.251c.55%200%20.999.446.999.996v13.008a.998.998%200%2001-.996.996H4.996A.998.998%200%20014%2021.004V7.996A1%201%200%20014.999%207h6.251z%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-info {
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm-.75-12v7h1.5v-7h-1.5zM12%209a1%201%200%20100-2%201%201%200%20000%202z%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm-.75-12v7h1.5v-7h-1.5zM12%209a1%201%200%20100-2%201%201%200%20000%202z%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-safe-success {
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%201000%201000%22%3E%3Cpath%20d%3D%22M500.9%204.6C315.5%2046.7%20180.4%2093.1%2057.6%20132c0%20129.3.2%20231.7.2%20339.7%200%20304.2%20248.3%20471.6%20443.1%20523.7C695.7%20943.3%20944%20775.9%20944%20471.7c0-108%20.2-210.4.2-339.7C821.4%2093.1%20686.3%2046.7%20500.9%204.6zm248.3%20349.1l-299.7%20295c-2.1%202-5.3%202-7.4-.1L304.4%20506.1c-2-2.1-2.3-5.7-.6-8l18.3-24.9c1.7-2.3%205-2.8%207.2-1l112.2%2086c2.3%201.8%206%201.7%208.1-.1l274.7-228.9c2.2-1.8%205.7-1.7%207.7.3l17%2016.8c2.2%202.1%202.2%205.3.2%207.4z%22%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20fill%3D%22%23070202%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%201000%201000%22%3E%3Cpath%20d%3D%22M500.9%204.6C315.5%2046.7%20180.4%2093.1%2057.6%20132c0%20129.3.2%20231.7.2%20339.7%200%20304.2%20248.3%20471.6%20443.1%20523.7C695.7%20943.3%20944%20775.9%20944%20471.7c0-108%20.2-210.4.2-339.7C821.4%2093.1%20686.3%2046.7%20500.9%204.6zm248.3%20349.1l-299.7%20295c-2.1%202-5.3%202-7.4-.1L304.4%20506.1c-2-2.1-2.3-5.7-.6-8l18.3-24.9c1.7-2.3%205-2.8%207.2-1l112.2%2086c2.3%201.8%206%201.7%208.1-.1l274.7-228.9c2.2-1.8%205.7-1.7%207.7.3l17%2016.8c2.2%202.1%202.2%205.3.2%207.4z%22%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20fill%3D%22%23070202%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-safe-warn {
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%201000%201000%22%3E%3Cpath%20d%3D%22M500.9%204.5c-185.4%2042-320.4%2088.4-443.2%20127.3%200%20129.3.2%20231.7.2%20339.6%200%20304.1%20248.2%20471.4%20443%20523.6%20194.7-52.2%20443-219.5%20443-523.6%200-107.9.2-210.3.2-339.6C821.3%2092.9%20686.2%2046.5%20500.9%204.5zm-26.1%20271.1h52.1c5.8%200%2010.3%204.7%2010.1%2010.4l-11.6%20313.8c-.1%202.8-2.5%205.2-5.4%205.2h-38.2c-2.9%200-5.3-2.3-5.4-5.2L464.8%20286c-.2-5.8%204.3-10.4%2010-10.4zm26.1%20448.3c-20.2%200-36.5-16.3-36.5-36.5s16.3-36.5%2036.5-36.5%2036.5%2016.3%2036.5%2036.5-16.4%2036.5-36.5%2036.5z%22%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20fill%3D%22%23020202%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%201000%201000%22%3E%3Cpath%20d%3D%22M500.9%204.5c-185.4%2042-320.4%2088.4-443.2%20127.3%200%20129.3.2%20231.7.2%20339.6%200%20304.1%20248.2%20471.4%20443%20523.6%20194.7-52.2%20443-219.5%20443-523.6%200-107.9.2-210.3.2-339.6C821.3%2092.9%20686.2%2046.5%20500.9%204.5zm-26.1%20271.1h52.1c5.8%200%2010.3%204.7%2010.1%2010.4l-11.6%20313.8c-.1%202.8-2.5%205.2-5.4%205.2h-38.2c-2.9%200-5.3-2.3-5.4-5.2L464.8%20286c-.2-5.8%204.3-10.4%2010-10.4zm26.1%20448.3c-20.2%200-36.5-16.3-36.5-36.5s16.3-36.5%2036.5-36.5%2036.5%2016.3%2036.5%2036.5-16.4%2036.5-36.5%2036.5z%22%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20fill%3D%22%23020202%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-success {
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm-1.177-7.86l-2.765-2.767L7%2012.431l3.119%203.121a1%201%200%20001.414%200l5.952-5.95-1.062-1.062-5.6%205.6z%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm-1.177-7.86l-2.765-2.767L7%2012.431l3.119%203.121a1%201%200%20001.414%200l5.952-5.95-1.062-1.062-5.6%205.6z%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-success-circle {
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm0-1.2a8.8%208.8%200%20100-17.6%208.8%208.8%200%20000%2017.6zm-1.172-6.242l5.809-5.808.848.849-5.95%205.95a1%201%200%2001-1.414%200L7%2012.426l.849-.849%202.98%202.98z%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm0-1.2a8.8%208.8%200%20100-17.6%208.8%208.8%200%20000%2017.6zm-1.172-6.242l5.809-5.808.848.849-5.95%205.95a1%201%200%2001-1.414%200L7%2012.426l.849-.849%202.98%202.98z%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-success-no-circle {
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M8.657%2018.435L3%2012.778l1.414-1.414%204.95%204.95L20.678%205l1.414%201.414-12.02%2012.021a1%201%200%2001-1.415%200z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M8.657%2018.435L3%2012.778l1.414-1.414%204.95%204.95L20.678%205l1.414%201.414-12.02%2012.021a1%201%200%2001-1.415%200z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-waiting {
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12.75%2011.38V6h-1.5v6l4.243%204.243%201.06-1.06-3.803-3.804zM12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12.75%2011.38V6h-1.5v6l4.243%204.243%201.06-1.06-3.803-3.804zM12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-waiting-circle {
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12.6%2011.503l3.891%203.891-.848.849L11.4%2012V6h1.2v5.503zM12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm0-1.2a8.8%208.8%200%20100-17.6%208.8%208.8%200%20000%2017.6z%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12.6%2011.503l3.891%203.891-.848.849L11.4%2012V6h1.2v5.503zM12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm0-1.2a8.8%208.8%200%20100-17.6%208.8%208.8%200%20000%2017.6z%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-warn {
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm-.763-15.864l.11%207.596h1.305l.11-7.596h-1.525zm.759%2010.967c.512%200%20.902-.383.902-.882%200-.5-.39-.882-.902-.882a.878.878%200%2000-.896.882c0%20.499.396.882.896.882z%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm-.763-15.864l.11%207.596h1.305l.11-7.596h-1.525zm.759%2010.967c.512%200%20.902-.383.902-.882%200-.5-.39-.882-.902-.882a.878.878%200%2000-.896.882c0%20.499.396.882.896.882z%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-info-circle {
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm0-1.2a8.8%208.8%200%20100-17.6%208.8%208.8%200%20000%2017.6zM11.4%2010h1.2v7h-1.2v-7zm.6-1a1%201%200%20110-2%201%201%200%20010%202z%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm0-1.2a8.8%208.8%200%20100-17.6%208.8%208.8%200%20000%2017.6zM11.4%2010h1.2v7h-1.2v-7zm.6-1a1%201%200%20110-2%201%201%200%20010%202z%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-cancel {
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cg%20fill-rule%3D%22evenodd%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm0-1.2a8.8%208.8%200%20100-17.6%208.8%208.8%200%20000%2017.6z%22%20fill-rule%3D%22nonzero%22%2F%3E%3Cpath%20d%3D%22M12.849%2012l3.11%203.111-.848.849L12%2012.849l-3.111%203.11-.849-.848L11.151%2012l-3.11-3.111.848-.849L12%2011.151l3.111-3.11.849.848L12.849%2012z%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cg%20fill-rule%3D%22evenodd%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm0-1.2a8.8%208.8%200%20100-17.6%208.8%208.8%200%20000%2017.6z%22%20fill-rule%3D%22nonzero%22%2F%3E%3Cpath%20d%3D%22M12.849%2012l3.11%203.111-.848.849L12%2012.849l-3.111%203.11-.849-.848L11.151%2012l-3.11-3.111.848-.849L12%2011.151l3.111-3.11.849.848L12.849%2012z%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E)
}

.weui-icon-search {
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M16.31%2015.561l4.114%204.115-.848.848-4.123-4.123a7%207%200%2011.857-.84zM16.8%2011a5.8%205.8%200%2010-11.6%200%205.8%205.8%200%200011.6%200z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M16.31%2015.561l4.114%204.115-.848.848-4.123-4.123a7%207%200%2011.857-.84zM16.8%2011a5.8%205.8%200%2010-11.6%200%205.8%205.8%200%200011.6%200z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-clear {
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M13.06%2012l3.006-3.005-1.06-1.06L12%2010.938%208.995%207.934l-1.06%201.06L10.938%2012l-3.005%203.005%201.06%201.06L12%2013.062l3.005%203.005%201.06-1.06L13.062%2012zM12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010z%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M13.06%2012l3.006-3.005-1.06-1.06L12%2010.938%208.995%207.934l-1.06%201.06L10.938%2012l-3.005%203.005%201.06%201.06L12%2013.062l3.005%203.005%201.06-1.06L13.062%2012zM12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010z%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-back {
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm1.999-6.563L10.68%2012%2014%208.562%2012.953%207.5%209.29%2011.277a1.045%201.045%200%20000%201.446l3.663%203.777L14%2015.437z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm1.999-6.563L10.68%2012%2014%208.562%2012.953%207.5%209.29%2011.277a1.045%201.045%200%20000%201.446l3.663%203.777L14%2015.437z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-delete {
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M6.774%206.4l.812%2013.648a.8.8%200%2000.798.752h7.232a.8.8%200%2000.798-.752L17.226%206.4H6.774zm11.655%200l-.817%2013.719A2%202%200%200115.616%2022H8.384a2%202%200%2001-1.996-1.881L5.571%206.4H3.5v-.7a.5.5%200%2001.5-.5h16a.5.5%200%2001.5.5v.7h-2.071zM14%203a.5.5%200%2001.5.5v.7h-5v-.7A.5.5%200%200110%203h4zM9.5%209h1.2l.5%209H10l-.5-9zm3.8%200h1.2l-.5%209h-1.2l.5-9z%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M6.774%206.4l.812%2013.648a.8.8%200%2000.798.752h7.232a.8.8%200%2000.798-.752L17.226%206.4H6.774zm11.655%200l-.817%2013.719A2%202%200%200115.616%2022H8.384a2%202%200%2001-1.996-1.881L5.571%206.4H3.5v-.7a.5.5%200%2001.5-.5h16a.5.5%200%2001.5.5v.7h-2.071zM14%203a.5.5%200%2001.5.5v.7h-5v-.7A.5.5%200%200110%203h4zM9.5%209h1.2l.5%209H10l-.5-9zm3.8%200h1.2l-.5%209h-1.2l.5-9z%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-success-no-circle-thin {
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M8.864%2016.617l-5.303-5.303-1.061%201.06%205.657%205.657a1%201%200%20001.414%200L21.238%206.364l-1.06-1.06L8.864%2016.616z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M8.864%2016.617l-5.303-5.303-1.061%201.06%205.657%205.657a1%201%200%20001.414%200L21.238%206.364l-1.06-1.06L8.864%2016.616z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-arrow {
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M2.454%206.58l1.06-1.06%205.78%205.779a.996.996%200%20010%201.413l-5.78%205.779-1.06-1.061%205.425-5.425-5.425-5.424z%22%20fill%3D%22%23B2B2B2%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M2.454%206.58l1.06-1.06%205.78%205.779a.996.996%200%20010%201.413l-5.78%205.779-1.06-1.061%205.425-5.425-5.425-5.424z%22%20fill%3D%22%23B2B2B2%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-arrow-bold {
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20height%3D%2224%22%20width%3D%2212%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M10.157%2012.711L4.5%2018.368l-1.414-1.414%204.95-4.95-4.95-4.95L4.5%205.64l5.657%205.657a1%201%200%20010%201.414z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20height%3D%2224%22%20width%3D%2212%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M10.157%2012.711L4.5%2018.368l-1.414-1.414%204.95-4.95-4.95-4.95L4.5%205.64l5.657%205.657a1%201%200%20010%201.414z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-back-arrow {
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M3.343%2012l7.071%207.071L9%2020.485l-7.778-7.778a1%201%200%20010-1.414L9%203.515l1.414%201.414L3.344%2012z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M3.343%2012l7.071%207.071L9%2020.485l-7.778-7.778a1%201%200%20010-1.414L9%203.515l1.414%201.414L3.344%2012z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-back-arrow-thin {
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M10%2019.438L8.955%2020.5l-7.666-7.79a1.02%201.02%200%20010-1.42L8.955%203.5%2010%204.563%202.682%2012%2010%2019.438z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M10%2019.438L8.955%2020.5l-7.666-7.79a1.02%201.02%200%20010-1.42L8.955%203.5%2010%204.563%202.682%2012%2010%2019.438z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-close {
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2010.586l5.657-5.657%201.414%201.414L13.414%2012l5.657%205.657-1.414%201.414L12%2013.414l-5.657%205.657-1.414-1.414L10.586%2012%204.929%206.343%206.343%204.93%2012%2010.586z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2010.586l5.657-5.657%201.414%201.414L13.414%2012l5.657%205.657-1.414%201.414L12%2013.414l-5.657%205.657-1.414-1.414L10.586%2012%204.929%206.343%206.343%204.93%2012%2010.586z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-close-thin {
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12.25%2010.693L6.057%204.5%205%205.557l6.193%206.193L5%2017.943%206.057%2019l6.193-6.193L18.443%2019l1.057-1.057-6.193-6.193L19.5%205.557%2018.443%204.5z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12.25%2010.693L6.057%204.5%205%205.557l6.193%206.193L5%2017.943%206.057%2019l6.193-6.193L18.443%2019l1.057-1.057-6.193-6.193L19.5%205.557%2018.443%204.5z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-back-circle {
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm0-1.2a8.8%208.8%200%20100-17.6%208.8%208.8%200%20000%2017.6zm1.999-5.363L12.953%2016.5%209.29%2012.723a1.045%201.045%200%20010-1.446L12.953%207.5%2014%208.563%2010.68%2012%2014%2015.438z%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm0-1.2a8.8%208.8%200%20100-17.6%208.8%208.8%200%20000%2017.6zm1.999-5.363L12.953%2016.5%209.29%2012.723a1.045%201.045%200%20010-1.446L12.953%207.5%2014%208.563%2010.68%2012%2014%2015.438z%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-success {
  color: var(--weui-BRAND)
}

.weui-icon-waiting {
  color: var(--weui-BLUE)
}

.weui-icon-warn {
  color: var(--weui-RED)
}

.weui-icon-info {
  color: var(--weui-BLUE)
}

.weui-icon-success-circle,
.weui-icon-success-no-circle,
.weui-icon-success-no-circle-thin {
  color: var(--weui-BRAND)
}

.weui-icon-waiting-circle {
  color: var(--weui-BLUE)
}

.weui-icon-circle {
  color: var(--weui-FG-2)
}

.weui-icon-download {
  color: var(--weui-BRAND)
}

.weui-icon-info-circle {
  color: var(--weui-FG-2)
}

.weui-icon-safe-success {
  color: var(--weui-BRAND)
}

.weui-icon-safe-warn {
  color: var(--weui-YELLOW)
}

.weui-icon-cancel {
  color: var(--weui-RED)
}

.weui-icon-search {
  color: var(--weui-FG-1)
}

.weui-icon-clear {
  color: var(--weui-FG-2)
}

.weui-icon-clear:active {
  color: var(--weui-FG-1)
}

.weui-icon-delete.weui-icon_gallery-delete {
  color: var(--weui-WHITE)
}

.weui-icon-arrow,
.weui-icon-arrow-bold,
.weui-icon-back-arrow,
.weui-icon-back-arrow-thin {
  width: 12px
}

.weui-icon-arrow,
.weui-icon-arrow-bold {
  color: var(--weui-FG-2)
}

.weui-icon-back,
.weui-icon-back-arrow,
.weui-icon-back-arrow-thin,
.weui-icon-back-circle {
  color: var(--weui-FG-0)
}

.weui-icon_msg {
  width: 64px;
  height: 64px
}

.weui-icon_msg.weui-icon-warn {
  color: var(--weui-RED)
}

.weui-icon_msg-primary {
  width: 64px;
  height: 64px
}

.weui-icon_msg-primary.weui-icon-warn {
  color: var(--weui-YELLOW)
}

.weui-link {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0)
}

.weui-link,
.weui-link:visited {
  color: var(--weui-LINK)
}

.weui-btn {
  position: relative;
  display: block;
  width: 184px;
  margin-left: auto;
  margin-right: auto;
  padding: 8px 24px;
  box-sizing: border-box;
  font-weight: 700;
  font-size: 17px;
  text-align: center;
  text-decoration: none;
  color: #fff;
  line-height: 1.41176471;
  border-radius: 4px;
  overflow: hidden;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0)
}

.weui-btn_block {
  width: auto
}

.weui-btn_inline {
  display: inline-block
}

.weui-btn_default {
  background-color: var(--weui-BTN-DEFAULT-BG)
}

.weui-btn_default,
.weui-btn_default:not(.weui-btn_disabled):visited {
  color: var(--weui-BTN-DEFAULT-COLOR)
}

.weui-btn_default:not(.weui-btn_disabled):active {
  background-color: var(--weui-BTN-DEFAULT-ACTIVE-BG)
}

.weui-btn_primary {
  background-color: var(--weui-BRAND)
}

.weui-btn_primary:not(.weui-btn_disabled):visited {
  color: #fff
}

.weui-btn_primary:not(.weui-btn_disabled):active {
  background-color: var(--weui-TAG-TEXT-GREEN)
}

.weui-btn_warn {
  background-color: var(--weui-BTN-DEFAULT-BG)
}

.weui-btn_warn,
.weui-btn_warn:not(.weui-btn_disabled):visited {
  color: var(--weui-RED)
}

.weui-btn_warn:not(.weui-btn_disabled):active {
  background-color: var(--weui-BTN-DEFAULT-ACTIVE-BG)
}

.weui-btn_disabled {
  color: var(--weui-BTN-DISABLED-FONT-COLOR);
  background-color: var(--weui-BTN-DEFAULT-BG)
}

.weui-btn_loading .weui-loading {
  margin: -.2em .34em 0 0
}

.weui-btn_loading.weui-btn_primary {
  background-color: var(--weui-TAG-TEXT-GREEN);
  color: var(--weui-WHITE)
}

.weui-btn_loading.weui-btn_default,
.weui-btn_loading.weui-btn_warn {
  background-color: var(--weui-BTN-DEFAULT-ACTIVE-BG)
}

.weui-btn_cell {
  position: relative;
  display: block;
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
  font-size: 17px;
  text-align: center;
  text-decoration: none;
  color: #fff;
  line-height: 1.41176471;
  padding: 16px;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  overflow: hidden;
  background-color: var(--weui-BG-5)
}

.weui-btn_cell+.weui-btn_cell {
  margin-top: 16px
}

.weui-btn_cell:active {
  background-color: var(--weui-BG-COLOR-ACTIVE)
}

.weui-btn_cell__icon {
  display: inline-block;
  vertical-align: middle;
  width: 24px;
  height: 24px;
  margin: -.2em .34em 0 0
}

.weui-btn_cell-default {
  color: var(--weui-FG-0)
}

.weui-btn_cell-primary {
  color: var(--weui-LINK)
}

.weui-btn_cell-warn {
  color: var(--weui-RED)
}

button.weui-btn,
input.weui-btn {
  border-width: 0;
  outline: 0;
  -webkit-appearance: none
}

button.weui-btn:focus,
input.weui-btn:focus {
  outline: 0
}

button.weui-btn_inline,
button.weui-btn_mini,
input.weui-btn_inline,
input.weui-btn_mini {
  width: auto
}

.weui-btn_mini {
  display: inline-block;
  width: auto;
  padding: 0 .75em;
  line-height: 2;
  font-size: 16px
}

.weui-btn:not(.weui-btn_mini)+.weui-btn:not(.weui-btn_mini) {
  margin-top: 16px
}

.weui-btn.weui-btn_inline+.weui-btn.weui-btn_inline {
  margin-top: auto;
  margin-left: 16px
}

.weui-btn-area {
  margin: 48px 16px 8px
}

.weui-btn-area_inline {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex
}

.weui-btn-area_inline .weui-btn {
  margin-top: auto;
  margin-right: 16px;
  width: 100%;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1
}

.weui-btn-area_inline .weui-btn:last-child {
  margin-right: 0
}

.weui-btn_reset {
  background: transparent;
  border: 0;
  padding: 0;
  outline: 0
}

.weui-btn_icon {
  font-size: 0
}

.weui-btn_icon:active [class*=weui-icon-] {
  color: var(--weui-FG-1)
}

.weui-cells {
  margin-top: 8px;
  background-color: var(--weui-BG-2);
  line-height: 1.41176471;
  font-size: 17px;
  overflow: hidden;
  position: relative
}

.weui-cells:before {
  top: 0;
  border-top: 1px solid var(--weui-FG-3);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(.5);
  transform: scaleY(.5)
}

.weui-cells:after,
.weui-cells:before {
  content: " ";
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  color: var(--weui-FG-3);
  z-index: 2
}

.weui-cells:after {
  bottom: 0;
  border-bottom: 1px solid var(--weui-FG-3);
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(.5);
  transform: scaleY(.5)
}

.weui-cells__title {
  margin-top: 16px;
  margin-bottom: 3px;
  padding-left: 16px;
  padding-right: 16px;
  color: var(--weui-FG-1);
  font-size: 14px;
  line-height: 1.4
}

.weui-cells__title+.weui-cells {
  margin-top: 0
}

.weui-cells__tips {
  margin-top: 8px;
  color: var(--weui-FG-1);
  padding-left: 16px;
  padding-right: 16px;
  font-size: 14px;
  line-height: 1.4
}

.weui-cells__tips a,
.weui-cells__tips navigator {
  color: var(--weui-LINK)
}

.weui-cells__tips navigator {
  display: inline
}

.weui-cell {
  padding: 16px;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center
}

.weui-cell:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid var(--weui-FG-3);
  color: var(--weui-FG-3);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(.5);
  transform: scaleY(.5);
  left: 16px;
  z-index: 2
}

.weui-cell:first-child:before {
  display: none
}

.weui-cell_active:active {
  background-color: var(--weui-BG-COLOR-ACTIVE)
}

.weui-cell_primary {
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
  align-items: flex-start
}

.weui-cell__bd {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1
}

.weui-cell__ft {
  text-align: right;
  color: var(--weui-FG-1)
}

.weui-cell_swiped {
  display: block;
  padding: 0
}

.weui-cell_swiped>.weui-cell__bd {
  position: relative;
  z-index: 1;
  background-color: var(--weui-BG-2)
}

.weui-cell_swiped>.weui-cell__ft {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  color: #fff
}

.weui-swiped-btn {
  display: block;
  padding: 16px 1em;
  line-height: 1.41176471;
  color: inherit
}

.weui-swiped-btn_default {
  background-color: var(--weui-BG-0)
}

.weui-swiped-btn_warn {
  background-color: var(--weui-RED)
}

.weui-cell_access {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  color: inherit
}

.weui-cell_access:active {
  background-color: var(--weui-BG-COLOR-ACTIVE)
}

.weui-cell_access .weui-cell__ft {
  padding-right: 22px;
  position: relative
}

.weui-cell_access .weui-cell__ft:after {
  content: " ";
  width: 12px;
  height: 24px;
  -webkit-mask-position: 0 0;
  mask-position: 0 0;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100%;
  mask-size: 100%;
  background-color: currentColor;
  color: var(--weui-FG-2);
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M2.454%206.58l1.06-1.06%205.78%205.779a.996.996%200%20010%201.413l-5.78%205.779-1.06-1.061%205.425-5.425-5.425-5.424z%22%20fill%3D%22%23B2B2B2%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M2.454%206.58l1.06-1.06%205.78%205.779a.996.996%200%20010%201.413l-5.78%205.779-1.06-1.061%205.425-5.425-5.425-5.424z%22%20fill%3D%22%23B2B2B2%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
  position: absolute;
  top: 50%;
  right: 0;
  margin-top: -12px
}

.weui-cell_link {
  color: var(--weui-LINK);
  font-size: 17px
}

.weui-cell_link:first-child:before {
  display: block
}

.weui-check__label {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0)
}

.weui-check__label:active {
  background-color: var(--weui-BG-COLOR-ACTIVE)
}

.weui-check {
  position: absolute;
  left: -9999px
}

.weui-cells_radio .weui-cell__ft {
  padding-left: 16px;
  font-size: 0
}

.weui-cells_radio .weui-check+.weui-icon-checked {
  min-width: 16px;
  color: transparent
}

.weui-cells_radio .weui-check:checked+.weui-icon-checked,
.weui-cells_radio .weui-check[aria-checked=true]+.weui-icon-checked {
  color: var(--weui-BRAND);
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M8.657%2018.435L3%2012.778l1.414-1.414%204.95%204.95L20.678%205l1.414%201.414-12.02%2012.021a1%201%200%2001-1.415%200z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M8.657%2018.435L3%2012.778l1.414-1.414%204.95%204.95L20.678%205l1.414%201.414-12.02%2012.021a1%201%200%2001-1.415%200z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E)
}

.weui-cells_checkbox .weui-check__label:before {
  left: 55px
}

.weui-cells_checkbox .weui-cell__hd {
  padding-right: 16px;
  font-size: 0
}

.weui-cells_checkbox .weui-icon-checked {
  color: var(--weui-FG-2);
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%221000%22%20height%3D%221000%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M500%20916.667C269.881%20916.667%2083.333%20730.119%2083.333%20500%2083.333%20269.881%20269.881%2083.333%20500%2083.333c230.119%200%20416.667%20186.548%20416.667%20416.667%200%20230.119-186.548%20416.667-416.667%20416.667zm0-50c202.504%200%20366.667-164.163%20366.667-366.667%200-202.504-164.163-366.667-366.667-366.667-202.504%200-366.667%20164.163-366.667%20366.667%200%20202.504%20164.163%20366.667%20366.667%20366.667z%22%20fill-rule%3D%22evenodd%22%20fill-opacity%3D%22.9%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%221000%22%20height%3D%221000%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M500%20916.667C269.881%20916.667%2083.333%20730.119%2083.333%20500%2083.333%20269.881%20269.881%2083.333%20500%2083.333c230.119%200%20416.667%20186.548%20416.667%20416.667%200%20230.119-186.548%20416.667-416.667%20416.667zm0-50c202.504%200%20366.667-164.163%20366.667-366.667%200-202.504-164.163-366.667-366.667-366.667-202.504%200-366.667%20164.163-366.667%20366.667%200%20202.504%20164.163%20366.667%20366.667%20366.667z%22%20fill-rule%3D%22evenodd%22%20fill-opacity%3D%22.9%22%2F%3E%3C%2Fsvg%3E)
}

.weui-cells_checkbox .weui-check:checked+.weui-icon-checked,
.weui-cells_checkbox .weui-check[aria-checked=true]+.weui-icon-checked {
  color: var(--weui-BRAND);
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm-1.177-7.86l-2.765-2.767L7%2012.431l3.119%203.121a1%201%200%20001.414%200l5.952-5.95-1.062-1.062-5.6%205.6z%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm-1.177-7.86l-2.765-2.767L7%2012.431l3.119%203.121a1%201%200%20001.414%200l5.952-5.95-1.062-1.062-5.6%205.6z%22%2F%3E%3C%2Fsvg%3E)
}

.weui-label {
  display: block;
  width: 105px;
  word-wrap: break-word;
  word-break: break-all
}

.weui-input {
  width: 100%;
  border: 0;
  outline: 0;
  -webkit-appearance: none;
  background-color: transparent;
  font-size: inherit;
  color: inherit;
  height: 1.41176471em;
  line-height: 1.41176471
}

.weui-input::-webkit-inner-spin-button,
.weui-input::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0
}

.weui-input:focus:not(:placeholder-shown)+.weui-btn_input-clear {
  display: inline
}

.weui-input::-webkit-input-placeholder,
.weui-input__placeholder {
  color: var(--weui-FG-2)
}

.weui-input::placeholder,
.weui-input__placeholder {
  color: var(--weui-FG-2)
}

.weui-textarea {
  display: block;
  border: 0;
  resize: none;
  background: transparent;
  width: 100%;
  color: inherit;
  font-size: 1em;
  line-height: inherit;
  outline: 0
}

.weui-textarea-counter {
  color: var(--weui-FG-2);
  text-align: right;
  font-size: 14px
}

.weui-cell_warn .weui-textarea-counter {
  color: var(--weui-RED)
}

.weui-cells_form .weui-cell_disabled:active,
.weui-cells_form .weui-cell_readonly:active,
.weui-cells_form .weui-cell_switch:active,
.weui-cells_form .weui-cell_vcode:active {
  background-color: transparent
}

.weui-cells_form .weui-cell__ft {
  font-size: 0
}

.weui-cells_form .weui-icon-warn {
  display: none
}

.weui-cells_form input,
.weui-cells_form label[for],
.weui-cells_form textarea {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0)
}

.weui-cell_warn {
  color: var(--weui-RED)
}

.weui-cell_warn .weui-icon-warn {
  display: inline-block
}

.weui-cell_disabled .weui-input:disabled,
.weui-cell_disabled .weui-textarea:disabled,
.weui-cell_readonly .weui-input:disabled,
.weui-cell_readonly .weui-textarea:disabled {
  opacity: 1;
  -webkit-text-fill-color: var(--weui-FG-1)
}

.weui-cell_disabled .weui-input[disabled],
.weui-cell_disabled .weui-input[readonly],
.weui-cell_disabled .weui-textarea[disabled],
.weui-cell_disabled .weui-textarea[readonly],
.weui-cell_readonly .weui-input[disabled],
.weui-cell_readonly .weui-input[readonly],
.weui-cell_readonly .weui-textarea[disabled],
.weui-cell_readonly .weui-textarea[readonly] {
  color: var(--weui-FG-1)
}

.weui-btn_input-clear {
  display: none;
  padding-left: 8px
}

.weui-btn_input-clear [class*=weui-icon-] {
  width: 18px
}

.weui-form-preview {
  position: relative;
  background-color: var(--weui-BG-2)
}

.weui-form-preview:before {
  top: 0;
  border-top: 1px solid var(--weui-FG-3);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(.5);
  transform: scaleY(.5)
}

.weui-form-preview:after,
.weui-form-preview:before {
  content: " ";
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  color: var(--weui-FG-3)
}

.weui-form-preview:after {
  bottom: 0;
  border-bottom: 1px solid var(--weui-FG-3);
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(.5);
  transform: scaleY(.5)
}

.weui-form-preview__hd {
  position: relative;
  padding: 16px;
  text-align: right;
  line-height: 2.5em
}

.weui-form-preview__hd:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1px solid var(--weui-FG-3);
  color: var(--weui-FG-3);
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(.5);
  transform: scaleY(.5);
  left: 16px
}

.weui-form-preview__hd .weui-form-preview__value {
  font-style: normal;
  font-size: 1.6em
}

.weui-form-preview__bd {
  padding: 16px;
  font-size: .9em;
  text-align: right;
  color: var(--weui-FG-1);
  line-height: 2
}

.weui-form-preview__ft {
  position: relative;
  line-height: 50px;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex
}

.weui-form-preview__ft:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid var(--weui-DIALOG-LINE-COLOR);
  color: var(--weui-DIALOG-LINE-COLOR);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(.5);
  transform: scaleY(.5)
}

.weui-form-preview__item {
  overflow: hidden
}

.weui-form-preview__label {
  float: left;
  margin-right: 1em;
  min-width: 4em;
  color: var(--weui-FG-1);
  text-align: justify;
  text-align-last: justify
}

.weui-form-preview__value {
  display: block;
  overflow: hidden;
  word-break: normal;
  word-wrap: break-word;
  color: var(--weui-FG-0)
}

.weui-form-preview__btn {
  position: relative;
  display: block;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  color: var(--weui-LINK);
  text-align: center;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0)
}

button.weui-form-preview__btn {
  background-color: transparent;
  border: 0;
  outline: 0;
  line-height: inherit;
  font-size: inherit
}

.weui-form-preview__btn:active {
  background-color: var(--weui-BG-COLOR-ACTIVE)
}

.weui-form-preview__btn:after {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  width: 1px;
  bottom: 0;
  border-left: 1px solid var(--weui-DIALOG-LINE-COLOR);
  color: var(--weui-DIALOG-LINE-COLOR);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleX(.5);
  transform: scaleX(.5)
}

.weui-form-preview__btn:first-child:after {
  display: none
}

.weui-form-preview__btn_default {
  color: var(--weui-FG-HALF)
}

.weui-form-preview__btn_primary {
  color: var(--weui-LINK)
}

.weui-cell_select {
  padding: 0
}

.weui-cell_select .weui-select {
  padding-right: 30px
}

.weui-cell_select .weui-cell__bd:after {
  content: " ";
  width: 12px;
  height: 24px;
  -webkit-mask-position: 0 0;
  mask-position: 0 0;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100%;
  mask-size: 100%;
  background-color: currentColor;
  color: var(--weui-FG-2);
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M2.454%206.58l1.06-1.06%205.78%205.779a.996.996%200%20010%201.413l-5.78%205.779-1.06-1.061%205.425-5.425-5.425-5.424z%22%20fill%3D%22%23B2B2B2%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M2.454%206.58l1.06-1.06%205.78%205.779a.996.996%200%20010%201.413l-5.78%205.779-1.06-1.061%205.425-5.425-5.425-5.424z%22%20fill%3D%22%23B2B2B2%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
  position: absolute;
  top: 50%;
  right: 16px;
  margin-top: -12px
}

.weui-select {
  -webkit-appearance: none;
  border: 0;
  outline: 0;
  background-color: transparent;
  width: 100%;
  font-size: inherit;
  height: 56px;
  line-height: 56px;
  position: relative;
  z-index: 1;
  padding-left: 16px;
  color: var(--weui-FG-0)
}

.weui-cell_select-before {
  padding-right: 16px
}

.weui-cell_select-before .weui-select {
  width: 105px;
  box-sizing: border-box
}

.weui-cell_select-before .weui-cell__hd {
  position: relative
}

.weui-cell_select-before .weui-cell__hd:after {
  content: " ";
  position: absolute;
  right: 0;
  top: 0;
  width: 1px;
  bottom: 0;
  border-right: 1px solid var(--weui-FG-3);
  color: var(--weui-FG-3);
  -webkit-transform-origin: 100% 0;
  transform-origin: 100% 0;
  -webkit-transform: scaleX(.5);
  transform: scaleX(.5)
}

.weui-cell_select-before .weui-cell__hd:before {
  content: " ";
  width: 12px;
  height: 24px;
  -webkit-mask-position: 0 0;
  mask-position: 0 0;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100%;
  mask-size: 100%;
  background-color: currentColor;
  color: var(--weui-FG-2);
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M2.454%206.58l1.06-1.06%205.78%205.779a.996.996%200%20010%201.413l-5.78%205.779-1.06-1.061%205.425-5.425-5.425-5.424z%22%20fill%3D%22%23B2B2B2%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M2.454%206.58l1.06-1.06%205.78%205.779a.996.996%200%20010%201.413l-5.78%205.779-1.06-1.061%205.425-5.425-5.425-5.424z%22%20fill%3D%22%23B2B2B2%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
  position: absolute;
  top: 50%;
  right: 16px;
  margin-top: -12px
}

.weui-cell_select-before .weui-cell__bd {
  padding-left: 16px
}

.weui-cell_select-before .weui-cell__bd:after {
  display: none
}

.weui-cell_select-before.weui-cell_access .weui-cell__hd {
  line-height: 56px;
  padding-left: 32px
}

.weui-cell_select-after {
  padding-left: 16px
}

.weui-cell_select-after .weui-select {
  padding-left: 0
}

.weui-cell_select-after.weui-cell_access .weui-cell__bd {
  line-height: 56px
}

.weui-cell_vcode {
  padding-top: 0;
  padding-right: 0;
  padding-bottom: 0
}

.weui-vcode-btn,
.weui-vcode-img {
  margin-left: 5px;
  height: 56px;
  vertical-align: middle
}

.weui-vcode-btn {
  display: inline-block;
  padding: 0 .6em 0 .7em;
  line-height: 56px;
  font-size: 17px;
  color: var(--weui-LINK);
  position: relative
}

.weui-vcode-btn:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  width: 1px;
  bottom: 0;
  border-left: 1px solid var(--weui-FG-3);
  color: var(--weui-FG-3);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleX(.5);
  transform: scaleX(.5)
}

button.weui-vcode-btn {
  background-color: transparent;
  border: 0;
  outline: 0
}

.weui-vcode-btn:active {
  color: #767676
}

.weui-gallery {
  display: none;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: #000;
  z-index: 1000
}

.weui-gallery__img,
.weui-gallery__opr {
  position: absolute;
  left: 0;
  left: constant(safe-area-inset-left);
  left: env(safe-area-inset-left);
  right: 0;
  right: constant(safe-area-inset-right);
  right: env(safe-area-inset-right)
}

.weui-gallery__img {
  top: 0;
  top: constant(safe-area-inset-top);
  top: env(safe-area-inset-top);
  bottom: 60px;
  bottom: calc(60px + constant(safe-area-inset-bottom));
  bottom: calc(60px + env(safe-area-inset-bottom));
  background: 50% no-repeat;
  background-size: contain
}

.weui-gallery__opr {
  position: absolute;
  bottom: 0;
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background-color: #0d0d0d;
  color: var(--weui-WHITE);
  line-height: 60px;
  text-align: center
}

.weui-gallery__del {
  display: block
}

.weui-cell_switch {
  padding-top: 12px;
  padding-bottom: 12px
}

.weui-switch {
  -webkit-appearance: none;
  appearance: none
}

.weui-switch,
.weui-switch-cp__box {
  position: relative;
  width: 52px;
  height: 32px;
  border: 2px solid var(--weui-FG-3);
  outline: 0;
  border-radius: 16px;
  box-sizing: border-box;
  -webkit-transition: background-color .1s, border .1s;
  transition: background-color .1s, border .1s
}

.weui-switch-cp__box:before,
.weui-switch:before {
  content: " ";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  border-radius: 15px;
  background-color: var(--weui-BG-3);
  -webkit-transition: -webkit-transform .35s cubic-bezier(.45, 1, .4, 1);
  transition: -webkit-transform .35s cubic-bezier(.45, 1, .4, 1);
  transition: transform .35s cubic-bezier(.45, 1, .4, 1);
  transition: transform .35s cubic-bezier(.45, 1, .4, 1), -webkit-transform .35s cubic-bezier(.45, 1, .4, 1)
}

.weui-switch-cp__box:after,
.weui-switch:after {
  content: " ";
  position: absolute;
  top: 0;
  left: 0;
  width: 28px;
  height: 28px;
  border-radius: 15px;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, .4);
  -webkit-transition: -webkit-transform .35s cubic-bezier(.4, .4, .25, 1.35);
  transition: -webkit-transform .35s cubic-bezier(.4, .4, .25, 1.35);
  transition: transform .35s cubic-bezier(.4, .4, .25, 1.35);
  transition: transform .35s cubic-bezier(.4, .4, .25, 1.35), -webkit-transform .35s cubic-bezier(.4, .4, .25, 1.35)
}

.weui-switch-cp__input:checked+.weui-switch-cp__box,
.weui-switch-cp__input[aria-checked=true]+.weui-switch-cp__box,
.weui-switch:checked {
  border-color: var(--weui-BRAND);
  background-color: var(--weui-BRAND)
}

.weui-switch-cp__input:checked+.weui-switch-cp__box:before,
.weui-switch-cp__input[aria-checked=true]+.weui-switch-cp__box:before,
.weui-switch:checked:before {
  -webkit-transform: scale(0);
  transform: scale(0)
}

.weui-switch-cp__input:checked+.weui-switch-cp__box:after,
.weui-switch-cp__input[aria-checked=true]+.weui-switch-cp__box:after,
.weui-switch:checked:after {
  -webkit-transform: translateX(20px);
  transform: translateX(20px)
}

.weui-switch-cp__input {
  position: absolute;
  left: -9999px
}

.weui-switch-cp__box {
  display: block
}

.weui-uploader {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1
}

.weui-uploader__hd {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  padding-bottom: 16px;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center
}

.weui-uploader__title {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1
}

.weui-uploader__info {
  color: var(--weui-FG-2)
}

.weui-uploader__bd {
  margin-bottom: -8px;
  margin-right: -8px;
  overflow: hidden
}

.weui-uploader__files {
  list-style: none
}

.weui-uploader__file {
  float: left;
  margin-right: 8px;
  margin-bottom: 8px;
  width: 96px;
  height: 96px;
  background: no-repeat 50%;
  background-size: cover
}

.weui-uploader__file_status {
  position: relative
}

.weui-uploader__file_status:before {
  content: " ";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, .5)
}

.weui-uploader__file_status .weui-uploader__file-content {
  display: block
}

.weui-uploader__file-content {
  display: none;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  color: var(--weui-WHITE)
}

.weui-uploader__file-content .weui-icon-warn {
  display: inline-block
}

.weui-uploader__input-box {
  float: left;
  position: relative;
  margin-right: 8px;
  margin-bottom: 8px;
  width: 96px;
  height: 96px;
  box-sizing: border-box;
  background-color: #ededed
}

@media(prefers-color-scheme: dark) {
  .weui-uploader__input-box {
    background-color: #2e2e2e
  }
}

.weui-uploader__input-box:after,
.weui-uploader__input-box:before {
  content: " ";
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  background-color: #a3a3a3
}

@media(prefers-color-scheme: dark) {
  .weui-uploader__input-box:after,
  .weui-uploader__input-box:before {
    background-color: #6d6d6d
  }
}

.weui-uploader__input-box:before {
  width: 2px;
  height: 32px
}

.weui-uploader__input-box:after {
  width: 32px;
  height: 2px
}

.weui-uploader__input-box:active:after,
.weui-uploader__input-box:active:before {
  opacity: .7
}

.weui-uploader__input {
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0)
}

.weui-msg {
  padding-top: 48px;
  padding: calc(48px + constant(safe-area-inset-top)) constant(safe-area-inset-right) constant(safe-area-inset-bottom) constant(safe-area-inset-left);
  padding: calc(48px + env(safe-area-inset-top)) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
  text-align: center;
  line-height: 1.4;
  min-height: 100%;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  flex-direction: column;
  background-color: var(--weui-BG-2)
}

.weui-msg a:not(.weui-btn) {
  color: var(--weui-LINK);
  display: inline-block;
  vertical-align: baseline
}

.weui-msg__icon-area {
  margin-bottom: 32px
}

.weui-msg__text-area {
  margin-bottom: 32px;
  padding: 0 32px;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  line-height: 1.6
}

.weui-msg__text-area:first-child {
  padding-top: 96px
}

.weui-msg__title {
  font-weight: 700;
  font-size: 22px
}

.weui-msg__desc,
.weui-msg__title {
  margin-bottom: 16px;
  color: var(--weui-FG-0);
  word-wrap: break-word;
  word-break: break-all
}

.weui-msg__desc {
  font-size: 17px
}

.weui-msg__desc-primary {
  font-size: 14px;
  color: var(--weui-FG-1);
  word-wrap: break-word;
  word-break: break-all;
  margin-bottom: 16px
}

.weui-msg__opr-area {
  margin-bottom: 16px
}

.weui-msg__opr-area .weui-btn-area {
  margin: 0
}

.weui-msg__opr-area .weui-btn+.weui-btn {
  margin-bottom: 16px
}

.weui-msg__opr-area:last-child {
  margin-bottom: 96px
}

.weui-msg__opr-area+.weui-msg__extra-area {
  margin-top: 48px
}

.weui-msg__tips-area {
  margin-bottom: 16px;
  padding: 0 40px
}

.weui-msg__opr-area+.weui-msg__tips-area {
  margin-bottom: 48px
}

.weui-msg__tips-area:last-child {
  margin-bottom: 64px
}

.weui-msg__extra-area,
.weui-msg__tips {
  font-size: 12px;
  color: var(--weui-FG-1)
}

.weui-msg__extra-area {
  margin-bottom: 24px
}

.weui-msg__extra-area a,
.weui-msg__extra-area navigator {
  color: var(--weui-LINK)
}

.weui-msg__extra-area navigator {
  display: inline
}

.weui-cells__group_form:first-child .weui-cells__title {
  margin-top: 0
}

.weui-cells__group_form .weui-cells__title {
  margin-top: 24px;
  margin-bottom: 8px;
  padding: 0 32px
}

.weui-cells__group_form .weui-cell:before,
.weui-cells__group_form .weui-cells:before {
  left: 32px;
  right: 32px
}

.weui-cells__group_form .weui-cells_checkbox .weui-check__label:before {
  left: 72px
}

.weui-cells__group_form .weui-cells:after {
  left: 32px;
  right: 32px
}

.weui-cells__group_form .weui-cell {
  padding: 16px 32px
}

.weui-cells__group_form .weui-cell:not(.weui-cell_link) {
  color: var(--weui-FG-0)
}

.weui-cells__group_form .weui-cell__hd {
  padding-right: 16px
}

.weui-cells__group_form .weui-cell__ft {
  padding-left: 16px
}

.weui-cells__group_form .weui-cell_warn input {
  color: var(--weui-RED)
}

.weui-cells__group_form .weui-label {
  max-width: 5em;
  margin-right: 8px
}

.weui-cells__group_form .weui-cells__tips {
  margin-top: 8px;
  padding: 0 32px;
  color: rgba(0, 0, 0, .3)
}

.weui-cells__group_form .weui-cells__tips a {
  font-weight: 700
}

.weui-cells__group_form .weui-cell_vcode {
  padding: 12px 32px
}

.weui-cells__group_form .weui-vcode-btn {
  font-size: 16px;
  padding: 0 12px;
  margin-left: 0;
  height: auto;
  width: auto;
  line-height: 2em;
  color: var(--weui-BTN-DEFAULT-COLOR);
  background-color: var(--weui-BTN-DEFAULT-BG)
}

.weui-cells__group_form .weui-vcode-btn:before {
  display: none
}

.weui-cells__group_form .weui-cell_select {
  padding: 0
}

.weui-cells__group_form .weui-cell_select .weui-select {
  padding: 0 32px
}

.weui-cells__group_form .weui-cell_select .weui-cell__bd:after {
  right: 32px
}

.weui-cells__group_form .weui-cell_select-before .weui-label {
  margin-right: 24px
}

.weui-cells__group_form .weui-cell_select-before .weui-select {
  padding-right: 24px;
  box-sizing: initial
}

.weui-cells__group_form .weui-cell_select-after {
  padding-left: 32px
}

.weui-cells__group_form .weui-cell_select-after .weui-select {
  padding-left: 0
}

.weui-cells__group_form .weui-cell_switch {
  padding: 12px 32px
}

.weui-form {
  padding: 56px 0 0;
  padding: calc(56px + constant(safe-area-inset-top)) constant(safe-area-inset-right) constant(safe-area-inset-bottom) constant(safe-area-inset-left);
  padding: calc(56px + env(safe-area-inset-top)) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  flex-direction: column;
  line-height: 1.4;
  min-height: 100%;
  box-sizing: border-box;
  background-color: var(--weui-BG-2)
}

.weui-form .weui-footer,
.weui-form .weui-footer__link {
  font-size: 14px
}

.weui-form .weui-agree {
  padding: 0
}

.weui-form__text-area {
  padding: 0 32px;
  color: var(--weui-FG-0);
  text-align: center
}

.weui-form__control-area {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  margin: 48px 0
}

.weui-form__tips-area {
  overflow: hidden
}

.weui-form__extra-area,
.weui-form__tips-area {
  margin-bottom: 24px;
  text-align: center
}

.weui-form__opr-area {
  margin-bottom: 64px
}

.weui-form__opr-area:last-child {
  margin-bottom: 96px
}

.weui-form__title {
  font-size: 22px;
  font-weight: 700;
  line-height: 1.36
}

.weui-form__desc {
  font-size: 17px;
  margin-top: 16px
}

.weui-form__tips {
  color: var(--weui-FG-1);
  font-size: 14px
}

.weui-form__tips a,
.weui-form__tips navigator {
  color: var(--weui-LINK)
}

.weui-form__tips navigator {
  display: inline
}

.weui-article {
  padding: 24px 16px;
  padding: 24px calc(16px + constant(safe-area-inset-right)) calc(24px + constant(safe-area-inset-bottom)) calc(16px + constant(safe-area-inset-left));
  padding: 24px calc(16px + env(safe-area-inset-right)) calc(24px + env(safe-area-inset-bottom)) calc(16px + env(safe-area-inset-left));
  font-size: 17px;
  color: var(--weui-FG-0)
}

.weui-article__section {
  margin-bottom: 1.5em
}

.weui-article__h1 {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: .9em;
  line-height: 1.4
}

.weui-article__h2 {
  font-size: 17px
}

.weui-article__h2,
.weui-article__h3 {
  font-weight: 700;
  margin-bottom: .34em;
  line-height: 1.4
}

.weui-article__h3 {
  font-size: 15px
}

.weui-article__p {
  margin: 0 0 .8em
}

.weui-tabbar {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  position: relative;
  z-index: 500;
  background-color: var(--weui-BG-1)
}

.weui-tabbar:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid var(--weui-FG-3);
  color: var(--weui-FG-3);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(.5);
  transform: scaleY(.5)
}

.weui-tabbar__item {
  display: block;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  padding: 8px 0;
  padding-bottom: calc(8px + constant(safe-area-inset-bottom));
  padding-bottom: calc(8px + env(safe-area-inset-bottom));
  font-size: 0;
  color: var(--weui-FG-1);
  text-align: center;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0)
}

.weui-tabbar__item:first-child {
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left)
}

.weui-tabbar__item:last-child {
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right)
}

.weui-tabbar__item.weui-bar__item_on .weui-tabbar__icon,
.weui-tabbar__item.weui-bar__item_on .weui-tabbar__icon>i,
.weui-tabbar__item.weui-bar__item_on .weui-tabbar__label {
  color: var(--weui-BRAND)
}

.weui-tabbar__icon {
  display: inline-block;
  width: 28px;
  height: 28px;
  margin-bottom: 2px
}

.weui-tabbar__icon>i,
i.weui-tabbar__icon {
  font-size: 24px;
  color: var(--weui-FG-1)
}

.weui-tabbar__icon img {
  width: 100%;
  height: 100%
}

.weui-tabbar__label {
  color: var(--weui-FG-0);
  font-size: 10px;
  line-height: 1.4
}

.weui-navbar {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  position: relative;
  z-index: 500;
  background-color: var(--weui-BG-2);
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top)
}

.weui-navbar:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1px solid var(--weui-FG-3);
  color: var(--weui-FG-3);
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(.5);
  transform: scaleY(.5)
}

.weui-navbar+.weui-tab__panel {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom)
}

.weui-navbar__item {
  position: relative;
  display: block;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  padding: 16px 0;
  padding-top: calc(16px + constant(safe-area-inset-top));
  padding-top: calc(16px + env(safe-area-inset-top));
  text-align: center;
  font-size: 17px;
  line-height: 1.41176471;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0)
}

.weui-navbar__item.weui-bar__item_on,
.weui-navbar__item:active {
  background-color: var(--weui-BG-COLOR-ACTIVE)
}

.weui-navbar__item:after {
  content: " ";
  position: absolute;
  right: 0;
  top: 0;
  width: 1px;
  bottom: 0;
  border-right: 1px solid var(--weui-FG-3);
  color: var(--weui-FG-3);
  -webkit-transform-origin: 100% 0;
  transform-origin: 100% 0;
  -webkit-transform: scaleX(.5);
  transform: scaleX(.5)
}

.weui-navbar__item:first-child {
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left)
}

.weui-navbar__item:last-child {
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right)
}

.weui-navbar__item:last-child:after {
  display: none
}

.weui-tab {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  height: 100%;
  box-sizing: border-box;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  flex-direction: column
}

.weui-tab__panel {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  overflow: auto;
  -webkit-overflow-scrolling: touch
}

.weui-tab__content {
  display: none
}

.weui-progress {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center
}

.weui-progress__bar {
  background-color: var(--weui-BG-0);
  height: 3px;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1
}

.weui-progress__inner-bar {
  width: 0;
  height: 100%;
  background-color: var(--weui-BRAND)
}

.weui-progress__opr {
  display: block;
  margin-left: 15px;
  font-size: 0
}

.weui-panel {
  background-color: var(--weui-BG-2);
  margin-top: 10px;
  position: relative;
  overflow: hidden
}

.weui-panel:first-child {
  margin-top: 0
}

.weui-panel:before {
  top: 0;
  border-top: 1px solid var(--weui-FG-3);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(.5);
  transform: scaleY(.5)
}

.weui-panel:after,
.weui-panel:before {
  content: " ";
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  color: var(--weui-FG-3)
}

.weui-panel:after {
  bottom: 0;
  border-bottom: 1px solid var(--weui-FG-3);
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(.5);
  transform: scaleY(.5)
}

.weui-panel__hd {
  padding: 16px 16px 13px;
  color: var(--weui-FG-0);
  font-size: 15px;
  font-weight: 700;
  position: relative
}

.weui-panel__hd:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1px solid var(--weui-FG-3);
  color: var(--weui-FG-3);
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(.5);
  transform: scaleY(.5);
  left: 15px
}

.weui-media-box {
  padding: 16px;
  position: relative
}

.weui-media-box:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid var(--weui-FG-3);
  color: var(--weui-FG-3);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(.5);
  transform: scaleY(.5);
  left: 16px
}

.weui-media-box:first-child:before {
  display: none
}

a.weui-media-box {
  color: #000;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0)
}

a.weui-media-box:active {
  background-color: var(--weui-BG-COLOR-ACTIVE)
}

.weui-media-box__title {
  font-weight: 400;
  font-size: 17px;
  color: var(--weui-FG-0);
  width: auto;
  white-space: nowrap;
  word-wrap: normal;
  word-wrap: break-word;
  word-break: break-all
}

.weui-media-box__desc,
.weui-media-box__title {
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis
}

.weui-media-box__desc {
  color: var(--weui-FG-2);
  font-size: 14px;
  padding-top: 4px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2
}

.weui-media-box__info {
  margin-top: 16px;
  padding-bottom: 4px;
  font-size: 13px;
  color: var(--weui-FG-2);
  line-height: 1em;
  list-style: none;
  overflow: hidden
}

.weui-media-box__info__meta {
  float: left;
  padding-right: 1em
}

.weui-media-box__info__meta_extra {
  padding-left: 1em;
  border-left: 1px solid var(--weui-FG-2)
}

.weui-media-box_appmsg {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center
}

.weui-media-box_appmsg .weui-media-box__hd {
  margin-right: 16px;
  width: 60px;
  height: 60px;
  line-height: 60px;
  text-align: center
}

.weui-media-box_appmsg .weui-media-box__thumb {
  width: 100%;
  max-height: 100%;
  vertical-align: top
}

.weui-media-box_appmsg .weui-media-box__bd {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  min-width: 0
}

.weui-media-box_small-appmsg {
  padding: 0
}

.weui-media-box_small-appmsg .weui-cells {
  margin-top: 0
}

.weui-media-box_small-appmsg .weui-cells:before {
  display: none
}

.weui-grids {
  position: relative;
  overflow: hidden
}

.weui-grids:before {
  right: 0;
  height: 1px;
  border-top: 1px solid var(--weui-FG-3);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(.5);
  transform: scaleY(.5)
}

.weui-grids:after,
.weui-grids:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  color: var(--weui-FG-3)
}

.weui-grids:after {
  width: 1px;
  bottom: 0;
  border-left: 1px solid var(--weui-FG-3);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleX(.5);
  transform: scaleX(.5)
}

.weui-grid {
  position: relative;
  float: left;
  padding: 20px 10px;
  width: 33.33333333%;
  box-sizing: border-box
}

.weui-grid:before {
  top: 0;
  width: 1px;
  border-right: 1px solid var(--weui-FG-3);
  -webkit-transform-origin: 100% 0;
  transform-origin: 100% 0;
  -webkit-transform: scaleX(.5);
  transform: scaleX(.5)
}

.weui-grid:after,
.weui-grid:before {
  content: " ";
  position: absolute;
  right: 0;
  bottom: 0;
  color: var(--weui-FG-3)
}

.weui-grid:after {
  left: 0;
  height: 1px;
  border-bottom: 1px solid var(--weui-FG-3);
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(.5);
  transform: scaleY(.5)
}

.weui-grid:active {
  background-color: var(--weui-BG-COLOR-ACTIVE)
}

.weui-grid__icon {
  width: 28px;
  height: 28px;
  margin: 0 auto
}

.weui-grid__icon img {
  display: block;
  width: 100%;
  height: 100%
}

.weui-grid__icon+.weui-grid__label {
  margin-top: 4px
}

.weui-grid__label {
  display: block;
  color: var(--weui-FG-0);
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden
}

.weui-footer,
.weui-grid__label {
  text-align: center;
  font-size: 14px
}

.weui-footer {
  color: var(--weui-FG-2);
  line-height: 1.4
}

.weui-footer a,
.weui-footer navigator {
  color: var(--weui-LINK)
}

.weui-footer navigator {
  display: inline
}

.weui-footer_fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding-top: 16px;
  padding-bottom: 16px;
  padding-bottom: calc(16px + constant(safe-area-inset-bottom));
  padding-bottom: calc(16px + env(safe-area-inset-bottom));
  left: constant(safe-area-inset-left);
  left: env(safe-area-inset-left);
  right: constant(safe-area-inset-right);
  right: env(safe-area-inset-right)
}

.weui-footer__links {
  font-size: 0
}

.weui-footer__link {
  display: inline-block;
  vertical-align: top;
  margin: 0 8px;
  position: relative;
  font-size: 14px
}

.weui-footer__link:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  width: 1px;
  bottom: 0;
  border-left: 1px solid var(--weui-FG-3);
  color: var(--weui-FG-3);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleX(.5);
  transform: scaleX(.5);
  left: -8px;
  top: .36em;
  bottom: .36em
}

.weui-footer__link:first-child:before {
  display: none
}

.weui-footer__text {
  padding: 0 16px;
  font-size: 12px
}

.weui-flex {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex
}

.weui-flex__item {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1
}

.weui-dialog {
  position: fixed;
  z-index: 5000;
  top: 50%;
  left: 16px;
  right: 16px;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  background-color: var(--weui-BG-2);
  text-align: center;
  border-radius: 12px;
  overflow: hidden;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-direction: column;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  flex-direction: column;
  max-height: 90%
}

.weui-dialog__hd {
  padding: 32px 24px 16px
}

.weui-dialog__title {
  font-weight: 700;
  font-size: 17px;
  line-height: 1.4
}

.weui-dialog__bd {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 0 24px;
  margin-bottom: 32px;
  font-size: 17px;
  line-height: 1.4;
  word-wrap: break-word;
  -webkit-hyphens: auto;
  hyphens: auto;
  color: var(--weui-FG-1)
}

.weui-dialog__bd:first-child {
  min-height: 40px;
  padding: 32px 24px 0;
  font-weight: 700;
  color: var(--weui-FG-0);
  -webkit-flex-direction: column;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  justify-content: center
}

.weui-dialog__bd:first-child,
.weui-dialog__ft {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex
}

.weui-dialog__ft {
  position: relative;
  line-height: 56px;
  min-height: 56px;
  font-size: 17px
}

.weui-dialog__ft:after {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid var(--weui-DIALOG-LINE-COLOR);
  color: var(--weui-DIALOG-LINE-COLOR);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(.5);
  transform: scaleY(.5)
}

.weui-dialog__btn {
  display: block;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  color: var(--weui-LINK);
  font-weight: 700;
  text-decoration: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  position: relative
}

.weui-dialog__btn:active {
  background-color: var(--weui-BG-COLOR-ACTIVE)
}

.weui-dialog__btn:after {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  width: 1px;
  bottom: 0;
  border-left: 1px solid var(--weui-DIALOG-LINE-COLOR);
  color: var(--weui-DIALOG-LINE-COLOR);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleX(.5);
  transform: scaleX(.5)
}

.weui-dialog__btn:first-child:after {
  display: none
}

.weui-dialog__btn_default {
  color: var(--weui-FG-HALF)
}

.weui-skin_android .weui-dialog {
  text-align: left;
  box-shadow: 0 6px 30px 0 rgba(0, 0, 0, .1)
}

.weui-skin_android .weui-dialog__title {
  font-size: 22px;
  line-height: 1.4
}

.weui-skin_android .weui-dialog__hd {
  text-align: left
}

.weui-skin_android .weui-dialog__bd {
  color: var(--weui-FG-1);
  text-align: left
}

.weui-skin_android .weui-dialog__bd:first-child {
  color: var(--weui-FG-0)
}

.weui-skin_android .weui-dialog__ft {
  display: block;
  text-align: right;
  line-height: 40px;
  min-height: 40px;
  padding: 0 24px 16px
}

.weui-skin_android .weui-dialog__ft:after {
  display: none
}

.weui-skin_android .weui-dialog__btn {
  display: inline-block;
  vertical-align: top;
  padding: 0 .8em
}

.weui-skin_android .weui-dialog__btn:after {
  display: none
}

.weui-skin_android .weui-dialog__btn:last-child {
  margin-right: -.8em
}

.weui-skin_android .weui-dialog__btn_default {
  color: var(--weui-FG-HALF)
}

@media screen and (min-width:352px) {
  .weui-dialog {
    width: 320px;
    margin: 0 auto
  }
}

.weui-half-screen-dialog {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  max-height: 75%;
  z-index: 5000;
  line-height: 1.4;
  background-color: var(--weui-BG-2);
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  overflow: hidden;
  padding: 0 24px;
  padding: 0 calc(24px + constant(safe-area-inset-right)) constant(safe-area-inset-bottom) calc(24px + constant(safe-area-inset-left));
  padding: 0 calc(24px + env(safe-area-inset-right)) env(safe-area-inset-bottom) calc(24px + env(safe-area-inset-left))
}

@media only screen and (max-height:558px) {
  .weui-half-screen-dialog {
    max-height: none
  }
}

.weui-half-screen-dialog__hd {
  font-size: 8px;
  height: 8em;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center
}

.weui-half-screen-dialog__hd .weui-icon-btn {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%)
}

.weui-half-screen-dialog__hd .weui-icon-btn:active {
  opacity: .5
}

.weui-half-screen-dialog__hd__side {
  position: relative;
  left: -8px
}

.weui-half-screen-dialog__hd__main {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1
}

.weui-half-screen-dialog__hd__side+.weui-half-screen-dialog__hd__main {
  text-align: center;
  padding: 0 40px
}

.weui-half-screen-dialog__hd__main+.weui-half-screen-dialog__hd__side {
  right: -8px;
  left: auto
}

.weui-half-screen-dialog__hd__main+.weui-half-screen-dialog__hd__side .weui-icon-btn {
  right: 0
}

.weui-half-screen-dialog__title {
  display: block;
  color: var(--weui-FG-0);
  font-weight: 700;
  font-size: 15px
}

.weui-half-screen-dialog__subtitle {
  display: block;
  color: var(--weui-FG-1);
  font-size: 10px
}

. {
  word-wrap: break-word;
  -webkit-hyphens: auto;
  hyphens: auto;
  overflow-y: auto;
  padding-top: 4px;
  padding-bottom: 40px;
  font-size: 14px;
  color: var(--weui-FG-0)
}

.weui-half-screen-dialog__desc {
  font-size: 17px;
  font-weight: 700;
  color: var(--weui-FG-0);
  line-height: 1.4
}

.weui-half-screen-dialog__tips {
  padding-top: 16px;
  font-size: 14px;
  color: var(--weui-FG-2);
  line-height: 1.4
}

.weui-half-screen-dialog__ft {
  padding: 0 24px 32px;
  text-align: center
}

.weui-half-screen-dialog__ft .weui-btn:nth-last-child(n+2),
.weui-half-screen-dialog__ft .weui-btn:nth-last-child(n+2)+.weui-btn {
  display: inline-block;
  vertical-align: top;
  margin: 0 8px;
  width: 120px
}

.weui-icon-btn {
  outline: 0;
  -webkit-appearance: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  border-width: 0;
  background-color: transparent;
  color: var(--weui-FG-0);
  font-size: 0
}

.weui-icon-more {
  width: 24px;
  -webkit-mask: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E  %3Cpath fill-opacity='.9' fill-rule='evenodd' d='M5 10.25a1.75 1.75 0 1 1 0 3.5 1.75 1.75 0 0 1 0-3.5zm7 0a1.75 1.75 0 1 1 0 3.5 1.75 1.75 0 0 1 0-3.5zm7 0a1.75 1.75 0 1 1 0 3.5 1.75 1.75 0 0 1 0-3.5z'/%3E%3C/svg%3E") no-repeat 50% 50%;
  mask: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E  %3Cpath fill-opacity='.9' fill-rule='evenodd' d='M5 10.25a1.75 1.75 0 1 1 0 3.5 1.75 1.75 0 0 1 0-3.5zm7 0a1.75 1.75 0 1 1 0 3.5 1.75 1.75 0 0 1 0-3.5zm7 0a1.75 1.75 0 1 1 0 3.5 1.75 1.75 0 0 1 0-3.5z'/%3E%3C/svg%3E") no-repeat 50% 50%;
  -webkit-mask-size: cover;
  mask-size: cover
}

.weui-icon-btn_goback,
.weui-icon-more {
  display: inline-block;
  vertical-align: middle;
  height: 24px;
  background-color: currentColor;
  color: var(--weui-FG-0)
}

.weui-icon-btn_goback {
  width: 12px;
  -webkit-mask: url("data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M10%2019.438L8.955%2020.5l-7.666-7.79a1.02%201.02%200%20010-1.42L8.955%203.5%2010%204.563%202.682%2012%2010%2019.438z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E") no-repeat 50% 50%;
  mask: url("data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M10%2019.438L8.955%2020.5l-7.666-7.79a1.02%201.02%200%20010-1.42L8.955%203.5%2010%204.563%202.682%2012%2010%2019.438z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E") no-repeat 50% 50%;
  -webkit-mask-size: cover;
  mask-size: cover
}

.weui-icon-btn_close {
  color: var(--weui-FG-0);
  display: inline-block;
  vertical-align: middle;
  width: 14px;
  height: 24px;
  -webkit-mask: url("data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12.25%2010.693L6.057%204.5%205%205.557l6.193%206.193L5%2017.943%206.057%2019l6.193-6.193L18.443%2019l1.057-1.057-6.193-6.193L19.5%205.557%2018.443%204.5z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E") no-repeat 50% 50%;
  mask: url("data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12.25%2010.693L6.057%204.5%205%205.557l6.193%206.193L5%2017.943%206.057%2019l6.193-6.193L18.443%2019l1.057-1.057-6.193-6.193L19.5%205.557%2018.443%204.5z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E") no-repeat 50% 50%;
  -webkit-mask-size: cover;
  mask-size: cover;
  background-color: currentColor
}

.weui-toast {
  position: fixed;
  z-index: 5000;
  width: 120px;
  height: 120px;
  top: 40%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  text-align: center;
  border-radius: 5px;
  color: hsla(0, 0%, 100%, .9);
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  background-color: #4c4c4c
}

@media(prefers-color-scheme: dark) {
  .weui-toast {
    background-color: #606060
  }
}

.weui-icon_toast {
  display: block
}

.weui-icon_toast.weui-icon-success-no-circle {
  color: hsla(0, 0%, 100%, .9);
  width: 55px;
  height: 55px
}

.weui-icon_toast.weui-loading {
  margin: 8px 0;
  width: 38px;
  height: 38px;
  vertical-align: baseline
}

.weui-toast__content {
  font-size: 14px
}

.weui-mask {
  background: rgba(0, 0, 0, .6)
}

.weui-mask,
.weui-mask_transparent {
  position: fixed;
  z-index: 1000;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0
}

.weui-actionsheet {
  position: fixed;
  left: 0;
  bottom: 0;
  -webkit-transform: translateY(100%);
  transform: translateY(100%);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  z-index: 5000;
  width: 100%;
  background-color: var(--weui-BG-1);
  -webkit-transition: -webkit-transform .3s;
  transition: -webkit-transform .3s;
  transition: transform .3s;
  transition: transform .3s, -webkit-transform .3s;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  overflow: hidden
}

.weui-actionsheet__title {
  position: relative;
  height: 56px;
  padding: 0 24px;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  flex-direction: column;
  text-align: center;
  font-size: 12px;
  color: var(--weui-FG-1);
  line-height: 1.4;
  background: var(--weui-BG-2)
}

.weui-actionsheet__title:before {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1px solid var(--weui-FG-3);
  color: var(--weui-FG-3);
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(.5);
  transform: scaleY(.5)
}

.weui-actionsheet__title .weui-actionsheet__title-text {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2
}

.weui-actionsheet__menu {
  color: var(--weui-FG-0);
  background-color: var(--weui-BG-2)
}

.weui-actionsheet__action {
  margin-top: 8px;
  background-color: var(--weui-BG-2);
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom)
}

.weui-actionsheet__cell {
  position: relative;
  padding: 16px;
  text-align: center;
  font-size: 17px;
  line-height: 1.41176471
}

.weui-actionsheet__cell:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid var(--weui-FG-3);
  color: var(--weui-FG-3);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(.5);
  transform: scaleY(.5)
}

.weui-actionsheet__cell:active {
  background-color: var(--weui-BG-COLOR-ACTIVE)
}

.weui-actionsheet__cell:first-child:before {
  display: none
}

.weui-actionsheet__cell_warn {
  color: var(--weui-RED)
}

.weui-skin_android .weui-actionsheet {
  position: fixed;
  left: 50%;
  top: 50%;
  bottom: auto;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 274px;
  box-sizing: border-box;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  background: transparent;
  -webkit-transition: -webkit-transform .3s;
  transition: -webkit-transform .3s;
  transition: transform .3s;
  transition: transform .3s, -webkit-transform .3s;
  border-top-left-radius: 0;
  border-top-right-radius: 0
}

.weui-skin_android .weui-actionsheet__action {
  display: none
}

.weui-skin_android .weui-actionsheet__menu {
  border-radius: 2px;
  box-shadow: 0 6px 30px 0 rgba(0, 0, 0, .1)
}

.weui-skin_android .weui-actionsheet__cell {
  padding: 16px;
  font-size: 17px;
  line-height: 1.41176471;
  color: var(--weui-FG-0);
  text-align: left
}

.weui-skin_android .weui-actionsheet__cell:first-child {
  border-top-left-radius: 2px;
  border-top-right-radius: 2px
}

.weui-skin_android .weui-actionsheet__cell:last-child {
  border-bottom-left-radius: 2px;
  border-bottom-right-radius: 2px
}

.weui-actionsheet_toggle {
  -webkit-transform: translate(0);
  transform: translate(0)
}

.weui-loadmore {
  width: 65%;
  margin: 1.5em auto;
  line-height: 1.6em;
  font-size: 14px;
  text-align: center
}

.weui-loadmore__tips {
  display: inline-block;
  vertical-align: middle;
  color: var(--weui-FG-0)
}

.weui-loadmore_line {
  border-top: 1px solid var(--weui-FG-3);
  margin-top: 2.4em
}

.weui-loadmore_line .weui-loadmore__tips {
  position: relative;
  top: -.9em;
  padding: 0 .55em;
  background-color: var(--weui-BG-2);
  color: var(--weui-FG-1)
}

.weui-loadmore_dot .weui-loadmore__tips {
  padding: 0 .16em
}

.weui-loadmore_dot .weui-loadmore__tips:before {
  content: " ";
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: var(--weui-FG-3);
  display: inline-block;
  position: relative;
  vertical-align: 0;
  top: -.16em
}

.weui-badge {
  display: inline-block;
  padding: .15em .4em;
  min-width: 8px;
  border-radius: 18px;
  background-color: var(--weui-RED);
  color: #fff;
  line-height: 1.2;
  text-align: center;
  font-size: 12px;
  vertical-align: middle
}

.weui-badge_dot {
  padding: .4em;
  min-width: 0
}

.weui-toptips {
  display: none;
  position: fixed;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  top: 8px;
  left: 8px;
  right: 8px;
  padding: 10px;
  border-radius: 8px;
  font-size: 14px;
  text-align: center;
  color: #fff;
  z-index: 5000;
  word-wrap: break-word;
  word-break: break-all
}

.weui-toptips_warn {
  background-color: var(--weui-RED)
}

.weui-search-bar {
  position: relative;
  padding: 8px;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  box-sizing: border-box;
  background-color: var(--weui-BG-0);
  -webkit-text-size-adjust: 100%;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center
}

.weui-search-bar.weui-search-bar_focusing .weui-search-bar__cancel-btn {
  display: block
}

.weui-search-bar.weui-search-bar_focusing .weui-search-bar__label {
  display: none
}

.weui-search-bar .weui-icon-search {
  width: 16px;
  height: 16px
}

.weui-search-bar__form {
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex: auto;
  flex: auto;
  background-color: var(--weui-BG-2);
  border-radius: 4px
}

.weui-search-bar__box {
  position: relative;
  padding-left: 28px;
  padding-right: 32px;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  z-index: 1
}

.weui-search-bar__box .weui-search-bar__input {
  padding: 8px 0;
  width: 100%;
  height: 1.14285714em;
  border: 0;
  font-size: 14px;
  line-height: 1.14285714em;
  box-sizing: content-box;
  background: transparent;
  caret-color: var(--weui-BRAND);
  color: var(--weui-FG-0)
}

.weui-search-bar__box .weui-search-bar__input:focus {
  outline: none
}

.weui-search-bar__box .weui-icon-search {
  position: absolute;
  top: 50%;
  left: 8px;
  margin-top: -8px
}

.weui-search-bar__box .weui-icon-clear {
  position: absolute;
  top: 50%;
  right: 0;
  margin-top: -16px;
  padding: 8px;
  width: 16px;
  height: 16px;
  -webkit-mask-size: 16px;
  mask-size: 16px
}

.weui-search-bar__label {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2;
  font-size: 0;
  border-radius: 4px;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: var(--weui-FG-1);
  background: var(--weui-BG-2)
}

.weui-search-bar__label span {
  display: inline-block;
  font-size: 14px;
  vertical-align: middle
}

.weui-search-bar__label .weui-icon-search {
  margin-right: 4px
}

.weui-search-bar__cancel-btn {
  display: none;
  margin-left: 8px;
  line-height: 28px;
  color: var(--weui-LINK);
  white-space: nowrap
}

.weui-search-bar__input:not(:valid)+.weui-icon-clear {
  display: none
}

input[type=search]::-webkit-search-cancel-button,
input[type=search]::-webkit-search-decoration,
input[type=search]::-webkit-search-results-button,
input[type=search]::-webkit-search-results-decoration {
  display: none
}

.weui-picker {
  position: fixed;
  width: 100%;
  box-sizing: border-box;
  left: 0;
  bottom: 0;
  z-index: 5000;
  background-color: var(--weui-BG-2);
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transform: translateY(100%);
  transform: translateY(100%);
  -webkit-transition: -webkit-transform .3s;
  transition: -webkit-transform .3s;
  transition: transform .3s;
  transition: transform .3s, -webkit-transform .3s
}

.weui-picker__hd {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  padding: 16px;
  padding: 16px calc(16px + constant(safe-area-inset-right)) 16px calc(16px + constant(safe-area-inset-left));
  padding: 16px calc(16px + env(safe-area-inset-right)) 16px calc(16px + env(safe-area-inset-left));
  position: relative;
  text-align: center;
  font-size: 17px;
  line-height: 1.4
}

.weui-picker__hd:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1px solid var(--weui-FG-3);
  color: var(--weui-FG-3);
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(.5);
  transform: scaleY(.5)
}

.weui-picker__bd {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  position: relative;
  background-color: var(--weui-BG-2);
  height: 240px;
  overflow: hidden
}

.weui-picker__group {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  position: relative;
  height: 100%
}

.weui-picker__group:first-child .weui-picker__item {
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left)
}

.weui-picker__group:last-child .weui-picker__item {
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right)
}

.weui-picker__mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  margin: 0 auto;
  z-index: 3;
  background-image: -webkit-linear-gradient(top, hsla(0, 0%, 100%, .95), hsla(0, 0%, 100%, .6)), -webkit-linear-gradient(bottom, hsla(0, 0%, 100%, .95), hsla(0, 0%, 100%, .6));
  background-image: linear-gradient(180deg, hsla(0, 0%, 100%, .95), hsla(0, 0%, 100%, .6)), linear-gradient(0deg, hsla(0, 0%, 100%, .95), hsla(0, 0%, 100%, .6));
  background-position: top, bottom;
  background-size: 100% 92px;
  background-repeat: no-repeat;
  -webkit-transform: translateZ(0);
  transform: translateZ(0)
}

@media(prefers-color-scheme: dark) {
  .weui-picker__mask {
    background-image: -webkit-linear-gradient(top, rgba(35, 35, 35, .95), rgba(35, 35, 35, .6)), -webkit-linear-gradient(bottom, rgba(35, 35, 35, .95), rgba(35, 35, 35, .6));
    background-image: linear-gradient(180deg, rgba(35, 35, 35, .95), rgba(35, 35, 35, .6)), linear-gradient(0deg, rgba(35, 35, 35, .95), rgba(35, 35, 35, .6))
  }
}

.weui-picker__indicator {
  width: 100%;
  height: 56px;
  position: absolute;
  left: 0;
  top: 92px;
  z-index: 3
}

.weui-picker__indicator:before {
  top: 0;
  border-top: 1px solid var(--weui-FG-3);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(.5);
  transform: scaleY(.5)
}

.weui-picker__indicator:after,
.weui-picker__indicator:before {
  content: " ";
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  color: var(--weui-FG-3)
}

.weui-picker__indicator:after {
  bottom: 0;
  border-bottom: 1px solid var(--weui-FG-3);
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(.5);
  transform: scaleY(.5)
}

.weui-picker__content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%
}

.weui-picker__item {
  height: 48px;
  line-height: 48px;
  text-align: center;
  color: var(--weui-FG-0);
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden
}

.weui-picker__item_disabled {
  color: var(--weui-FG-1)
}

@-webkit-keyframes a {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0)
  }

  to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0)
  }
}

@keyframes a {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0)
  }

  to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0)
  }
}

.weui-animate-slide-up {
  -webkit-animation: a ease .3s forwards;
  animation: a ease .3s forwards
}

@-webkit-keyframes b {
  0% {
    -webkit-transform: translateZ(0);
    transform: translateZ(0)
  }

  to {
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0)
  }
}

@keyframes b {
  0% {
    -webkit-transform: translateZ(0);
    transform: translateZ(0)
  }

  to {
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0)
  }
}

.weui-animate-slide-down {
  -webkit-animation: b ease .3s forwards;
  animation: b ease .3s forwards
}

@-webkit-keyframes c {
  0% {
    opacity: 0
  }

  to {
    opacity: 1
  }
}

@keyframes c {
  0% {
    opacity: 0
  }

  to {
    opacity: 1
  }
}

.weui-animate-fade-in {
  -webkit-animation: c ease .3s forwards;
  animation: c ease .3s forwards
}

@-webkit-keyframes d {
  0% {
    opacity: 1
  }

  to {
    opacity: 0
  }
}

@keyframes d {
  0% {
    opacity: 1
  }

  to {
    opacity: 0
  }
}

.weui-animate-fade-out {
  -webkit-animation: d ease .3s forwards;
  animation: d ease .3s forwards
}

.weui-agree {
  display: block;
  padding: 8px 15px 0;
  font-size: 14px;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0)
}

.weui-agree a,
.weui-agree navigator {
  color: var(--weui-LINK)
}

.weui-agree navigator {
  display: inline
}

.weui-agree__text {
  color: var(--weui-FG-1);
  margin-left: 2px
}

.weui-agree__checkbox {
  -webkit-appearance: none;
  appearance: none;
  display: inline-block;
  border: 0;
  outline: 0;
  vertical-align: middle;
  background-color: currentColor;
  -webkit-mask-position: 0 0;
  mask-position: 0 0;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100%;
  mask-size: 100%;
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%221000%22%20height%3D%221000%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M500%20916.667C269.881%20916.667%2083.333%20730.119%2083.333%20500%2083.333%20269.881%20269.881%2083.333%20500%2083.333c230.119%200%20416.667%20186.548%20416.667%20416.667%200%20230.119-186.548%20416.667-416.667%20416.667zm0-50c202.504%200%20366.667-164.163%20366.667-366.667%200-202.504-164.163-366.667-366.667-366.667-202.504%200-366.667%20164.163-366.667%20366.667%200%20202.504%20164.163%20366.667%20366.667%20366.667z%22%20fill-rule%3D%22evenodd%22%20fill-opacity%3D%22.9%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%221000%22%20height%3D%221000%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M500%20916.667C269.881%20916.667%2083.333%20730.119%2083.333%20500%2083.333%20269.881%20269.881%2083.333%20500%2083.333c230.119%200%20416.667%20186.548%20416.667%20416.667%200%20230.119-186.548%20416.667-416.667%20416.667zm0-50c202.504%200%20366.667-164.163%20366.667-366.667%200-202.504-164.163-366.667-366.667-366.667-202.504%200-366.667%20164.163-366.667%20366.667%200%20202.504%20164.163%20366.667%20366.667%20366.667z%22%20fill-rule%3D%22evenodd%22%20fill-opacity%3D%22.9%22%2F%3E%3C%2Fsvg%3E);
  color: var(--weui-FG-2);
  width: 1em;
  height: 1em;
  font-size: 17px;
  margin-top: -.2em
}

.weui-agree__checkbox-check {
  position: absolute;
  left: -9999px
}

.weui-agree__checkbox-check[aria-checked=true]+.weui-agree__checkbox,
.weui-agree__checkbox:checked {
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm-1.177-7.86l-2.765-2.767L7%2012.431l3.119%203.121a1%201%200%20001.414%200l5.952-5.95-1.062-1.062-5.6%205.6z%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm-1.177-7.86l-2.765-2.767L7%2012.431l3.119%203.121a1%201%200%20001.414%200l5.952-5.95-1.062-1.062-5.6%205.6z%22%2F%3E%3C%2Fsvg%3E);
  color: var(--weui-BRAND)
}

.weui-agree_animate {
  -webkit-animation: e .3s 1;
  animation: e .3s 1
}

@-webkit-keyframes e {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0)
  }

  16% {
    -webkit-transform: translateX(-8px);
    transform: translateX(-8px)
  }

  28% {
    -webkit-transform: translateX(-16px);
    transform: translateX(-16px)
  }

  44% {
    -webkit-transform: translateX(0);
    transform: translateX(0)
  }

  59% {
    -webkit-transform: translateX(-16px);
    transform: translateX(-16px)
  }

  73% {
    -webkit-transform: translateX(0);
    transform: translateX(0)
  }

  82% {
    -webkit-transform: translateX(16px);
    transform: translateX(16px)
  }

  94% {
    -webkit-transform: translateX(8px);
    transform: translateX(8px)
  }

  to {
    -webkit-transform: translateX(0);
    transform: translateX(0)
  }
}

@keyframes e {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0)
  }

  16% {
    -webkit-transform: translateX(-8px);
    transform: translateX(-8px)
  }

  28% {
    -webkit-transform: translateX(-16px);
    transform: translateX(-16px)
  }

  44% {
    -webkit-transform: translateX(0);
    transform: translateX(0)
  }

  59% {
    -webkit-transform: translateX(-16px);
    transform: translateX(-16px)
  }

  73% {
    -webkit-transform: translateX(0);
    transform: translateX(0)
  }

  82% {
    -webkit-transform: translateX(16px);
    transform: translateX(16px)
  }

  94% {
    -webkit-transform: translateX(8px);
    transform: translateX(8px)
  }

  to {
    -webkit-transform: translateX(0);
    transform: translateX(0)
  }
}

.weui-loading {
  width: 20px;
  height: 20px;
  display: inline-block;
  vertical-align: middle;
  -webkit-animation: f 1s steps(12) infinite;
  animation: f 1s steps(12) infinite;
  background: transparent url("data:image/svg+xml;charset=utf8, %3Csvg xmlns='http://www.w3.org/2000/svg' width='120' height='120' viewBox='0 0 100 100'%3E%3Cpath fill='none' d='M0 0h100v100H0z'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23E9E9E9' rx='5' ry='5' transform='translate(0 -30)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23989697' rx='5' ry='5' transform='rotate(30 105.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%239B999A' rx='5' ry='5' transform='rotate(60 75.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23A3A1A2' rx='5' ry='5' transform='rotate(90 65 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23ABA9AA' rx='5' ry='5' transform='rotate(120 58.66 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23B2B2B2' rx='5' ry='5' transform='rotate(150 54.02 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23BAB8B9' rx='5' ry='5' transform='rotate(180 50 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23C2C0C1' rx='5' ry='5' transform='rotate(-150 45.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23CBCBCB' rx='5' ry='5' transform='rotate(-120 41.34 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23D2D2D2' rx='5' ry='5' transform='rotate(-90 35 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23DADADA' rx='5' ry='5' transform='rotate(-60 24.02 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23E2E2E2' rx='5' ry='5' transform='rotate(-30 -5.98 65)'/%3E%3C/svg%3E") no-repeat;
  background-size: 100%
}

.weui-btn_loading.weui-btn_primary .weui-loading,
.weui-loading.weui-loading_transparent {
  background-image: url("data:image/svg+xml;charset=utf8, %3Csvg xmlns='http://www.w3.org/2000/svg' width='120' height='120' viewBox='0 0 100 100'%3E%3Cpath fill='none' d='M0 0h100v100H0z'/%3E%3Crect xmlns='http://www.w3.org/2000/svg' width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.56)' rx='5' ry='5' transform='translate(0 -30)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.5)' rx='5' ry='5' transform='rotate(30 105.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.43)' rx='5' ry='5' transform='rotate(60 75.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.38)' rx='5' ry='5' transform='rotate(90 65 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.32)' rx='5' ry='5' transform='rotate(120 58.66 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.28)' rx='5' ry='5' transform='rotate(150 54.02 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.25)' rx='5' ry='5' transform='rotate(180 50 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.2)' rx='5' ry='5' transform='rotate(-150 45.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.17)' rx='5' ry='5' transform='rotate(-120 41.34 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.14)' rx='5' ry='5' transform='rotate(-90 35 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.1)' rx='5' ry='5' transform='rotate(-60 24.02 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.03)' rx='5' ry='5' transform='rotate(-30 -5.98 65)'/%3E%3C/svg%3E")
}

@-webkit-keyframes f {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg)
  }

  to {
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn)
  }
}

@keyframes f {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg)
  }

  to {
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn)
  }
}

.weui-slider {
  padding: 15px 18px;
  -webkit-user-select: none;
  user-select: none
}

.weui-slider__inner {
  position: relative;
  height: 2px;
  background-color: var(--weui-FG-3)
}

.weui-slider__track {
  height: 2px;
  background-color: var(--weui-BRAND);
  width: 0
}

.weui-slider__handler {
  position: absolute;
  left: 0;
  top: 50%;
  width: 28px;
  height: 28px;
  margin-left: -14px;
  margin-top: -14px;
  border-radius: 50%;
  background-color: #fff;
  box-shadow: 0 0 4px var(--weui-FG-3)
}

.weui-slider-box {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center
}

.weui-slider-box .weui-slider {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1
}

.weui-slider-box__value {
  margin-left: .5em;
  min-width: 24px;
  color: var(--weui-FG-1);
  text-align: center;
  font-size: 14px
}

.wx_dot_loading,
.wx_dot_loading:after,
.wx_dot_loading:before {
  display: inline-block;
  vertical-align: middle;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, .3);
  font-size: 0;
  -webkit-animation: h 1.6s step-start infinite;
  animation: h 1.6s step-start infinite
}

.wx_dot_loading {
  position: relative
}

.wx_dot_loading:before {
  content: "";
  position: absolute;
  left: -12px;
  background-color: rgba(0, 0, 0, .1);
  -webkit-animation: g 1.6s step-start infinite;
  animation: g 1.6s step-start infinite
}

.wx_dot_loading:after {
  content: "";
  position: absolute;
  right: -12px;
  background-color: rgba(0, 0, 0, .5);
  -webkit-animation: i 1.6s step-start infinite;
  animation: i 1.6s step-start infinite
}

@-webkit-keyframes g {

  0%,
  to {
    background-color: rgba(0, 0, 0, .1)
  }

  30% {
    background-color: rgba(0, 0, 0, .5)
  }

  60% {
    background-color: rgba(0, 0, 0, .3)
  }
}

@keyframes g {

  0%,
  to {
    background-color: rgba(0, 0, 0, .1)
  }

  30% {
    background-color: rgba(0, 0, 0, .5)
  }

  60% {
    background-color: rgba(0, 0, 0, .3)
  }
}

@-webkit-keyframes h {

  0%,
  to {
    background-color: rgba(0, 0, 0, .3)
  }

  30% {
    background-color: rgba(0, 0, 0, .1)
  }

  60% {
    background-color: rgba(0, 0, 0, .5)
  }
}

@keyframes h {

  0%,
  to {
    background-color: rgba(0, 0, 0, .3)
  }

  30% {
    background-color: rgba(0, 0, 0, .1)
  }

  60% {
    background-color: rgba(0, 0, 0, .5)
  }
}

@-webkit-keyframes i {

  0%,
  to {
    background-color: rgba(0, 0, 0, .5)
  }

  30% {
    background-color: rgba(0, 0, 0, .3)
  }

  60% {
    background-color: rgba(0, 0, 0, .1)
  }
}

@keyframes i {

  0%,
  to {
    background-color: rgba(0, 0, 0, .5)
  }

  30% {
    background-color: rgba(0, 0, 0, .3)
  }

  60% {
    background-color: rgba(0, 0, 0, .1)
  }
}

.wx_dot_loading_white {
  background-color: hsla(0, 0%, 100%, .3);
  -webkit-animation: k 1.6s step-start infinite;
  animation: k 1.6s step-start infinite
}

.wx_dot_loading_white:before {
  background-color: hsla(0, 0%, 100%, .5);
  -webkit-animation: j 1.6s step-start infinite;
  animation: j 1.6s step-start infinite
}

.wx_dot_loading_white:after {
  background-color: hsla(0, 0%, 100%, .1);
  -webkit-animation: l 1.6s step-start infinite;
  animation: l 1.6s step-start infinite
}

@-webkit-keyframes j {

  0%,
  to {
    background-color: hsla(0, 0%, 100%, .5)
  }

  30% {
    background-color: hsla(0, 0%, 100%, .1)
  }

  60% {
    background-color: hsla(0, 0%, 100%, .3)
  }
}

@keyframes j {

  0%,
  to {
    background-color: hsla(0, 0%, 100%, .5)
  }

  30% {
    background-color: hsla(0, 0%, 100%, .1)
  }

  60% {
    background-color: hsla(0, 0%, 100%, .3)
  }
}

@-webkit-keyframes k {

  0%,
  to {
    background-color: hsla(0, 0%, 100%, .3)
  }

  30% {
    background-color: hsla(0, 0%, 100%, .5)
  }

  60% {
    background-color: hsla(0, 0%, 100%, .1)
  }
}

@keyframes k {

  0%,
  to {
    background-color: hsla(0, 0%, 100%, .3)
  }

  30% {
    background-color: hsla(0, 0%, 100%, .5)
  }

  60% {
    background-color: hsla(0, 0%, 100%, .1)
  }
}

@-webkit-keyframes l {

  0%,
  to {
    background-color: hsla(0, 0%, 100%, .1)
  }

  30% {
    background-color: hsla(0, 0%, 100%, .3)
  }

  60% {
    background-color: hsla(0, 0%, 100%, .5)
  }
}

@keyframes l {

  0%,
  to {
    background-color: hsla(0, 0%, 100%, .1)
  }

  30% {
    background-color: hsla(0, 0%, 100%, .3)
  }

  60% {
    background-color: hsla(0, 0%, 100%, .5)
  }
}

:host {
  width: 100%
}

.weui-slideview {
  position: relative;
  overflow: hidden
}

.weui-slideview__left {
  position: relative;
  z-index: 10
}

.weui-slideview__right {
  position: absolute;
  z-index: 1;
  left: 100%;
  top: 0;
  height: 100%
}

.weui-slideview__btn__wrp {
  position: absolute;
  left: 0;
  bottom: 0;
  text-align: center;
  min-width: 69px;
  height: 100%;
  white-space: nowrap
}

.weui-slideview__btn {
  color: #fff;
  padding: 0 17px
}

.weui-slideview__btn-group_default .weui-slideview__btn {
  background: #c7c7cc
}

@media(prefers-color-scheme: dark) {
  .weui-slideview__btn-group_default .weui-slideview__btn {
    background: var(--weui-BG-4)
  }
}

.weui-slideview__btn-group_default~.weui-slideview__btn-group_default:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  width: 1px;
  bottom: 0;
  border-left: 1px solid #fff;
  color: #fff;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleX(.5);
  transform: scaleX(.5)
}

@media(prefers-color-scheme: dark) {
  .weui-slideview__btn-group_default~.weui-slideview__btn-group_default:before {
    border-left-color: var(--weui-FG-3)
  }
}

.weui-slideview__btn-group_default:first-child:before {
  display: none
}

.weui-slideview__btn-group_warn .weui-slideview__btn {
  background: #fe3b30
}

.weui-slideview__btn-group_warn~.weui-slideview__btn-group_warn:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  width: 1px;
  bottom: 0;
  border-left: 1px solid #fff;
  color: #fff;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleX(.5);
  transform: scaleX(.5)
}

.weui-slideview__btn-group_warn:first-child:before {
  display: none
}

.weui-slideview_icon .weui-slideview__btn__wrp {
  background: transparent;
  font-size: 0
}

.weui-slideview_icon .weui-slideview__btn__wrp:first-child {
  padding-left: 16px
}

.weui-slideview_icon .weui-slideview__btn__wrp:last-child {
  padding-right: 8px
}

.weui-slideview_icon .weui-slideview__btn {
  width: 48px;
  height: 48px;
  line-height: 48px;
  padding: 0;
  display: inline-block;
  vertical-align: middle;
  border-radius: 50%;
  background-color: #fff
}

@media(prefers-color-scheme: dark) {
  .weui-slideview_icon .weui-slideview__btn {
    background-color: var(--weui-BG-4)
  }
}

.weui-slideview_icon .weui-slideview__btn__icon {
  display: inline-block;
  vertical-align: middle;
  width: 22px;
  height: 22px
}

page {
  --height: 44px;
  --right: 95px
}

.weui-navigation-bar {
  overflow: hidden;
  color: var(--weui-FG-0)
}

.weui-navigation-bar .android {
  --height: 48px;
  --right: 221px
}

.weui-navigation-bar__inner {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 5001;
  height: var(--height);
  padding-right: var(--right);
  width: calc(100% - var(--right))
}

.weui-navigation-bar__inner,
.weui-navigation-bar__inner .weui-navigation-bar__left {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center
}

.weui-navigation-bar__inner .weui-navigation-bar__left {
  position: relative;
  width: var(--right);
  padding-left: 16px
}

.weui-navigation-bar__inner .weui-navigation-bar__left .weui-navigation-bar__btn {
  display: inline-block;
  vertical-align: middle;
  background-repeat: no-repeat
}

.weui-navigation-bar__inner .weui-navigation-bar__left .weui-navigation-bar__btn_goback {
  font-size: 12px;
  width: 1em;
  height: 2em;
  -webkit-mask: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='24' viewBox='0 0 12 24'%3E  %3Cpath fill-opacity='.9' fill-rule='evenodd' d='M10 19.438L8.955 20.5l-7.666-7.79a1.02 1.02 0 0 1 0-1.42L8.955 3.5 10 4.563 2.682 12 10 19.438z'/%3E%3C/svg%3E") no-repeat 50% 50%;
  mask: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='24' viewBox='0 0 12 24'%3E  %3Cpath fill-opacity='.9' fill-rule='evenodd' d='M10 19.438L8.955 20.5l-7.666-7.79a1.02 1.02 0 0 1 0-1.42L8.955 3.5 10 4.563 2.682 12 10 19.438z'/%3E%3C/svg%3E") no-repeat 50% 50%;
  -webkit-mask-size: cover;
  mask-size: cover;
  background-color: currentColor
}

.weui-navigation-bar__inner .weui-navigation-bar__left .weui-navigation-bar__btn_goback:active {
  opacity: .5
}

.weui-navigation-bar__inner .weui-navigation-bar__center {
  font-size: 17px;
  text-align: center;
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  justify-content: center
}

.weui-navigation-bar__inner .weui-navigation-bar__loading {
  margin-right: 4px;
  font-size: 0
}

.weui-navigation-bar__inner .weui-navigation-bar__loading .weui-loading {
  margin-left: 0
}

.weui-navigation-bar__inner .weui-navigation-bar__right {
  margin-right: 16px
}

.weui-navigation-bar__placeholder {
  height: var(--height);
  background: var(--weui-BG-1);
  position: relative;
  z-index: 50
}

.weui-uploader__hd {
  display: block
}

.weui-uploader__overview {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center
}

.weui-uploader__tips {
  color: var(--weui-FG-2);
  font-size: 14px;
  line-height: 1.4;
  padding-top: 4px
}

.weui-uploader__img {
  display: block;
  width: 100%;
  height: 100%
}

.weui-gallery {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  flex-direction: column;
  -webkit-flex-wrap: nowrap;
  flex-wrap: nowrap
}

.weui-gallery__info {
  color: #fff;
  font-size: 17px;
  line-height: 60px;
  min-height: 60px;
  text-align: center
}

.weui-gallery__img__wrp {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  position: relative;
  font-size: 0
}

.weui-gallery__img {
  position: absolute;
  width: 100%;
  height: 100%
}

.weui-gallery__opr {
  position: static
}

.weui-search-bar .weui-search-bar__box .weui-search-bar__input {
  height: inherit;
  line-height: inherit
}

.weui-search-bar .weui-search-bar__box .weui-icon-clear {
  display: block
}

.weui-loadmore .weui-loading {
  margin-right: .3em
}

.weui-btn_input-clear {
  display: block
}
class t{constructor(t,e){this.cols=t||0,this.rows=e||0,this.data=new Uint8Array(this.rows*this.cols)}get reservedBit(){return this._reservedBit||(this._reservedBit=new Uint8Array(this.data.length)),this._reservedBit}set(t,e,r,s){const o=t*this.cols+e;this.data[o]="number"==typeof r?r:r?1:0,s&&(this.reservedBit[o]=1)}get(t,e){return this.data[t*this.cols+e]}getRowData(t){const e=(t||0)*this.cols;return this.data.slice(e,e+this.cols)}getColData(t){return this.data.filter(((e,r)=>r%this.cols===t))}xor(t,e,r){this.data[t*this.cols+e]^=r?1:0}fill(t,e,r,s){const o=(e||0)*this.cols+(r||0);let i=0;i="number"==typeof s?s>0?o+s:this.data.length+s:this.data.length,this.data.fill(t,o,i)}setArray(t,e,r){const s=(e||0)*this.cols+(r||0);this.data.set(t,s)}addRow(t,e,r){e>=this.data.length?console.error(`offset: ${e} 越界`):(r&&r<t.length&&(t=t.slice(0,r)),this.data.set(t,e))}isReserved(t,e){return this._reservedBit?this._reservedBit[t*this.cols+e]:0}}const e=Object.freeze({nullCharacter:0,maxAsciiCharacter:127,lineFeed:10,LF:10,carriageReturn:13,CR:13,lineSeparator:8232,paragraphSeparator:8233,nextLine:133,space:32,nonBreakingSpace:160,enQuad:8192,emQuad:8193,enSpace:8194,emSpace:8195,threePerEmSpace:8196,fourPerEmSpace:8197,sixPerEmSpace:8198,figureSpace:8199,punctuationSpace:8200,thinSpace:8201,hairSpace:8202,zeroWidthSpace:8203,narrowNoBreakSpace:8239,ideographicSpace:12288,mathematicalSpace:8287,ogham:5760,_:95,$:36,num_0:48,num_9:57,a:97,z:122,A:65,Z:90,ampersand:38,asterisk:42,at:64,backslash:92,backtick:96,bar:124,caret:94,closeBrace:125,closeBracket:93,closeParen:41,colon:58,comma:44,dot:46,doubleQuote:34,equals:61,exclamation:33,greaterThan:62,hash:35,lessThan:60,minus:45,openBrace:123,openBracket:91,openParen:40,percent:37,plus:43,question:63,semicolon:59,singleQuote:39,slash:47,tilde:126,backspace:8,formFeed:12,byteOrderMark:65279,tab:9,verticalTab:11});class r{static isDigit(t){return"number"==typeof t?t>=e.num_0&&t<=e.num_9:t>="0"&&t<="9"}static isDigits(t,e,r){const s=e||0,o=r?s+r:t.length;return!(o>t.length)&&t.substring(s,o).match(/^[0-9]+$/)}static isUpper(t){return"number"==typeof t?t>=e.A&&t<=e.Z:t>="A"&&t<="Z"}static isLower(t){return"number"==typeof t?t>=e.a&&t<=e.z:t>="a"&&t<="z"}static ctoi(t){const r="string"==typeof t?t.charCodeAt(0):t;return r>=e.num_0&&r<=e.num_9?r-e.num_0:r>=e.A&&r<=e.Z?r-e.A+10:r>=e.a&&r<=e.z?r-e.a+10:-1}static itoc(t){return t>=0&&t<=9?`${t}`:String.fromCharCode(t-10+e.A)}static repeatChar(t,e){const r=[];for(let s=0;s<e;s++)r.push(t);return r.join("")}static preFillChar(t,e,r){return t.length<e?Array(e-t.length+1).join(r)+t:t}}class s{static isISO_8859_1(t){if(!t)return!1;for(let e=0;e<t.length;e++)if(t.charCodeAt(e)>255)return!1;return!0}static getCharCodes(t){t=t||"";const e=[];for(let r=0;r<t.length;r++)e.push(t.charCodeAt(r));return e}static getCharCodeArrayString(t){return t.map((t=>String.fromCharCode(t))).join("")}static encodeUtf8(t){const e=[];for(let r=0;r<t.length;r++){let s=t.charCodeAt(r);if(s>=55296&&s<=56319&&t.length>r+1){const e=t.charCodeAt(r+1);e>=56320&&e<=57343&&(s=1024*(s-55296)+e-56320+65536,r+=1)}s<128?e.push(s):s<2048?(e.push(s>>6|192),e.push(63&s|128)):s<55296||s>=57344&&s<65536?(e.push(224|s>>12),e.push(128|s>>6&63),e.push(128|63&s)):s>=65536&&s<=1114111?(e.push(240|s>>18),e.push(128|s>>12&63),e.push(128|s>>6&63),e.push(128|63&s)):e.push(239,191,189)}return new Uint8Array(e)}static getBytes_Utf8(t){try{return(new TextEncoder).encode(t)}catch(e){return console.log("DzTextEncoder.encode: TextEncoder is not defined"),this.encodeUtf8(t)}}static getBytes_ISO8859_1(t){return t=unescape(encodeURIComponent(t)),Uint8Array.from(this.getCharCodes(t))}static getBytes_Unicode(t){return Uint8Array.from(this.getCharCodes(t))}static getBytes(t,e){return t=t||"",e?s.getBytes_Utf8(t):s.getBytes_Unicode(t)}static hasBase256Chars(t){const e="string"==typeof t?t.split("").map((t=>t.charCodeAt(0))):t;if((null==e?void 0:e.length)>0)for(const t of e)if(t>=128)return!0;return!1}static encodeUnicodeFromUtf8(t){let e=0,r=0,s=0;const o=[];do{if(t[r]<=127)o[s]=t[r],e=r+1,s++;else{if(t[r]>=128&&t[r]<=191)return;if(t[r]>=192&&t[r]<=193)return;if(t[r]>=194&&t[r]<=223)o[s]=((31&t[r])<<6)+(63&t[r+1]),e=r+2,s++;else if(t[r]>=224&&t[r]<=239)o[s]=((15&t[r])<<12)+((63&t[r+1])<<6)+(63&t[r+2]),e=r+3,s++;else if(t[r]>=240)return}r=e}while(r<t.length);return o}}const o=Object.freeze({Auto:0,Square:1,Rectangle:2});var i;!function(t){t[t.ASCII=0]="ASCII",t[t.C40=1]="C40",t[t.TEXT=2]="TEXT",t[t.X12=3]="X12",t[t.EDIFACT=4]="EDIFACT",t[t.BASE256=5]="BASE256"}(i||(i={}));class a{static PdfDataMatrixSymbolAttribute(t,e,r,s,o,i,a,n,c){return{SymbolRow:t,SymbolColumn:e,HorizontalDataRegion:r,VerticalDataRegion:s,DataCodewords:o,CorrectionCodewords:i,InterleavedBlock:a,InterleavedDataBlock:n,Rectangle:c}}static createSymbolAttributes(){const t=[];return t.push(this.PdfDataMatrixSymbolAttribute(10,10,1,1,3,5,1,3)),t.push(this.PdfDataMatrixSymbolAttribute(12,12,1,1,5,7,1,5)),t.push(this.PdfDataMatrixSymbolAttribute(8,18,1,1,5,7,1,5,!0)),t.push(this.PdfDataMatrixSymbolAttribute(14,14,1,1,8,10,1,8)),t.push(this.PdfDataMatrixSymbolAttribute(8,32,2,1,10,11,1,10,!0)),t.push(this.PdfDataMatrixSymbolAttribute(16,16,1,1,12,12,1,12)),t.push(this.PdfDataMatrixSymbolAttribute(12,26,1,1,16,14,1,16,!0)),t.push(this.PdfDataMatrixSymbolAttribute(18,18,1,1,18,14,1,18)),t.push(this.PdfDataMatrixSymbolAttribute(20,20,1,1,22,18,1,22)),t.push(this.PdfDataMatrixSymbolAttribute(12,36,2,1,22,18,1,22,!0)),t.push(this.PdfDataMatrixSymbolAttribute(22,22,1,1,30,20,1,30)),t.push(this.PdfDataMatrixSymbolAttribute(16,36,2,1,32,24,1,32,!0)),t.push(this.PdfDataMatrixSymbolAttribute(24,24,1,1,36,24,1,36)),t.push(this.PdfDataMatrixSymbolAttribute(26,26,1,1,44,28,1,44)),t.push(this.PdfDataMatrixSymbolAttribute(16,48,2,1,49,28,1,49,!0)),t.push(this.PdfDataMatrixSymbolAttribute(32,32,2,2,62,36,1,62)),t.push(this.PdfDataMatrixSymbolAttribute(36,36,2,2,86,42,1,86)),t.push(this.PdfDataMatrixSymbolAttribute(40,40,2,2,114,48,1,114)),t.push(this.PdfDataMatrixSymbolAttribute(44,44,2,2,144,56,1,144)),t.push(this.PdfDataMatrixSymbolAttribute(48,48,2,2,174,68,1,174)),t.push(this.PdfDataMatrixSymbolAttribute(52,52,2,2,204,84,2,102)),t.push(this.PdfDataMatrixSymbolAttribute(64,64,4,4,280,112,2,140)),t.push(this.PdfDataMatrixSymbolAttribute(72,72,4,4,368,144,4,92)),t.push(this.PdfDataMatrixSymbolAttribute(80,80,4,4,456,192,4,114)),t.push(this.PdfDataMatrixSymbolAttribute(88,88,4,4,576,224,4,144)),t.push(this.PdfDataMatrixSymbolAttribute(96,96,4,4,696,272,4,174)),t.push(this.PdfDataMatrixSymbolAttribute(104,104,4,4,816,336,6,136)),t.push(this.PdfDataMatrixSymbolAttribute(120,120,6,6,1050,408,6,175)),t.push(this.PdfDataMatrixSymbolAttribute(132,132,6,6,1304,496,8,163)),t.push(this.PdfDataMatrixSymbolAttribute(144,144,6,6,1558,620,10,156)),t}static getSymbolInfo(t,e,r){const s=r||0;let i,a,n=0;const c=this.createSymbolAttributes();for(const r of c)if(!(r.DataCodewords<t))if(a||(a=r),e===o.Square){if(r.SymbolColumn==r.SymbolRow){i=r;break}}else if(e===o.Rectangle){if(r.SymbolColumn!=r.SymbolRow){i=r;break}}else{if(s<1){i=r;break}n=r.SymbolColumn/r.SymbolRow,n<=s&&(!i||r.SymbolRow<i.SymbolRow)&&(i=r)}return i||a}}class n{get ByteArray(){return this.byteArray}get Encoding(){return this.newEncoding}get Codewords(){return this.codewords}get SymbolInfo(){return this.symbolInfo||this.updateSymbolInfo(),this.symbolInfo}get remainingChars(){return this.byteArray.length-this.pos}constructor(t,e,r){this.codewords=[],this.shape=o.Auto,this.pos=0,this.byteArray=t||Uint8Array.from([]),this.shape=e||o.Auto,this.aspectRatio=r}hasMoreCharacters(){return this.pos<this.byteArray.length}getCharCode(t){const e="number"==typeof t?this.pos+t:this.pos;return this.byteArray[e]}writeCodeword(...t){this.codewords.push(...t)}writeCodewords(t){for(const e of t)this.codewords.push(e)}signalEncoderChange(t){this.newEncoding=t}resetEncoderSignal(){this.newEncoding=void 0}getCodewords(){return Uint8Array.from(this.codewords)}updateSymbolInfo(t){const e="number"==typeof t&&t>0?t:this.codewords.length;(!this.symbolInfo||e>this.symbolInfo.DataCodewords)&&(this.symbolInfo=a.getSymbolInfo(e,this.shape,this.aspectRatio))}resetSymbolInfo(){this.symbolInfo=void 0}}class c{static isExtendedASCII(t){return t>=128&&t<=255}static isNativeC40(t){return t==e.space||r.isDigit(t)||r.isUpper(t)}static isNativeText(t){return t==e.space||r.isDigit(t)||r.isLower(t)}static isNativeX12(t){return this.isX12TermSep(t)||this.isNativeC40(t)}static isX12TermSep(t){return t==e.CR||t=="*".charCodeAt(0)||t==">".charCodeAt(0)}static isNativeEDIFACT(t){return t>=e.space&&t<="^".charCodeAt(0)}static getEncoder(t){if(!this.encodeMap.has(t))switch(t){default:case i.ASCII:this.encodeMap.set(t,new h);break;case i.C40:this.encodeMap.set(t,new l);break;case i.TEXT:this.encodeMap.set(t,new d);break;case i.X12:this.encodeMap.set(t,new C);break;case i.EDIFACT:this.encodeMap.set(t,new u);break;case i.BASE256:this.encodeMap.set(t,new p)}return this.encodeMap.get(t)}static determineConsecutiveDigitCount(t,e){const s=t.length;let o=e;for(;o<s&&r.isDigit(t[o]);)o++;return o-e}static encodeNumberic(t,r){return 10*(t-e.num_0)+(r-e.num_0)+130}static isSpecialB256(t){return!1}static illegalCharacter(t){let e=t.toString(16);throw e="0000".substring(0,4-e.length)+e,new Error(`Illegal character: ${t} (0x${e})`)}static lookAheadTest(t,e,r){const s=this.lookAheadTestIntern(t,e,r);if(r==i.X12&&s==i.X12){const r=Math.min(e+3,t.length);for(let s=e;s<r;s++)if(!c.isNativeX12(t[s]))return i.ASCII}else if(r==i.EDIFACT&&s==i.EDIFACT){const r=Math.min(e+4,t.length);for(let s=e;s<r;s++)if(!c.isNativeEDIFACT(t[s]))return i.ASCII}return s}static findMinimums(t,e,r){let s=Number.MAX_VALUE;for(let o=0;o<6;o++){const i=e[o]=Math.ceil(t[o]);s>i&&(s=i,r.fill(0)),s==i&&r[o]++}return s}static getMinimumCount(t){return t.reduce(((t,e)=>t+e))}static lookAheadTestIntern(t,e,s){if(e>=t.length)return s;let o;s==i.ASCII?o=[0,1,1,1,1,1.25]:(o=[1,2,2,2,2,2.25],o[s]=0);let a=0;const n=Array(6),h=Array(6);for(;;){if(e+a==t.length){n.fill(0),h.fill(0);const t=this.findMinimums(o,h,n),e=this.getMinimumCount(n);if(h[i.ASCII]==t)return i.ASCII;if(1==e){if(n[i.BASE256]>0)return i.BASE256;if(n[i.EDIFACT]>0)return i.EDIFACT;if(n[i.TEXT]>0)return i.TEXT;if(n[i.X12]>0)return i.X12}return i.C40}const s=t[e+a];if(a++,r.isDigit(s)?o[i.ASCII]+=.5:c.isExtendedASCII(s)?(o[i.ASCII]=Math.ceil(o[i.ASCII]),o[i.ASCII]+=2):(o[i.ASCII]=Math.ceil(o[i.ASCII]),o[i.ASCII]++),c.isNativeC40(s)?o[i.C40]+=2/3:c.isExtendedASCII(s)?o[i.C40]+=8/3:o[i.C40]+=4/3,c.isNativeText(s)?o[i.TEXT]+=2/3:c.isExtendedASCII(s)?o[i.TEXT]+=8/3:o[i.TEXT]+=4/3,c.isNativeX12(s)?o[i.X12]+=2/3:c.isExtendedASCII(s)?o[i.X12]+=13/3:o[i.X12]+=10/3,c.isNativeEDIFACT(s)?o[i.EDIFACT]+=3/4:c.isExtendedASCII(s)?o[i.EDIFACT]+=4.25:o[i.EDIFACT]+=3.25,c.isSpecialB256(s)?o[i.BASE256]+=4:o[i.BASE256]++,a>=4){if(n.fill(0),h.fill(0),this.findMinimums(o,h,n),h[i.ASCII]<Math.min(h[i.BASE256],h[i.C40],h[i.TEXT],h[i.X12],h[i.EDIFACT]))return i.ASCII;if(h[i.BASE256]<h[i.ASCII]||h[i.BASE256]+1<Math.min(h[i.C40],h[i.TEXT],h[i.X12],h[i.EDIFACT]))return i.BASE256;if(h[i.EDIFACT]+1<Math.min(h[i.BASE256],h[i.C40],h[i.TEXT],h[i.X12],h[i.ASCII]))return i.EDIFACT;if(h[i.TEXT]+1<Math.min(h[i.BASE256],h[i.C40],h[i.EDIFACT],h[i.X12],h[i.ASCII]))return i.TEXT;if(h[i.X12]+1<Math.min(h[i.BASE256],h[i.C40],h[i.EDIFACT],h[i.TEXT],h[i.ASCII]))return i.X12;if(h[i.C40]+1<Math.min(h[i.ASCII],h[i.BASE256],h[i.EDIFACT],h[i.TEXT])){if(h[i.C40]<h[i.X12])return i.C40;if(h[i.C40]==h[i.X12]){let r=e+a+1;for(;r<t.length;){const e=t[r];if(c.isX12TermSep(e))return i.X12;if(!c.isNativeX12(e))break;r++}return i.C40}}}}}encode(t){throw new Error("umimpliment method!")}encodeChar(t,e){throw new Error("umimpliment method!")}getEncodingMode(){return i.ASCII}}c.PAD=129,c.LATCH_TO_C40=230,c.LATCH_TO_BASE256=231,c.UPPER_SHIFT=235,c.LATCH_TO_ANSIX12=238,c.LATCH_TO_TEXT=239,c.LATCH_TO_EDIFACT=240,c.C40_UNLATCH=254,c.X12_UNLATCH=254,c.encodeMap=new Map;class h extends c{getEncodingMode(){return i.ASCII}encodeChar(t,e){if(e.length>0&&r.isDigit(t)){const s=e.length-1,o=e[s]-1,i=e.length>=2?e[e.length-2]:0;return r.isDigit(o)&&i!==c.UPPER_SHIFT?(e[s]=c.encodeNumberic(o,t),0):(e.push(t+1),1)}return t<127?(e.push(t+1),1):(e.push(c.UPPER_SHIFT),e.push(t-128+1),2)}encode(t){let e=c.determineConsecutiveDigitCount(t.ByteArray,t.pos);if(e>=2){e%2==1&&e!==t.remainingChars&&e--;const r=[];for(let s=0;s<e;s++)this.encodeChar(t.getCharCode(),r),t.pos++;t.writeCodewords(r)}else{const e=t.getCharCode(),r=c.lookAheadTest(t.ByteArray,t.pos,this.getEncodingMode());if(r!=this.getEncodingMode())switch(r){case i.BASE256:return t.writeCodeword(c.LATCH_TO_BASE256),void t.signalEncoderChange(i.BASE256);case i.C40:return t.writeCodeword(c.LATCH_TO_C40),void t.signalEncoderChange(i.C40);case i.X12:t.writeCodeword(c.LATCH_TO_ANSIX12),t.signalEncoderChange(i.X12);break;case i.TEXT:t.writeCodeword(c.LATCH_TO_TEXT),t.signalEncoderChange(i.TEXT);break;case i.EDIFACT:t.writeCodeword(c.LATCH_TO_EDIFACT),t.signalEncoderChange(i.EDIFACT);break;default:throw new Error("Illegal mode: "+r)}else c.isExtendedASCII(e)?(t.writeCodeword(c.UPPER_SHIFT),t.writeCodeword(e-128+1),t.pos++):(t.writeCodeword(e+1),t.pos++)}}}class l extends c{getEncodingMode(){return i.C40}static writeNextTriplet(t,e){t.writeCodewords(this.encodeToCodewords(e)),e.splice(0,3)}static encodeToCodewords(t){const e=1600*t[0]+40*t[1]+t[2]+1;return[Math.floor(e/256),e%256]}encodeChar(t,s){return t==e.space?(s.push(3),1):r.isDigit(t)?(s.push(t-e.num_0+4),1):r.isUpper(t)?(s.push(t-e.A+14),1):t<e.space?(s.push(0),s.push(t),2):t<=e.slash?(s.push(1),s.push(t-(e.space+1)),2):t<=e.at?(s.push(1),s.push(t-(e.num_9+1)+15),2):t<=e._?(s.push(1),s.push(t-91+22),2):t<=127?(s.push(2),s.push(t-96),2):(s.push(1,30),this.encodeChar(t-128,s)+2)}encode(t){const e=[];for(;t.hasMoreCharacters();){const r=t.getCharCode();t.pos++;let s=this.encodeChar(r,e);const o=e.length/3*2,a=t.Codewords.length+o;t.updateSymbolInfo(a);const n=t.SymbolInfo.DataCodewords-a;if(!t.hasMoreCharacters()){const r=[];for(e.length%3==2&&2!=n&&(s=this.backtrackOneCharacter(t,e,r,s));e.length%3==1&&(s>3||1!=n);)s=this.backtrackOneCharacter(t,e,r,s);break}if(e.length%3==0){if(c.lookAheadTest(t.ByteArray,t.pos,this.getEncodingMode())!=this.getEncodingMode()){t.signalEncoderChange(i.ASCII);break}}}this.handleEOD(t,e)}backtrackOneCharacter(t,e,r,s){const o=e.length;e.splice(o-s),t.pos--;const i=t.getCharCode();return s=this.encodeChar(i,r),t.resetSymbolInfo(),s}handleEOD(t,e){const r=2*Math.floor(e.length/3),s=e.length%3,o=t.Codewords.length+r;t.updateSymbolInfo(o);const a=t.SymbolInfo.DataCodewords-o;if(2==s){for(e.push(0);e.length>=3;)l.writeNextTriplet(t,e);t.hasMoreCharacters()&&t.writeCodeword(c.C40_UNLATCH)}else if(1==a&&1==s){for(;e.length>=3;)l.writeNextTriplet(t,e);t.hasMoreCharacters()&&t.writeCodeword(c.C40_UNLATCH),t.pos--}else{if(0!=s)throw new Error("Unexpected case. Please report!");for(;e.length>=3;)l.writeNextTriplet(t,e);(a>0||t.hasMoreCharacters())&&t.writeCodeword(c.C40_UNLATCH)}t.signalEncoderChange(i.ASCII)}}let d=class extends l{getEncodingMode(){return i.TEXT}encodeChar(t,s){return t===e.space?(s.push(3),1):r.isDigit(t)?(s.push(t-e.num_0+4),1):r.isLower(t)?(s.push(t-e.a+14),1):t<e.space?(s.push(0),s.push(t),2):t<=e.slash?(s.push(1),s.push(t-33),2):t<=e.at?(s.push(1),s.push(t-58+15),2):t>="[".charCodeAt(0)&&t<=e._?(s.push(1),s.push(t-91+22),2):t=="`".charCodeAt(0)?(s.push(2),s.push(0),2):t<=e.Z?(s.push(2),s.push(t-e.A+1),2):t<=127?(s.push(2),s.push(t-123+27),2):(s.push(1,30),this.encodeChar(t-128,s)+2)}};class C extends l{getEncodingMode(){return i.X12}encodeChar(t,s){switch(t){case e.LF:s.push(0);break;case"*".charCodeAt(0):s.push(1);break;case">".charCodeAt(0):s.push(2);break;case e.space:s.push(3);break;default:r.isDigit(t)?s.push(t-e.num_0+4):r.isUpper(t)?s.push(t-e.A+14):c.illegalCharacter(t)}return 1}encode(t){const e=[];for(e.push(c.LATCH_TO_TEXT);t.hasMoreCharacters();){const r=t.getCharCode();if(t.pos++,this.encodeChar(r,e),e.length%3==0){l.writeNextTriplet(t,e);if(c.lookAheadTest(t.ByteArray,t.pos,this.getEncodingMode())!=this.getEncodingMode()){t.signalEncoderChange(i.ASCII);break}}}this.handleEOD(t,e)}handleEOD(t,e){t.updateSymbolInfo();const r=t.SymbolInfo.DataCodewords-t.Codewords.length,s=e.length;t.pos-=s,(t.remainingChars>1||r>1||t.remainingChars!=r)&&t.writeCodeword(i.X12),(void 0===t.Encoding||t.Encoding<0)&&t.signalEncoderChange(i.ASCII)}}class u extends c{getEncodingMode(){return i.EDIFACT}encodeChar(t,r){if(t>=e.space&&t<=e.question)r.push(t);else{if(!(t>=e.at&&t<=e.caret))return c.illegalCharacter(t),0;r.push(t-e.at)}return 1}encode(t){const e=[];for(;t.hasMoreCharacters();){const r=t.getCharCode();this.encodeChar(r,e),t.pos++;if(e.length>=4){t.writeCodewords(l.encodeToCodewords(e)),e.splice(0,4);if(c.lookAheadTest(t.ByteArray,t.pos,this.getEncodingMode())!=this.getEncodingMode()){t.signalEncoderChange(i.ASCII);break}}}e.push(31),this.handleEOD(t,e)}handleEOD(t,e){try{const r=e.length;if(0==r)return;if(1==r){t.updateSymbolInfo();let e=t.SymbolInfo.DataCodewords-t.Codewords.length;const r=t.remainingChars;if(r>e&&(t.updateSymbolInfo(t.Codewords.length+1),e=t.SymbolInfo.DataCodewords-t.Codewords.length),r<=e&&e<=2)return}if(r>4)throw new Error("Count must not exceed 4");const s=r-1,o=l.encodeToCodewords(e);let i=!t.hasMoreCharacters()&&s<=2;if(s<=2){t.updateSymbolInfo(t.Codewords.length+s);t.SymbolInfo.DataCodewords-t.Codewords.length>=3&&(i=!1,t.updateSymbolInfo(t.Codewords.length+o.length))}i?(t.resetSymbolInfo(),t.pos-=s):t.writeCodewords(o)}finally{t.signalEncoderChange(i.ASCII)}}}class p extends c{getEncodingMode(){return i.BASE256}encode(t){const e=[];for(e.push(0);t.hasMoreCharacters();){const r=t.getCharCode();e.push(r),t.pos++;if(c.lookAheadTest(t.ByteArray,t.pos,this.getEncodingMode())!=this.getEncodingMode()){t.signalEncoderChange(i.ASCII);break}}const r=e.length-1,s=t.Codewords.length+r+1;t.updateSymbolInfo(s);const o=t.SymbolInfo.DataCodewords-s>0;if(t.hasMoreCharacters()||o)if(r<=249)e[0]=r;else{if(!(r<=1555))throw new Error("Message length not in valid ranges: "+r);e[0]=Math.floor(r/250)+249,e.splice(1,0,r%250)}for(let r=0,s=e.length;r<s;r++)t.writeCodeword(p.randomize255State(e[r],t.Codewords.length+1))}static randomize255State(t,e){const r=t+(149*e%255+1);return r<=255?r:r-256}static ComputeBase256Codeword(t,e){const r=t+(149*(e+1)%255+1);return r<=255?r:r-256}}class A{static randomize253State(t){const e=149*t%253+1,r=c.PAD+e;return r<=254?r:r-254}static PadCodewords(t,e){if(t<=e.length)return e;const r=new Uint8Array(t);r.set(e);let s=e.length;for(r[s++]=c.PAD;s<t;s++)r[s]=A.randomize253State(s+1);return r}static encodeHighLevel(t,e,r){const s=new n(t,e,r);let o=i.ASCII;for(;s.hasMoreCharacters();){c.getEncoder(o).encode(s);const t=s.Encoding;void 0!==t&&(o=t,s.resetEncoderSignal())}s.updateSymbolInfo();const a=s.SymbolInfo.DataCodewords;s.Codewords.length<a&&(o===i.C40||o===i.TEXT||o===i.X12)&&s.writeCodeword(c.C40_UNLATCH);const h=s.getCodewords();return h.length<a?this.PadCodewords(a,h):h}static determineConsecutiveDigitCount(t,e){const s=t.length;let o=e;for(;o<s&&r.isDigit(t.charCodeAt(o));)o++;return o-e}static illegalCharacter(t){let e=t.toString(16);throw e="0000".substring(0,4-e.length)+e,new Error("Illegal character: "+t+" (0x"+e+")")}}var g;!function(t){t[t.OK=0]="OK",t[t.ERROR_NO_DATA=1]="ERROR_NO_DATA",t[t.ERROR_DATA_ENCODE=2]="ERROR_DATA_ENCODE",t[t.INVALID_ASCIINUMBERIC_CHARACTERS=3]="INVALID_ASCIINUMBERIC_CHARACTERS",t[t.DATA_CANNOT_ENCODE=4]="DATA_CANNOT_ENCODE",t[t.DATA_TOO_LONG=5]="DATA_TOO_LONG",t[t.ERROR_IN_CORRECTION_CODE=6]="ERROR_IN_CORRECTION_CODE"}(g||(g={}));class m{static getInstance(){return this._instance||(this._instance=new m)}static register(t){t&&t.register(this.getInstance())}static get ErrorCode(){return this.mErrorCode}static checkdata(t){let r=!1;for(let s=0;s<t.length;s++)(t.charCodeAt(s)>=this.CHAR_CODE_VISUAL_FIRST&&t.charCodeAt(s)<=this.CHAR_CODE_VISUAL_LAST||t.charCodeAt(s)===e.LF||t.charCodeAt(s)===e.CR)&&(r=!0);return r}static checkBase256(t){for(let e=0;e<t.length;e++)if(t.charCodeAt(e)>255)return!1;return!0}static getErrorMessage(){switch(this.ErrorCode){default:return"Unkown error!";case g.INVALID_ASCIINUMBERIC_CHARACTERS:return"Non-numeric characters detected";case g.DATA_CANNOT_ENCODE:return"Data cannot be encoded as barcode";case g.DATA_TOO_LONG:return"Data too long for barcode size.";case g.ERROR_IN_CORRECTION_CODE:return"Error in error correction code generation!"}}static EccProduct(t,e){if(0===t||0===e)return 0;var r=Array(256);r=this.CreateLogArrays(!0);Array(256);return this.CreateLogArrays(!1)[(r[t]+r[e])%255]}static ComputeErrorCorrection(t,e){const r=e.CorrectionCodewords;let s=new Uint8Array(r+e.DataCodewords);var o=e.InterleavedBlock,i=e.DataCodewords,a=e.CorrectionCodewords/o,n=i+a*o,c=this.CreateRSPolynomial(o,e);const h=new Uint8Array(68);for(let r=0;r<o;r++){for(let t=0;t<h.length;t++)h[t]=0;for(let e=r;e<i;e+=o){const r=this.EccSum(h[a-1],t[e]);for(var l=a-1;l>0;l--)h[l]=this.EccSum(h[l-1],this.EccProduct(c[l],r));h[0]=this.EccProduct(c[0],r)}let C=0;if(r>=8)C=e.DataCodewords/o;else{C=e.InterleavedDataBlock;var d=a;for(let t=r+o*C;t<n;t+=o)s[t]=h[--d];if(0!==d)return void(this.mErrorCode=g.ERROR_IN_CORRECTION_CODE)}}if(s.length>r){const t=s;s=new Uint8Array(r);for(let r=0,o=t.length-1;o>e.DataCodewords;o--)s[r++]=t[o]}return s.reverse()}static CreateLogArrays(t){const e=Array(256),r=Array(256);e[0]=-255,r[0]=1;for(let t=1;t<=255;t++)r[t]=2*r[t-1],r[t]>=256&&(r[t]=301^r[t]),e[r[t]]=t;return t?e:r}static EccSum(t,e){return t^e}static EccDoublify(t,e){if(0===t)return 0;if(0===e)return t;const r=this.CreateLogArrays(!0);return this.CreateLogArrays(!1)[(r[t]+e)%255]}static CreateRSPolynomial(t,e){const r=Array(69),s=e.CorrectionCodewords/t;for(let t=0;t<r.length;t++)r[t]=1;for(let t=1;t<=s;t++)for(let e=t-1;e>=0;e--)r[e]=this.EccDoublify(r[e],t),e>0&&(r[e]=this.EccSum(r[e],r[e-1]));return r}static encodeECC200(t,e){const r=this.ComputeErrorCorrection(t,e);if(!r||r.length<=0)return;const s=new Uint8Array(t.length+r.length);return s.set(t),s.set(r,t.length),s}static ecc200placementbit(t,e,r,s,o,i,a){s<0&&(s+=e,o+=4-(e+4)%8),o<0&&(o+=r,s+=4-(r+4)%8),t[s*r+o]=(i<<3)+a}static ecc200placementblock(t,e,r,s,o,i){this.ecc200placementbit(t,e,r,s-2,o-2,i,7),this.ecc200placementbit(t,e,r,s-2,o-1,i,6),this.ecc200placementbit(t,e,r,s-1,o-2,i,5),this.ecc200placementbit(t,e,r,s-1,o-1,i,4),this.ecc200placementbit(t,e,r,s-1,o-0,i,3),this.ecc200placementbit(t,e,r,s-0,o-2,i,2),this.ecc200placementbit(t,e,r,s-0,o-1,i,1),this.ecc200placementbit(t,e,r,s-0,o-0,i,0)}static ecc200placementcornerD(t,e,r,s){this.ecc200placementbit(t,e,r,e-1,0,s,7),this.ecc200placementbit(t,e,r,e-1,r-1,s,6),this.ecc200placementbit(t,e,r,0,r-3,s,5),this.ecc200placementbit(t,e,r,0,r-2,s,4),this.ecc200placementbit(t,e,r,0,r-1,s,3),this.ecc200placementbit(t,e,r,1,r-3,s,2),this.ecc200placementbit(t,e,r,1,r-2,s,1),this.ecc200placementbit(t,e,r,1,r-1,s,0)}static ecc200placementcornerA(t,e,r,s){this.ecc200placementbit(t,e,r,e-1,0,s,7),this.ecc200placementbit(t,e,r,e-1,1,s,6),this.ecc200placementbit(t,e,r,e-1,2,s,5);this.ecc200placementbit(t,e,r,0,r-2,s,4),this.ecc200placementbit(t,e,r,0,r-1,s,3);this.ecc200placementbit(t,e,r,1,r-1,s,2),this.ecc200placementbit(t,e,r,2,r-1,s,1),this.ecc200placementbit(t,e,r,3,r-1,s,0)}static ecc200placementcornerB(t,e,r,s){this.ecc200placementbit(t,e,r,e-3,0,s,7),this.ecc200placementbit(t,e,r,e-2,0,s,6),this.ecc200placementbit(t,e,r,e-1,0,s,5),this.ecc200placementbit(t,e,r,0,r-4,s,4),this.ecc200placementbit(t,e,r,0,r-3,s,3),this.ecc200placementbit(t,e,r,0,r-2,s,2),this.ecc200placementbit(t,e,r,0,r-1,s,1),this.ecc200placementbit(t,e,r,1,r-1,s,0)}static ecc200placementcornerC(t,e,r,s){this.ecc200placementbit(t,e,r,e-3,0,s,7),this.ecc200placementbit(t,e,r,e-2,0,s,6),this.ecc200placementbit(t,e,r,e-1,0,s,5),this.ecc200placementbit(t,e,r,0,r-2,s,4),this.ecc200placementbit(t,e,r,0,r-1,s,3),this.ecc200placementbit(t,e,r,1,r-1,s,2),this.ecc200placementbit(t,e,r,2,r-1,s,1),this.ecc200placementbit(t,e,r,3,r-1,s,0)}static ecc200placement(t,e,r){for(var s,o,i,a=0;a<e;a++)for(var n=0;n<r;n++)t[a*r+n]=0;i=1,s=4,o=0;do{s===e&&0===o&&this.ecc200placementcornerA(t,e,r,i++),s===e-2&&0===o&&r%4!=0&&this.ecc200placementcornerB(t,e,r,i++),s===e-2&&0===o&&r%8==4&&this.ecc200placementcornerC(t,e,r,i++),s===e+4&&2===o&&r%8==0&&this.ecc200placementcornerD(t,e,r,i++);do{s<e&&o>=0&&0===t[s*r+o]&&this.ecc200placementblock(t,e,r,s,o,i++),s-=2,o+=2}while(s>=0&&o<r);s++,o+=3;do{s>=0&&o<r&&0===t[s*r+o]&&this.ecc200placementblock(t,e,r,s,o,i++),s+=2,o-=2}while(s<e&&o>=0);s+=3,o++}while(s<e||o<r);0===t[e*r-1]&&(t[e*r-1]=t[e*r-r-2]=1)}static CreateMatrix(e,r,s){const o=r.SymbolColumn,i=r.SymbolRow,a=o/r.HorizontalDataRegion,n=i/r.VerticalDataRegion,c=o-o/a*2,h=i-i/n*2,l=Array(c*h);this.ecc200placement(l,h,c);const d=new Uint8Array(o*i);for(let t=0;t<i;t+=n){for(let e=0;e<o;e++)d[t*o+e]=1;for(let e=0;e<o;e+=2)d[(t+n-1)*o+e]=1}for(let t=0;t<o;t+=a){for(let e=0;e<i;e++)d[e*o+t]=1;for(let e=0;e<i;e+=2)d[e*o+t+a-1]=1}let C=0;for(let t=0;t<h;t++)for(let r=0;r<c;r++){const s=l[(h-t-1)*c+r];(1===s||s>7&&e[(s>>3)-1]&1<<(7&s))&&(C=(1+t+2*Math.floor(t/(n-2)))*o+1+r+2*Math.floor(r/(a-2)),d[C]=1)}s=s>0?s:m.QUIET_ZONE_DEFAULT;const u=r.SymbolRow+2*s,p=r.SymbolColumn+2*s,A=new t(p,u);for(let t=0;t<i;t++)A.setArray(d.slice(t*o,t*o+o),s+i-t-1,s);return A}static create(t){t=t||{},this.mErrorCode=g.OK;const e=(t.text||"").trim(),r=t.codeShape||o.Auto;if(!e)return void(this.mErrorCode=g.ERROR_NO_DATA);const i=e,n=m.checkBase256(i);let c=s.getBytes(i,!n);const h=A.encodeHighLevel(c,r,t.aspectRatio);if(!h||h.length<=0)return;const l=a.getSymbolInfo(h.length,r,t.aspectRatio);return!l||(c=this.encodeECC200(h,l),!c||c.length<=0)?void 0:this.CreateMatrix(c,l,t.quietZone||0)}get barcodeType(){return"DataMatrix"}encode(t){return m.create(t)}}m.DEBUG=!0,m.CHAR_CODE_VISUAL_FIRST=32,m.CHAR_CODE_VISUAL_LAST=126,m.QUIET_ZONE_DEFAULT=0,m.mErrorCode=0;const f=()=>m.getInstance(),b=t=>{m.register(t)};export{o as DMCodeShape,i as DMEncoding,m as DataMatrix,f as getInstance,b as register};

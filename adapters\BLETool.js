/**
 * 德佟SDK BLETool适配层
 * 适配原有SUPVANAPIT50PRO BLETool接口到德佟SDK
 */

import { LPAPIFactory, LPAPI } from "../js_sdk/lpapi-ble/index";

class BLETool {
  constructor() {
    this.lpapi = null;
    this.isInitialized = false;
    this.currentScanCallback = null;
    this.connectedDevice = null;
  }

  /**
   * 初始化德佟SDK
   */
  async initLPAPI() {
    if (this.isInitialized && this.lpapi) {
      return this.lpapi;
    }

    try {
      // 德佟SDK可能不需要预先指定Canvas，在使用时再获取
      // 初始化LPAPI
      this.lpapi = LPAPIFactory.getInstance({
        logLevel: 2 // 适中的日志级别
      });

      this.isInitialized = true;
      console.log('德佟SDK初始化成功');
      return this.lpapi;
    } catch (error) {
      console.error('德佟SDK初始化失败:', error);
      // 如果初始化失败，尝试简单的初始化
      try {
        this.lpapi = LPAPIFactory.getInstance({});
        this.isInitialized = true;
        console.log('德佟SDK简单初始化成功');
        return this.lpapi;
      } catch (fallbackError) {
        console.error('德佟SDK简单初始化也失败:', fallbackError);
        throw fallbackError;
      }
    }
  }

  /**
   * 扫描蓝牙设备列表
   * @param {Function} callback 设备发现回调函数
   * @returns {Promise}
   */
  async scanBleDeviceList(callback) {
    try {
      await this.initLPAPI();

      this.currentScanCallback = callback;
      this.discoveredDevices = new Map(); // 用于去重和管理设备
      this.scanTimeout = null;
      this.autoConnectTimeout = null;

      return new Promise((resolve, reject) => {
        // 设置10秒扫描超时
        this.scanTimeout = setTimeout(() => {
          console.log('德佟P2扫描超时，未发现设备');
          this.stopScanBleDevices();
          if (this.discoveredDevices.size === 0) {
            reject({ ResultCode: 104, message: '扫描超时，未发现设备' });
          }
        }, 10000);

        this.lpapi.startBleDiscovery({
          timeout: 0, // 持续扫描
          deviceFound: (devices) => {
            console.log('德佟P2发现设备:', devices);
            if (devices && devices.length > 0) {
              devices.forEach(device => {
                // 使用deviceId作为唯一标识，避免重复设备
                if (!this.discoveredDevices.has(device.deviceId)) {
                  this.discoveredDevices.set(device.deviceId, device);

                  const adaptedResponse = {
                    ResultCode: 0,
                    ResultValue: {
                      devices: [device]
                    }
                  };

                  if (callback) {
                    callback(adaptedResponse);
                  }
                }
              });

              // 智能连接逻辑：发现设备后等待1秒
              this.handleSmartConnection();
            }
          },
          success: () => {
            console.log('德佟P2开始扫描蓝牙设备');
            resolve({ ResultCode: 0, message: '扫描开始' });
          },
          fail: (error) => {
            console.error('德佟P2扫描失败:', error);
            this.clearTimeouts();
            reject({ ResultCode: 103, message: '开始搜寻附近的蓝牙外围设备异常', error });
          }
        });
      });
    } catch (error) {
      console.error('扫描蓝牙设备异常:', error);
      throw { ResultCode: 103, message: '开始搜寻附近的蓝牙外围设备异常', error };
    }
  }

  /**
   * 智能连接处理：等待1秒后决定是否自动连接
   */
  handleSmartConnection() {
    // 清除之前的自动连接定时器
    if (this.autoConnectTimeout) {
      clearTimeout(this.autoConnectTimeout);
    }

    // 等待1秒，看是否有更多设备
    this.autoConnectTimeout = setTimeout(() => {
      const deviceCount = this.discoveredDevices.size;
      console.log(`德佟P2发现${deviceCount}台设备`);

      if (deviceCount === 1) {
        // 只有一台设备，自动连接
        const device = Array.from(this.discoveredDevices.values())[0];
        console.log('德佟P2自动连接唯一设备:', device.name);
        this.autoConnectDevice(device);
      } else if (deviceCount > 1) {
        // 多台设备，显示选择对话框
        console.log('德佟P2发现多台设备，显示选择对话框');
        this.showDeviceSelectionDialog();
      }
    }, 1000);
  }

  /**
   * 自动连接设备
   */
  async autoConnectDevice(device) {
    try {
      console.log('德佟P2开始自动连接设备:', device.name);
      await this.stopScanBleDevices();

      wx.showLoading({
        title: '正在连接设备...',
        mask: true
      });

      const result = await this.connectBleDevice(device);
      wx.hideLoading();

      if (result.ResultCode === 0) {
        wx.showToast({
          title: '设备连接成功',
          icon: 'success'
        });

        // 通知页面连接成功
        if (this.onAutoConnectSuccess) {
          this.onAutoConnectSuccess(device);
        }
      }
    } catch (error) {
      wx.hideLoading();
      console.error('德佟P2自动连接失败:', error);
      wx.showToast({
        title: '连接失败，请手动重试',
        icon: 'none'
      });
    }
  }

  /**
   * 显示设备选择对话框
   */
  showDeviceSelectionDialog() {
    const devices = Array.from(this.discoveredDevices.values());
    const deviceNames = devices.map(device => device.name || device.deviceId);

    wx.showActionSheet({
      itemList: deviceNames,
      success: async (res) => {
        const selectedDevice = devices[res.tapIndex];
        console.log('德佟P2用户选择设备:', selectedDevice.name);

        await this.stopScanBleDevices();

        try {
          wx.showLoading({
            title: '正在连接设备...',
            mask: true
          });

          const result = await this.connectBleDevice(selectedDevice);
          wx.hideLoading();

          if (result.ResultCode === 0) {
            wx.showToast({
              title: '设备连接成功',
              icon: 'success'
            });

            if (this.onAutoConnectSuccess) {
              this.onAutoConnectSuccess(selectedDevice);
            }
          }
        } catch (error) {
          wx.hideLoading();
          console.error('德佟P2连接选择的设备失败:', error);
          wx.showToast({
            title: '连接失败',
            icon: 'error'
          });
        }
      },
      fail: () => {
        console.log('德佟P2用户取消设备选择');
      }
    });
  }

  /**
   * 清除所有定时器
   */
  clearTimeouts() {
    if (this.scanTimeout) {
      clearTimeout(this.scanTimeout);
      this.scanTimeout = null;
    }
    if (this.autoConnectTimeout) {
      clearTimeout(this.autoConnectTimeout);
      this.autoConnectTimeout = null;
    }
  }

  /**
   * 停止扫描蓝牙设备
   * @returns {Promise}
   */
  async stopScanBleDevices() {
    try {
      if (this.lpapi) {
        await this.lpapi.stopBleDiscovery();
        console.log('停止扫描蓝牙设备');
        return { ResultCode: 117, message: '停止搜索蓝牙设备成功' };
      }
      return { ResultCode: 117, message: '停止搜索蓝牙设备成功' };
    } catch (error) {
      console.error('停止扫描失败:', error);
      throw { ResultCode: 106, message: '断开蓝牙失败异常', error };
    }
  }

  /**
   * 连接蓝牙设备
   * @param {Object} device 设备对象
   * @returns {Promise}
   */
  async connectBleDevice(device) {
    try {
      await this.initLPAPI();

      return new Promise((resolve, reject) => {
        this.lpapi.openPrinter({
          name: device.name,
          deviceId: device.deviceId,
          success: (result) => {
            console.log('德佟P2连接设备成功:', result);
            this.connectedDevice = device;
            resolve({ ResultCode: 0, message: '连接成功', device });
          },
          fail: (error) => {
            console.error('德佟P2连接设备失败:', error);
            reject({ ResultCode: 109, message: '连接蓝牙异常', error });
          }
        });
      });
    } catch (error) {
      console.error('连接蓝牙设备异常:', error);
      throw { ResultCode: 109, message: '连接蓝牙异常', error };
    }
  }

  /**
   * 断开蓝牙设备连接
   * @returns {Promise}
   */
  async disconnectBleDevice() {
    try {
      if (this.lpapi) {
        await this.lpapi.closePrinter();
        this.connectedDevice = null;
        console.log('断开蓝牙连接');
        return { ResultCode: 0, message: '断开连接成功' };
      }
      return { ResultCode: 0, message: '断开连接成功' };
    } catch (error) {
      console.error('断开连接失败:', error);
      throw { ResultCode: 106, message: '断开蓝牙失败异常', error };
    }
  }

  /**
   * 停止打印
   * @param {Function} callback 回调函数
   * @returns {Promise}
   */
  async stopPrint(callback) {
    try {
      if (this.lpapi) {
        // 德佟SDK可能没有直接的停止打印方法，这里模拟实现
        console.log('停止打印');
        const result = { ResultCode: 0, message: '停止打印成功' };
        if (callback) {
          callback(result);
        }
        return result;
      }
      const result = { ResultCode: 0, message: '停止打印成功' };
      if (callback) {
        callback(result);
      }
      return result;
    } catch (error) {
      console.error('停止打印失败:', error);
      const errorResult = { ResultCode: 132, message: '打印异常终止', error };
      if (callback) {
        callback(errorResult);
      }
      throw errorResult;
    }
  }

  /**
   * 获取当前连接的设备
   */
  getConnectedDevice() {
    return this.connectedDevice;
  }

  /**
   * 检查是否已连接设备
   */
  isConnected() {
    return !!this.connectedDevice;
  }
}

// 创建单例实例
const bleTool = new BLETool();

export default bleTool;

/**
 * 打印机状态码配置文件
 * 包含所有打印机相关的状态码和错误信息
 */

const printerStatusConfig = {
  // 状态码映射表
  statusCodeMap: {
    // 成功状态
    0: '请求成功',
    1: '请求成功',

    // 模板相关
    100: '模板信息回调',

    // 蓝牙相关错误 (101-117)
    101: '初始化蓝牙模块异常',
    102: '获取本机蓝牙适配器状态异常',
    103: '开始搜寻附近的蓝牙外围设备异常',
    104: '蓝牙寻找到新设备的事件异常',
    105: '蓝牙适配器不可用',
    106: '断开蓝牙失败异常',
    107: '蓝牙序列号为空',
    108: '文本Canvas不能为空',
    109: '连接蓝牙异常',
    110: '连接硕方蓝牙异常',
    111: 'blemtu异常',
    112: '获取蓝牙设备服务异常',
    113: '获取蓝牙特征值异常',
    114: '获取特征值变化异常',
    115: '获取notify异常',
    116: '模板对象不能为空',
    117: '停止搜索蓝牙设备成功',

    // 图像处理相关错误 (118-124)
    118: '获取条形码对象数据异常',
    119: '生成图片失败异常',
    120: '二维码转换成图片异常',
    121: '图片下载异常',
    122: '获取rgba字模数据异常',
    123: '下载本地图片异常',
    124: '生成图片数据异常',

    // 打印相关错误 (125-135)
    125: '机器启动打印异常',
    126: '请关闭耗材仓盖',
    127: '耗材未装好',
    128: '请检查耗材余量',
    129: '未检测到耗材',
    130: '未识别到耗材',
    131: '耗材已用完',
    132: '打印异常终止',
    133: '色带错误',
    134: '压缩失败',
    135: '打印字模数据不能为空',

    // 德佟P2专用错误码 (200-220) - 基于德佟SDK LPA_Result
    200: '参数错误',
    201: '未检测到打印机或者未指定打印机',
    202: '打印机未连接',
    203: '打印机连接失败',
    204: '数据Notify特征值启动失败',
    205: '数据发送失败',
    206: '数据接收异常，打印机无响应',
    207: '打印机正在打印过程中',
    208: '指令发送响应超时',
    209: '打印任务创建失败',
    210: '打印任务被取消',
    211: '打印数据获取失败',
    220: '其他未知异常'
  },

  // 状态码分类
  statusCategories: {
    // 成功状态
    success: [0, 1, 100, 117],

    // 蓝牙相关错误
    bluetooth: [101, 102, 103, 104, 105, 106, 107, 109, 110, 111, 112, 113, 114, 115, 202, 203, 204, 205, 206, 208],

    // 数据处理错误
    dataProcessing: [108, 116, 118, 119, 120, 121, 122, 123, 124, 134, 135, 200, 209, 211],

    // 硬件/耗材错误
    hardware: [125, 126, 127, 128, 129, 130, 131, 132, 133, 201, 207, 210, 220]
  },

  // 错误级别定义
  errorLevels: {
    // 信息级别（成功、通知等）
    info: [0, 1, 100, 117],

    // 警告级别（可恢复的错误）
    warning: [105, 107, 108, 116, 126, 127, 128, 135, 200, 207, 210],

    // 错误级别（需要用户干预）
    error: [101, 102, 103, 104, 106, 109, 110, 111, 112, 113, 114, 115, 118, 119, 120, 121, 122, 123, 124, 125, 129, 130, 131, 132, 133, 134, 201, 202, 203, 204, 205, 206, 208, 209, 211, 220]
  },

  // 用户友好的错误提示
  userFriendlyMessages: {
    // 蓝牙连接问题
    101: '蓝牙初始化失败，请重启应用后重试',
    102: '无法获取蓝牙状态，请检查设备蓝牙功能',
    103: '搜索设备失败，请确保打印机已开启',
    104: '发现新设备时出错，请重新搜索',
    105: '蓝牙不可用，请开启蓝牙后重试',
    106: '断开连接失败，请重启应用',
    107: '设备序列号为空，请重新连接设备',
    109: '连接打印机失败，请检查设备状态',
    110: '连接硕方设备失败，请重试',

    // 耗材相关问题
    126: '请关闭耗材仓盖后重试',
    127: '耗材未正确安装，请重新安装',
    128: '耗材余量不足，请更换新耗材',
    129: '未检测到耗材，请安装耗材',
    130: '无法识别耗材类型，请使用原装耗材',
    131: '耗材已用完，请更换新耗材',
    132: '打印过程中断，请检查设备状态',
    133: '色带错误，请检查色带安装',

    // 数据处理问题
    108: '数据处理错误，请重新生成标签',
    116: '模板数据错误，请重新选择模板',
    119: '图片生成失败，请重试',
    135: '打印数据为空，请检查标签内容',

    // 德佟P2专用错误提示
    200: '参数设置错误，请检查打印参数',
    201: '未找到打印机，请确保设备已开启并在范围内',
    202: '打印机未连接，请先连接设备',
    203: '连接打印机失败，请重新尝试连接',
    204: '设备通信初始化失败，请重启设备',
    205: '数据发送失败，请检查设备连接',
    206: '设备无响应，请检查打印机状态',
    207: '打印机正在工作中，请等待当前任务完成',
    208: '设备响应超时，请检查连接状态',
    209: '创建打印任务失败，请重试',
    210: '打印任务已取消',
    211: '获取打印数据失败，请重新生成',
    220: '设备出现未知错误，请重启设备后重试'
  }
};

/**
 * 获取状态码对应的消息
 * @param {number} code 状态码
 * @param {boolean} userFriendly 是否返回用户友好的消息
 * @returns {string} 状态消息
 */
function getStatusMessage(code, userFriendly = false) {
  if (userFriendly && printerStatusConfig.userFriendlyMessages[code]) {
    return printerStatusConfig.userFriendlyMessages[code];
  }
  return printerStatusConfig.statusCodeMap[code] || `未知状态码: ${code}`;
}

/**
 * 获取状态码的错误级别
 * @param {number} code 状态码
 * @returns {string} 错误级别: info, warning, error
 */
function getErrorLevel(code) {
  const levels = printerStatusConfig.errorLevels;
  if (levels.info.includes(code)) return 'info';
  if (levels.warning.includes(code)) return 'warning';
  if (levels.error.includes(code)) return 'error';
  return 'unknown';
}

/**
 * 获取状态码的分类
 * @param {number} code 状态码
 * @returns {string} 分类: success, bluetooth, dataProcessing, hardware
 */
function getStatusCategory(code) {
  const categories = printerStatusConfig.statusCategories;
  for (const [category, codes] of Object.entries(categories)) {
    if (codes.includes(code)) return category;
  }
  return 'unknown';
}

/**
 * 判断是否为成功状态
 * @param {number} code 状态码
 * @returns {boolean} 是否成功
 */
function isSuccessStatus(code) {
  return printerStatusConfig.statusCategories.success.includes(code);
}

/**
 * 德佟SDK错误码映射到标准错误码
 * @param {number} detongCode 德佟SDK错误码 (LPA_Result)
 * @returns {number} 标准错误码
 */
function mapDetongErrorCode(detongCode) {
  // 德佟SDK LPA_Result 到标准错误码的映射
  const detongMapping = {
    0x00: 0,   // OK -> 请求成功
    0x01: 200, // ERROR_PARAM -> 参数错误
    0x02: 201, // ERROR_NO_PRINTER -> 未检测到打印机
    0x03: 202, // ERROR_DISCONNECTED -> 打印机未连接
    0x04: 203, // ERROR_CONNECT_FAILED -> 打印机连接失败
    0x05: 204, // ERROR_START_NOTIFICATION -> 数据Notify特征值启动失败
    0x06: 205, // ERROR_DATA_SEND_ERROR -> 数据发送失败
    0x07: 206, // ERROR_DATA_RECEIVE_ERROR -> 数据接收异常
    0x08: 207, // ERROR_IS_PRINTING -> 打印机正在打印
    0x09: 208, // ERROR_RESPONSE_TIMEOUT -> 指令发送响应超时
    0x10: 209, // ERROR_JOB_CREATE -> 打印任务创建失败
    0x11: 210, // ERROR_JOB_CANCELED -> 打印任务被取消
    0x12: 211, // ERROR_GET_IMAGE_DATA -> 打印数据获取失败
    0x20: 220, // ERROR_OTHER -> 其他未知异常
    '-1': 207  // ASYNC_WAIT -> 异步等待中（映射为正在打印）
  };

  return detongMapping[detongCode] || detongCode;
}

/**
 * 根据德佟SDK错误码获取用户友好的错误信息
 * @param {number} detongCode 德佟SDK错误码
 * @returns {string} 用户友好的错误信息
 */
function getDetongErrorMessage(detongCode) {
  const standardCode = mapDetongErrorCode(detongCode);
  return getStatusMessage(standardCode, true);
}

module.exports = {
  printerStatusConfig,
  getStatusMessage,
  getErrorLevel,
  getStatusCategory,
  isSuccessStatus,
  mapDetongErrorCode,
  getDetongErrorMessage
};

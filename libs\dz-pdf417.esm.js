const t=[[65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,32,253,254,255],[97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,32,253,254,255],[48,49,50,51,52,53,54,55,56,57,38,13,9,44,58,35,45,46,36,47,43,37,42,61,94,251,32,253,254,255],[59,60,62,64,91,92,93,95,96,126,33,13,9,44,58,10,45,46,36,47,34,124,42,40,41,63,123,125,39,255]],e=[[120256,125680,128380,120032,125560,128318,108736,119920,108640,86080,108592,86048,110016,120560,125820,109792,120440,125758,88256,109680,88160,89536,110320,120700,89312,110200,120638,89200,110140,89840,110460,89720,110398,89980,128506,119520,125304,128190,107712,119408,125244,107616,119352,84032,107568,119324,84e3,107544,83984,108256,119672,125374,85184,108144,119612,85088,108088,119582,85040,108060,85728,108408,119742,85616,108348,85560,108318,85880,108478,85820,85790,107200,119152,125116,107104,119096,125086,83008,107056,119068,82976,107032,82960,82952,83648,107376,119228,83552,107320,119198,83504,107292,83480,83468,83824,107452,83768,107422,83740,83900,106848,118968,125022,82496,106800,118940,82464,106776,118926,82448,106764,82440,106758,82784,106936,119006,82736,106908,82712,106894,82700,82694,106974,82830,82240,106672,118876,82208,106648,118862,82192,106636,82184,106630,82180,82352,82328,82316,82080,118830,106572,106566,82050,117472,124280,127678,103616,117360,124220,103520,117304,124190,75840,103472,75808,104160,117624,124350,76992,104048,117564,76896,103992,76848,76824,77536,104312,117694,77424,104252,77368,77340,77688,104382,77628,77758,121536,126320,128700,121440,126264,128670,111680,121392,126236,111648,121368,126222,111632,121356,103104,117104,124092,112320,103008,117048,124062,112224,121656,126366,93248,74784,102936,117006,93216,112152,93200,75456,103280,117180,93888,75360,103224,117150,93792,112440,121758,93744,75288,93720,75632,103356,94064,75576,103326,94008,112542,93980,75708,94140,75678,94110,121184,126136,128606,111168,121136,126108,111136,121112,126094,111120,121100,111112,111108,102752,116920,123998,111456,102704,116892,91712,74272,121244,116878,91680,74256,102668,91664,111372,102662,74244,74592,102840,116958,92e3,74544,102812,91952,111516,102798,91928,74508,74502,74680,102878,92088,74652,92060,74638,92046,92126,110912,121008,126044,110880,120984,126030,110864,120972,110856,120966,110852,110850,74048,102576,116828,90944,74016,102552,116814,90912,111e3,121038,90896,73992,102534,90888,110982,90884,74160,102620,91056,74136,102606,91032,111054,91020,74118,91014,91100,91086,110752,120920,125998,110736,120908,110728,120902,110724,110722,73888,102488,116782,90528,73872,102476,90512,110796,102470,90504,73860,90500,73858,73944,90584,90572,90566,120876,120870,110658,102444,73800,90312,90308,90306,101056,116080,123580,100960,116024,70720,100912,115996,70688,100888,70672,70664,71360,101232,116156,71264,101176,116126,71216,101148,71192,71180,71536,101308,71480,101278,71452,71612,71582,118112,124600,127838,105024,118064,124572,104992,118040,124558,104976,118028,104968,118022,100704,115896,123486,105312,100656,115868,79424,70176,118172,115854,79392,105240,100620,79376,70152,79368,70496,100792,115934,79712,70448,118238,79664,105372,100750,79640,70412,79628,70584,100830,79800,70556,79772,70542,70622,79838,122176,126640,128860,122144,126616,128846,122128,126604,122120,126598,122116,104768,117936,124508,113472,104736,126684,124494,113440,122264,126670,113424,104712,117894,113416,122246,104706,69952,100528,115804,78656,69920,100504,115790,96064,78624,104856,117966,96032,113560,122318,100486,96016,78600,104838,96008,69890,70064,100572,78768,70040,100558,96176,78744,104910,96152,113614,70022,78726,70108,78812,70094,96220,78798,122016,126552,128814,122e3,126540,121992,126534,121988,121986,104608,117848,124462,113056,104592,126574,113040,122060,117830,113032,104580,113028,104578,113026,69792,100440,115758,78240,69776,100428,95136,78224,104652,100422,95120,113100,69764,95112,78212,69762,78210,69848,100462,78296,69836,95192,78284,69830,95180,78278,69870,95214,121936,126508,121928,126502,121924,121922,104528,117804,112848,104520,117798,112840,121958,112836,104514,112834,69712,100396,78032,69704,100390,94672,78024,104550,94664,112870,69698,94660,78018,94658,78060,94700,94694,126486,121890,117782,104484,104482,69672,77928,94440,69666,77922,99680,68160,99632,68128,99608,115342,68112,99596,68104,99590,68448,99768,115422,68400,99740,68376,99726,68364,68358,68536,99806,68508,68494,68574,101696,116400,123740,101664,116376,101648,116364,101640,116358,101636,67904,99504,115292,72512,67872,116444,115278,72480,101784,116430,72464,67848,99462,72456,101766,67842,68016,99548,72624,67992,99534,72600,101838,72588,67974,68060,72668,68046,72654,118432,124760,127918,118416,124748,118408,124742,118404,118402,101536,116312,105888,101520,116300,105872,118476,116294,105864,101508,105860,101506,105858,67744,99416,72096,67728,116334,80800,72080,101580,99398,80784,105932,67716,80776,72068,67714,72066,67800,99438,72152,67788,80856,72140,67782,80844,72134,67822,72174,80878,126800,128940,126792,128934,126788,126786,118352,124716,122576,126828,124710,122568,126822,122564,118338,122562,101456,116268,105680,101448,116262,114128,105672,118374,114120,122598,101442,114116,105666,114114,67664,99372,71888,67656,99366,80336,71880,101478,97232,80328,105702,67650,97224,114150,71874,97220,67692,71916,67686,80364,71910,97260,80358,97254,126760,128918,126756,126754,118312,124694,122472,126774,122468,118306,122466,101416,116246,105576,101412,113896,105572,101410,113892,105570,113890,67624,99350,71784,101430,80104,71780,67618,96744,80100,71778,96740,80098,96738,71798,96758,126738,122420,122418,105524,113780,113778,71732,79988,96500,96498,66880,66848,98968,66832,66824,66820,66992,66968,66956,66950,67036,67022,1e5,99984,115532,99976,115526,99972,99970,66720,98904,69024,100056,98892,69008,100044,69e3,100038,68996,66690,68994,66776,98926,69080,100078,69068,66758,69062,66798,69102,116560,116552,116548,116546,99920,102096,116588,115494,102088,116582,102084,99906,102082,66640,68816,66632,98854,73168,68808,66628,73160,68804,66626,73156,68802,66668,68844,66662,73196,68838,73190,124840,124836,124834,116520,118632,124854,118628,116514,118626,99880,115478,101992,116534,106216,101988,99874,106212,101986,106210,66600,98838,68712,99894,72936,68708,66594,81384,72932,68706,81380,72930,66614,68726,72950,81398,128980,128978,124820,126900,124818,126898,116500,118580,116498,122740,118578,122738,99860,101940,99858,106100,101938,114420],[128352,129720,125504,128304,129692,125472,128280,129678,125456,128268,125448,128262,125444,125792,128440,129758,120384,125744,128412,120352,125720,128398,120336,125708,120328,125702,120324,120672,125880,128478,110144,120624,125852,110112,120600,125838,110096,120588,110088,120582,110084,110432,120760,125918,89664,110384,120732,89632,110360,120718,89616,110348,89608,110342,89952,110520,120798,89904,110492,89880,110478,89868,90040,110558,90012,89998,125248,128176,129628,125216,128152,129614,125200,128140,125192,128134,125188,125186,119616,125360,128220,119584,125336,128206,119568,125324,119560,125318,119556,119554,108352,119728,125404,108320,119704,125390,108304,119692,108296,119686,108292,108290,85824,108464,119772,85792,108440,119758,85776,108428,85768,108422,85764,85936,108508,85912,108494,85900,85894,85980,85966,125088,128088,129582,125072,128076,125064,128070,125060,125058,119200,125144,128110,119184,125132,119176,125126,119172,119170,107424,119256,125166,107408,119244,107400,119238,107396,107394,83872,107480,119278,83856,107468,83848,107462,83844,83842,83928,107502,83916,83910,83950,125008,128044,125e3,128038,124996,124994,118992,125036,118984,125030,118980,118978,106960,119020,106952,119014,106948,106946,82896,106988,82888,106982,82884,82882,82924,82918,124968,128022,124964,124962,118888,124982,118884,118882,106728,118902,106724,106722,82408,106742,82404,82402,124948,124946,118836,118834,106612,106610,124224,127664,129372,124192,127640,129358,124176,127628,124168,127622,124164,124162,117568,124336,127708,117536,124312,127694,117520,124300,117512,124294,117508,117506,104256,117680,124380,104224,117656,124366,104208,117644,104200,117638,104196,104194,77632,104368,117724,77600,104344,117710,77584,104332,77576,104326,77572,77744,104412,77720,104398,77708,77702,77788,77774,128672,129880,93168,128656,129868,92664,128648,129862,92412,128644,128642,124064,127576,129326,126368,124048,129902,126352,128716,127558,126344,124036,126340,124034,126338,117152,124120,127598,121760,117136,124108,121744,126412,124102,121736,117124,121732,117122,121730,103328,117208,124142,112544,103312,117196,112528,121804,117190,112520,103300,112516,103298,112514,75680,103384,117230,94112,75664,103372,94096,112588,103366,94088,75652,94084,75650,75736,103406,94168,75724,94156,75718,94150,75758,128592,129836,91640,128584,129830,91388,128580,91262,128578,123984,127532,126160,123976,127526,126152,128614,126148,123970,126146,116944,124012,121296,116936,124006,121288,126182,121284,116930,121282,102864,116972,111568,102856,116966,111560,121318,111556,102850,111554,74704,102892,92112,74696,102886,92104,111590,92100,74690,92098,74732,92140,74726,92134,128552,129814,90876,128548,90750,128546,123944,127510,126056,128566,126052,123938,126050,116840,123958,121064,116836,121060,116834,121058,102632,116854,111080,121078,111076,102626,111074,74216,102646,91112,74212,91108,74210,91106,74230,91126,128532,90494,128530,123924,126004,123922,126002,116788,120948,116786,120946,102516,110836,102514,110834,73972,90612,73970,90610,128522,123914,125978,116762,120890,102458,110714,123552,127320,129198,123536,127308,123528,127302,123524,123522,116128,123608,127342,116112,123596,116104,123590,116100,116098,101280,116184,123630,101264,116172,101256,116166,101252,101250,71584,101336,116206,71568,101324,71560,101318,71556,71554,71640,101358,71628,71622,71662,127824,129452,79352,127816,129446,79100,127812,78974,127810,123472,127276,124624,123464,127270,124616,127846,124612,123458,124610,115920,123500,118224,115912,123494,118216,124646,118212,115906,118210,100816,115948,105424,100808,115942,105416,118246,105412,100802,105410,70608,100844,79824,70600,100838,79816,105446,79812,70594,79810,70636,79852,70630,79846,129960,95728,113404,129956,95480,113278,129954,95356,95294,127784,129430,78588,128872,129974,95996,78462,128868,127778,95870,128866,123432,127254,124520,123428,126696,128886,123426,126692,124514,126690,115816,123446,117992,115812,122344,117988,115810,122340,117986,122338,100584,115830,104936,100580,113640,104932,100578,113636,104930,113634,70120,100598,78824,70116,96232,78820,70114,96228,78818,96226,70134,78838,129940,94968,113022,129938,94844,94782,127764,78206,128820,127762,95102,128818,123412,124468,123410,126580,124466,126578,115764,117876,115762,122100,117874,122098,100468,104692,100466,113140,104690,113138,69876,78324,69874,95220,78322,95218,129930,94588,94526,127754,128794,123402,124442,126522,115738,117818,121978,100410,104570,112890,69754,78074,94714,94398,123216,127148,123208,127142,123204,123202,115408,123244,115400,123238,115396,115394,99792,115436,99784,115430,99780,99778,68560,99820,68552,99814,68548,68546,68588,68582,127400,129238,72444,127396,72318,127394,123176,127126,123752,123172,123748,123170,123746,115304,123190,116456,115300,116452,115298,116450,99560,115318,101864,99556,101860,99554,101858,68072,99574,72680,68068,72676,68066,72674,68086,72694,129492,80632,105854,129490,80508,80446,127380,72062,127924,127378,80766,127922,123156,123700,123154,124788,123698,124786,115252,116340,115250,118516,116338,118514,99444,101620,99442,105972,101618,105970,67828,72180,67826,80884,72178,80882,97008,114044,96888,113982,96828,96798,129482,80252,130010,97148,80190,97086,127370,127898,128954,123146,123674,124730,126842,115226,116282,118394,122618,99386,101498,105722,114170,67706,71930,80378,96632,113854,96572,96542,80062,96702,96444,96414,96350,123048,123044,123042,115048,123062,115044,115042,99048,115062,99044,99042,67048,99062,67044,67042,67062,127188,68990,127186,123028,123316,123026,123314,114996,115572,114994,115570,98932,100084,98930,100082,66804,69108,66802,69106,129258,73084,73022,127178,127450,123018,123290,123834,114970,115514,116602,98874,99962,102138,66682,68858,73210,81272,106174,81212,81182,72894,81342,97648,114364,97592,114334,97564,97550,81084,97724,81054,97694,97464,114270,97436,97422,80990,97502,97372,97358,97326,114868,114866,98676,98674,66292,66290,123098,114842,115130,98618,99194,66170,67322,69310,73404,73374,81592,106334,81564,81550,73310,81630,97968,114524,97944,114510,97932,97926,81500,98012,81486,97998,97880,114478,97868,97862,81454,97902,97836,97830,69470,73564,73550,81752,106414,81740,81734,73518,81774,81708,81702],[109536,120312,86976,109040,120060,86496,108792,119934,86256,108668,86136,129744,89056,110072,129736,88560,109820,129732,88312,109694,129730,88188,128464,129772,89592,128456,129766,89340,128452,89214,128450,125904,128492,125896,128486,125892,125890,120784,125932,120776,125926,120772,120770,110544,120812,110536,120806,110532,84928,108016,119548,84448,107768,119422,84208,107644,84088,107582,84028,129640,85488,108284,129636,85240,108158,129634,85116,85054,128232,129654,85756,128228,85630,128226,125416,128246,125412,125410,119784,125430,119780,119778,108520,119798,108516,108514,83424,107256,119166,83184,107132,83064,107070,83004,82974,129588,83704,107390,129586,83580,83518,128116,83838,128114,125172,125170,119284,119282,107508,107506,82672,106876,82552,106814,82492,82462,129562,82812,82750,128058,125050,119034,82296,106686,82236,82206,82366,82108,82078,76736,103920,117500,76256,103672,117374,76016,103548,75896,103486,75836,129384,77296,104188,129380,77048,104062,129378,76924,76862,127720,129398,77564,127716,77438,127714,124392,127734,124388,124386,117736,124406,117732,117730,104424,117750,104420,104418,112096,121592,126334,92608,111856,121468,92384,111736,121406,92272,111676,92216,111646,92188,75232,103160,117118,93664,74992,103036,93424,112252,102974,93304,74812,93244,74782,93214,129332,75512,103294,129908,129330,93944,75388,129906,93820,75326,93758,127604,75646,128756,127602,94078,128754,124148,126452,124146,126450,117236,121844,117234,121842,103412,103410,91584,111344,121212,91360,111224,121150,91248,111164,91192,111134,91164,91150,74480,102780,91888,74360,102718,91768,111422,91708,74270,91678,129306,74620,129850,92028,74558,91966,127546,128634,124026,126202,116986,121338,102906,90848,110968,121022,90736,110908,90680,110878,90652,90638,74104,102590,91e3,74044,90940,74014,90910,74174,91070,90480,110780,90424,110750,90396,90382,73916,90556,73886,90526,90296,110686,90268,90254,73822,90334,90204,90190,71136,101112,116094,70896,100988,70776,100926,70716,70686,129204,71416,101246,129202,71292,71230,127348,71550,127346,123636,123634,116212,116210,101364,101362,79296,105200,118140,79072,105080,118078,78960,105020,78904,104990,78876,78862,70384,100732,79600,70264,100670,79480,105278,79420,70174,79390,129178,70524,129466,79740,70462,79678,127290,127866,123514,124666,115962,118266,100858,113376,122232,126654,95424,113264,122172,95328,113208,122142,95280,113180,95256,113166,95244,78560,104824,117950,95968,78448,104764,95856,113468,104734,95800,78364,95772,78350,95758,70008,100542,78712,69948,96120,78652,69918,96060,78622,96030,70078,78782,96190,94912,113008,122044,94816,112952,122014,94768,112924,94744,112910,94732,94726,78192,104636,95088,78136,104606,95032,113054,95004,78094,94990,69820,78268,69790,95164,78238,95134,94560,112824,121950,94512,112796,94488,112782,94476,94470,78008,104542,94648,77980,94620,77966,94606,69726,78046,94686,94384,112732,94360,112718,94348,94342,77916,94428,77902,94414,94296,112686,94284,94278,77870,94318,94252,94246,68336,99708,68216,99646,68156,68126,68476,68414,127162,123258,115450,99834,72416,101752,116414,72304,101692,72248,101662,72220,72206,67960,99518,72568,67900,72508,67870,72478,68030,72638,80576,105840,118460,80480,105784,118430,80432,105756,80408,105742,80396,80390,72048,101564,80752,71992,101534,80696,71964,80668,71950,80654,67772,72124,67742,80828,72094,80798,114016,122552,126814,96832,113968,122524,96800,113944,122510,96784,113932,96776,113926,96772,80224,105656,118366,97120,80176,105628,97072,114076,105614,97048,80140,97036,80134,97030,71864,101470,80312,71836,97208,80284,71822,97180,80270,97166,67678,71902,80350,97246,96576,113840,122460,96544,113816,122446,96528,113804,96520,113798,96516,96514,80048,105564,96688,80024,105550,96664,113870,96652,80006,96646,71772,80092,71758,96732,80078,96718,96416,113752,122414,96400,113740,96392,113734,96388,96386,79960,105518,96472,79948,96460,79942,96454,71726,79982,96494,96336,113708,96328,113702,96324,96322,79916,96364,79910,96358,96296,113686,96292,96290,79894,96310,66936,99006,66876,66846,67006,68976,100028,68920,99998,68892,68878,66748,69052,66718,69022,73056,102072,116574,73008,102044,72984,102030,72972,72966,68792,99934,73144,68764,73116,68750,73102,66654,68830,73182,81216,106160,118620,81184,106136,118606,81168,106124,81160,106118,81156,81154,72880,101980,81328,72856,101966,81304,106190,81292,72838,81286,68700,72924,68686,81372,72910,81358,114336,122712,126894,114320,122700,114312,122694,114308,114306,81056,106072,118574,97696,81040,106060,97680,114380,106054,97672,81028,97668,81026,97666,72792,101934,81112,72780,97752,81100,72774,97740,81094,97734,68654,72814,81134,97774,114256,122668,114248,122662,114244,114242,80976,106028,97488,80968,106022,97480,114278,97476,80962,97474,72748,81004,72742,97516,80998,97510,114216,122646,114212,114210,80936,106006,97384,80932,97380,80930,97378,72726,80950,97398,114196,114194,80916,97332,80914,97330,66236,66206,67256,99166,67228,67214,66142,67294,69296,100188,69272,100174,69260,69254,67164,69340,67150,69326,73376,102232,116654,73360,102220,73352,102214,73348,73346,69208,100142,73432,102254,73420,69190,73414,67118,69230,73454,106320,118700,106312,118694,106308,106306,73296,102188,81616,106348,102182,81608,73284,81604,73282,81602,69164,73324,69158,81644,73318,81638,122792,126934,122788,122786,106280,118678,114536,106276,114532,106274,114530,73256,102166,81512,73252,98024,81508,73250,98020,81506,98018,69142,73270,81526,98038,122772,122770,106260,114484,106258,114482,73236,81460,73234,97908,81458,97906,122762,106250,114458,73226,81434,97850,66396,66382,67416,99246,67404,67398,66350,67438,69456,100268,69448,100262,69444,69442,67372,69484,67366,69478,102312,116694,102308,102306,69416,100246,73576,102326,73572,69410,73570,67350,69430,73590,118740,118738,102292,106420,102290,106418,69396,73524,69394,81780,73522,81778,118730,102282,106394,69386,73498,81722,66476,66470,67496,99286,67492,67490,66454,67510,100308,100306,67476,69556,67474,69554,116714]],i=[[27,917],[522,568,723,809],[237,308,436,284,646,653,428,379],[274,562,232,755,599,524,801,132,295,116,442,428,295,42,176,65],[361,575,922,525,176,586,640,321,536,742,677,742,687,284,193,517,273,494,263,147,593,800,571,320,803,133,231,390,685,330,63,410],[539,422,6,93,862,771,453,106,610,287,107,505,733,877,381,612,723,476,462,172,430,609,858,822,543,376,511,400,672,762,283,184,440,35,519,31,460,594,225,535,517,352,605,158,651,201,488,502,648,733,717,83,404,97,280,771,840,629,4,381,843,623,264,543],[521,310,864,547,858,580,296,379,53,779,897,444,400,925,749,415,822,93,217,208,928,244,583,620,246,148,447,631,292,908,490,704,516,258,457,907,594,723,674,292,272,96,684,432,686,606,860,569,193,219,129,186,236,287,192,775,278,173,40,379,712,463,646,776,171,491,297,763,156,732,95,270,447,90,507,48,228,821,808,898,784,663,627,378,382,262,380,602,754,336,89,614,87,432,670,616,157,374,242,726,600,269,375,898,845,454,354,130,814,587,804,34,211,330,539,297,827,865,37,517,834,315,550,86,801,4,108,539],[524,894,75,766,882,857,74,204,82,586,708,250,905,786,138,720,858,194,311,913,275,190,375,850,438,733,194,280,201,280,828,757,710,814,919,89,68,569,11,204,796,605,540,913,801,700,799,137,439,418,592,668,353,859,370,694,325,240,216,257,284,549,209,884,315,70,329,793,490,274,877,162,749,812,684,461,334,376,849,521,307,291,803,712,19,358,399,908,103,511,51,8,517,225,289,470,637,731,66,255,917,269,463,830,730,433,848,585,136,538,906,90,2,290,743,199,655,903,329,49,802,580,355,588,188,462,10,134,628,320,479,130,739,71,263,318,374,601,192,605,142,673,687,234,722,384,177,752,607,640,455,193,689,707,805,641,48,60,732,621,895,544,261,852,655,309,697,755,756,60,231,773,434,421,726,528,503,118,49,795,32,144,500,238,836,394,280,566,319,9,647,550,73,914,342,126,32,681,331,792,620,60,609,441,180,791,893,754,605,383,228,749,760,213,54,297,134,54,834,299,922,191,910,532,609,829,189,20,167,29,872,449,83,402,41,656,505,579,481,173,404,251,688,95,497,555,642,543,307,159,924,558,648,55,497,10],[352,77,373,504,35,599,428,207,409,574,118,498,285,380,350,492,197,265,920,155,914,299,229,643,294,871,306,88,87,193,352,781,846,75,327,520,435,543,203,666,249,346,781,621,640,268,794,534,539,781,408,390,644,102,476,499,290,632,545,37,858,916,552,41,542,289,122,272,383,800,485,98,752,472,761,107,784,860,658,741,290,204,681,407,855,85,99,62,482,180,20,297,451,593,913,142,808,684,287,536,561,76,653,899,729,567,744,390,513,192,516,258,240,518,794,395,768,848,51,610,384,168,190,826,328,596,786,303,570,381,415,641,156,237,151,429,531,207,676,710,89,168,304,402,40,708,575,162,864,229,65,861,841,512,164,477,221,92,358,785,288,357,850,836,827,736,707,94,8,494,114,521,2,499,851,543,152,729,771,95,248,361,578,323,856,797,289,51,684,466,533,820,669,45,902,452,167,342,244,173,35,463,651,51,699,591,452,578,37,124,298,332,552,43,427,119,662,777,475,850,764,364,578,911,283,711,472,420,245,288,594,394,511,327,589,777,699,688,43,408,842,383,721,521,560,644,714,559,62,145,873,663,713,159,672,729,624,59,193,417,158,209,563,564,343,693,109,608,563,365,181,772,677,310,248,353,708,410,579,870,617,841,632,860,289,536,35,777,618,586,424,833,77,597,346,269,757,632,695,751,331,247,184,45,787,680,18,66,407,369,54,492,228,613,830,922,437,519,644,905,789,420,305,441,207,300,892,827,141,537,381,662,513,56,252,341,242,797,838,837,720,224,307,631,61,87,560,310,756,665,397,808,851,309,473,795,378,31,647,915,459,806,590,731,425,216,548,249,321,881,699,535,673,782,210,815,905,303,843,922,281,73,469,791,660,162,498,308,155,422,907,817,187,62,16,425,535,336,286,437,375,273,610,296,183,923,116,667,751,353,62,366,691,379,687,842,37,357,720,742,330,5,39,923,311,424,242,749,321,54,669,316,342,299,534,105,667,488,640,672,576,540,316,486,721,610,46,656,447,171,616,464,190,531,297,321,762,752,533,175,134,14,381,433,717,45,111,20,596,284,736,138,646,411,877,669,141,919,45,780,407,164,332,899,165,726,600,325,498,655,357,752,768,223,849,647,63,310,863,251,366,304,282,738,675,410,389,244,31,121,303,263]];var n,r,_;!function(t){t[t.Auto=-1]="Auto",t[t.Level_0=0]="Level_0",t[t.Level_1=1]="Level_1",t[t.Level_2=2]="Level_2",t[t.Level_3=3]="Level_3",t[t.Level_4=4]="Level_4",t[t.Level_5=5]="Level_5",t[t.Level_6=6]="Level_6",t[t.Level_7=7]="Level_7",t[t.Level_8=8]="Level_8"}(n||(n={})),function(t){t[t.LATCH_TO_TEXT=900]="LATCH_TO_TEXT",t[t.LATCH_TO_BYTE_PADDED=901]="LATCH_TO_BYTE_PADDED",t[t.LATCH_TO_NUMERIC=902]="LATCH_TO_NUMERIC",t[t.SHIFT_TO_BYTE=913]="SHIFT_TO_BYTE",t[t.LATCH_TO_BYTE=924]="LATCH_TO_BYTE"}(r||(r={})),function(t){t[t.TEXT_COMPACTION=0]="TEXT_COMPACTION",t[t.BYTE_COMPACTION=1]="BYTE_COMPACTION",t[t.NUMERIC_COMPACTION=2]="NUMERIC_COMPACTION"}(_||(_={}));class o extends Array{constructor(t,e){if(super(t),this.sign=e,Object.setPrototypeOf(this,o.prototype),t>o.__kMaxLength)throw new RangeError("Maximum BigInt size exceeded")}static BigInt(t){if("number"==typeof t){if(0===t)return o.__zero();if(o.__isOneDigitInt(t))return t<0?o.__oneDigit(-t,!0):o.__oneDigit(t,!1);if(!Number.isFinite(t)||Math.floor(t)!==t)throw new RangeError("The number "+t+" cannot be converted to BigInt because it is not an integer");return o.__fromDouble(t)}if("string"==typeof t){const e=o.__fromString(t);if(null===e)throw new SyntaxError("Cannot convert "+t+" to a BigInt");return e}if("boolean"==typeof t)return!0===t?o.__oneDigit(1,!1):o.__zero();if("object"==typeof t){if(t.constructor===o)return t;const e=o.__toPrimitive(t);return o.BigInt(e)}throw new TypeError("Cannot convert "+t+" to a BigInt")}toDebugString(){const t=["BigInt["];for(const e of this)t.push((e?(e>>>0).toString(16):e)+", ");return t.push("]"),t.join("")}toString(t=10){if(t<2||t>36)throw new RangeError("toString() radix argument must be between 2 and 36");return 0===this.length?"0":t&t-1?o.__toStringGeneric(this,t,!1):o.__toStringBasePowerOfTwo(this,t)}valueOf(){throw new Error("Convert JSBI instances to native numbers using `toNumber`.")}static toNumber(t){const e=t.length;if(0===e)return 0;if(1===e){const e=t.__unsignedDigit(0);return t.sign?-e:e}const i=t.__digit(e-1),n=o.__clz30(i),r=30*e-n;if(r>1024)return t.sign?-1/0:1/0;let _=r-1,s=i,l=e-1;const u=n+3;let g=32===u?0:s<<u;g>>>=12;const a=u-12;let c=u>=12?0:s<<20+u,f=20+u;for(a>0&&l>0&&(l--,s=t.__digit(l),g|=s>>>30-a,c=s<<a+2,f=a+2);f>0&&l>0;)l--,s=t.__digit(l),c|=f>=30?s<<f-30:s>>>30-f,f-=30;const h=o.__decideRounding(t,f,l,s);if((1===h||0===h&&!(1&~c))&&(c=c+1>>>0,0===c&&(g++,g>>>20!=0&&(g=0,_++,_>1023))))return t.sign?-1/0:1/0;const d=t.sign?1<<31:0;return _=_+1023<<20,o.__kBitConversionInts[1]=d|_|g,o.__kBitConversionInts[0]=c,o.__kBitConversionDouble[0]}static unaryMinus(t){if(0===t.length)return t;const e=t.__copy();return e.sign=!t.sign,e}static bitwiseNot(t){return t.sign?o.__absoluteSubOne(t).__trim():o.__absoluteAddOne(t,!0)}static exponentiate(t,e){if(e.sign)throw new RangeError("Exponent must be positive");if(0===e.length)return o.__oneDigit(1,!1);if(0===t.length)return t;if(1===t.length&&1===t.__digit(0))return!t.sign||1&e.__digit(0)?t:o.unaryMinus(t);if(e.length>1)throw new RangeError("BigInt too big");let i=e.__unsignedDigit(0);if(1===i)return t;if(i>=o.__kMaxLengthBits)throw new RangeError("BigInt too big");if(1===t.length&&2===t.__digit(0)){const e=1+(i/30|0),n=t.sign&&!!(1&i),r=new o(e,n);r.__initializeDigits();const _=1<<i%30;return r.__setDigit(e-1,_),r}let n=null,r=t;for(1&i&&(n=t),i>>=1;0!==i;i>>=1)r=o.multiply(r,r),1&i&&(n=null===n?r:o.multiply(n,r));return n}static multiply(t,e){if(0===t.length)return t;if(0===e.length)return e;let i=t.length+e.length;t.__clzmsd()+e.__clzmsd()>=30&&i--;const n=new o(i,t.sign!==e.sign);n.__initializeDigits();for(let i=0;i<t.length;i++)o.__multiplyAccumulate(e,t.__digit(i),n,i);return n.__trim()}static divide(t,e){if(0===e.length)throw new RangeError("Division by zero");if(o.__absoluteCompare(t,e)<0)return o.__zero();const i=t.sign!==e.sign,n=e.__unsignedDigit(0);let r;if(1===e.length&&n<=32767){if(1===n)return i===t.sign?t:o.unaryMinus(t);r=o.__absoluteDivSmall(t,n,null)}else r=o.__absoluteDivLarge(t,e,!0,!1);return r.sign=i,r.__trim()}static remainder(t,e){if(0===e.length)throw new RangeError("Division by zero");if(o.__absoluteCompare(t,e)<0)return t;const i=e.__unsignedDigit(0);if(1===e.length&&i<=32767){if(1===i)return o.__zero();const e=o.__absoluteModSmall(t,i);return 0===e?o.__zero():o.__oneDigit(e,t.sign)}const n=o.__absoluteDivLarge(t,e,!1,!0);return n.sign=t.sign,n.__trim()}static add(t,e){const i=t.sign;return i===e.sign?o.__absoluteAdd(t,e,i):o.__absoluteCompare(t,e)>=0?o.__absoluteSub(t,e,i):o.__absoluteSub(e,t,!i)}static subtract(t,e){const i=t.sign;return i!==e.sign?o.__absoluteAdd(t,e,i):o.__absoluteCompare(t,e)>=0?o.__absoluteSub(t,e,i):o.__absoluteSub(e,t,!i)}static leftShift(t,e){return 0===e.length||0===t.length?t:e.sign?o.__rightShiftByAbsolute(t,e):o.__leftShiftByAbsolute(t,e)}static signedRightShift(t,e){return 0===e.length||0===t.length?t:e.sign?o.__leftShiftByAbsolute(t,e):o.__rightShiftByAbsolute(t,e)}static unsignedRightShift(){throw new TypeError("BigInts have no unsigned right shift; use >> instead")}static lessThan(t,e){return o.__compareToBigInt(t,e)<0}static lessThanOrEqual(t,e){return o.__compareToBigInt(t,e)<=0}static greaterThan(t,e){return o.__compareToBigInt(t,e)>0}static greaterThanOrEqual(t,e){return o.__compareToBigInt(t,e)>=0}static equal(t,e){if(t.sign!==e.sign)return!1;if(t.length!==e.length)return!1;for(let i=0;i<t.length;i++)if(t.__digit(i)!==e.__digit(i))return!1;return!0}static notEqual(t,e){return!o.equal(t,e)}static bitwiseAnd(t,e){if(!t.sign&&!e.sign)return o.__absoluteAnd(t,e).__trim();if(t.sign&&e.sign){const i=Math.max(t.length,e.length)+1;let n=o.__absoluteSubOne(t,i);const r=o.__absoluteSubOne(e);return n=o.__absoluteOr(n,r,n),o.__absoluteAddOne(n,!0,n).__trim()}return t.sign&&([t,e]=[e,t]),o.__absoluteAndNot(t,o.__absoluteSubOne(e)).__trim()}static bitwiseXor(t,e){if(!t.sign&&!e.sign)return o.__absoluteXor(t,e).__trim();if(t.sign&&e.sign){const i=Math.max(t.length,e.length),n=o.__absoluteSubOne(t,i),r=o.__absoluteSubOne(e);return o.__absoluteXor(n,r,n).__trim()}const i=Math.max(t.length,e.length)+1;t.sign&&([t,e]=[e,t]);let n=o.__absoluteSubOne(e,i);return n=o.__absoluteXor(n,t,n),o.__absoluteAddOne(n,!0,n).__trim()}static bitwiseOr(t,e){const i=Math.max(t.length,e.length);if(!t.sign&&!e.sign)return o.__absoluteOr(t,e).__trim();if(t.sign&&e.sign){let n=o.__absoluteSubOne(t,i);const r=o.__absoluteSubOne(e);return n=o.__absoluteAnd(n,r,n),o.__absoluteAddOne(n,!0,n).__trim()}t.sign&&([t,e]=[e,t]);let n=o.__absoluteSubOne(e,i);return n=o.__absoluteAndNot(n,t,n),o.__absoluteAddOne(n,!0,n).__trim()}static asIntN(t,e){if(0===e.length)return e;if((t=Math.floor(t))<0)throw new RangeError("Invalid value: not (convertible to) a safe integer");if(0===t)return o.__zero();if(t>=o.__kMaxLengthBits)return e;const i=(t+29)/30|0;if(e.length<i)return e;const n=e.__unsignedDigit(i-1),r=1<<(t-1)%30;if(e.length===i&&n<r)return e;if(!((n&r)===r))return o.__truncateToNBits(t,e);if(!e.sign)return o.__truncateAndSubFromPowerOfTwo(t,e,!0);if(!(n&r-1)){for(let n=i-2;n>=0;n--)if(0!==e.__digit(n))return o.__truncateAndSubFromPowerOfTwo(t,e,!1);return e.length===i&&n===r?e:o.__truncateToNBits(t,e)}return o.__truncateAndSubFromPowerOfTwo(t,e,!1)}static asUintN(t,e){if(0===e.length)return e;if((t=Math.floor(t))<0)throw new RangeError("Invalid value: not (convertible to) a safe integer");if(0===t)return o.__zero();if(e.sign){if(t>o.__kMaxLengthBits)throw new RangeError("BigInt too big");return o.__truncateAndSubFromPowerOfTwo(t,e,!1)}if(t>=o.__kMaxLengthBits)return e;const i=(t+29)/30|0;if(e.length<i)return e;const n=t%30;if(e.length==i){if(0===n)return e;if(e.__digit(i-1)>>>n==0)return e}return o.__truncateToNBits(t,e)}static ADD(t,e){if(t=o.__toPrimitive(t),e=o.__toPrimitive(e),"string"==typeof t)return"string"!=typeof e&&(e=e.toString()),t+e;if("string"==typeof e)return t.toString()+e;if(t=o.__toNumeric(t),e=o.__toNumeric(e),o.__isBigInt(t)&&o.__isBigInt(e))return o.add(t,e);if("number"==typeof t&&"number"==typeof e)return t+e;throw new TypeError("Cannot mix BigInt and other types, use explicit conversions")}static LT(t,e){return o.__compare(t,e,0)}static LE(t,e){return o.__compare(t,e,1)}static GT(t,e){return o.__compare(t,e,2)}static GE(t,e){return o.__compare(t,e,3)}static EQ(t,e){for(;;){if(o.__isBigInt(t))return o.__isBigInt(e)?o.equal(t,e):o.EQ(e,t);if("number"==typeof t){if(o.__isBigInt(e))return o.__equalToNumber(e,t);if("object"!=typeof e)return t==e;e=o.__toPrimitive(e)}else if("string"==typeof t){if(o.__isBigInt(e))return null!==(t=o.__fromString(t))&&o.equal(t,e);if("object"!=typeof e)return t==e;e=o.__toPrimitive(e)}else if("boolean"==typeof t){if(o.__isBigInt(e))return o.__equalToNumber(e,+t);if("object"!=typeof e)return t==e;e=o.__toPrimitive(e)}else if("symbol"==typeof t){if(o.__isBigInt(e))return!1;if("object"!=typeof e)return t==e;e=o.__toPrimitive(e)}else{if("object"!=typeof t)return t==e;if("object"==typeof e&&e.constructor!==o)return t==e;t=o.__toPrimitive(t)}}}static NE(t,e){return!o.EQ(t,e)}static DataViewGetBigInt64(t,e,i=!1){return o.asIntN(64,o.DataViewGetBigUint64(t,e,i))}static DataViewGetBigUint64(t,e,i=!1){const[n,r]=i?[4,0]:[0,4],_=t.getUint32(e+n,i),s=t.getUint32(e+r,i),l=new o(3,!1);return l.__setDigit(0,1073741823&s),l.__setDigit(1,(268435455&_)<<2|s>>>30),l.__setDigit(2,_>>>28),l.__trim()}static DataViewSetBigInt64(t,e,i,n=!1){o.DataViewSetBigUint64(t,e,i,n)}static DataViewSetBigUint64(t,e,i,n=!1){let r=0,_=0;if((i=o.asUintN(64,i)).length>0&&(_=i.__digit(0),i.length>1)){const t=i.__digit(1);_|=t<<30,r=t>>>2,i.length>2&&(r|=i.__digit(2)<<28)}const[s,l]=n?[4,0]:[0,4];t.setUint32(e+s,r,n),t.setUint32(e+l,_,n)}static __zero(){return new o(0,!1)}static __oneDigit(t,e){const i=new o(1,e);return i.__setDigit(0,t),i}__copy(){const t=new o(this.length,this.sign);for(let e=0;e<this.length;e++)t[e]=this[e];return t}__trim(){let t=this.length,e=this[t-1];for(;0===e;)t--,e=this[t-1],this.pop();return 0===t&&(this.sign=!1),this}__initializeDigits(){for(let t=0;t<this.length;t++)this[t]=0}static __decideRounding(t,e,i,n){if(e>0)return-1;let r;if(e<0)r=-e-1;else{if(0===i)return-1;i--,n=t.__digit(i),r=29}let _=1<<r;if(!(n&_))return-1;if(_-=1,n&_)return 1;for(;i>0;)if(i--,0!==t.__digit(i))return 1;return 0}static __fromDouble(t){const e=t<0;o.__kBitConversionDouble[0]=t;const i=(o.__kBitConversionInts[1]>>>20&2047)-1023,n=1+(i/30|0),r=new o(n,e);let _=1048575&o.__kBitConversionInts[1]|1048576,s=o.__kBitConversionInts[0];const l=i%30;let u,g=0;if(l<20){const t=20-l;g=t+32,u=_>>>t,_=_<<32-t|s>>>t,s<<=32-t}else if(20===l)g=32,u=_,_=s,s=0;else{const t=l-20;g=32-t,u=_<<t|s>>>32-t,_=s<<t,s=0}r.__setDigit(n-1,u);for(let t=n-2;t>=0;t--)g>0?(g-=30,u=_>>>2,_=_<<30|s>>>2,s<<=30):u=0,r.__setDigit(t,u);return r.__trim()}static __isWhitespace(t){return t<=13&&t>=9||(t<=159?32===t:t<=131071?160===t||5760===t:t<=196607?(t&=131071)<=10||40===t||41===t||47===t||95===t||4096===t:65279===t)}static __fromString(t,e=0){let i=0;const n=t.length;let r=0;if(r===n)return o.__zero();let _=t.charCodeAt(r);for(;o.__isWhitespace(_);){if(++r===n)return o.__zero();_=t.charCodeAt(r)}if(43===_){if(++r===n)return null;_=t.charCodeAt(r),i=1}else if(45===_){if(++r===n)return null;_=t.charCodeAt(r),i=-1}if(0===e){if(e=10,48===_){if(++r===n)return o.__zero();if(_=t.charCodeAt(r),88===_||120===_){if(e=16,++r===n)return null;_=t.charCodeAt(r)}else if(79===_||111===_){if(e=8,++r===n)return null;_=t.charCodeAt(r)}else if(66===_||98===_){if(e=2,++r===n)return null;_=t.charCodeAt(r)}}}else if(16===e&&48===_){if(++r===n)return o.__zero();if(_=t.charCodeAt(r),88===_||120===_){if(++r===n)return null;_=t.charCodeAt(r)}}if(0!==i&&10!==e)return null;for(;48===_;){if(++r===n)return o.__zero();_=t.charCodeAt(r)}const s=n-r;let l=o.__kMaxBitsPerChar[e],u=o.__kBitsPerCharTableMultiplier-1;if(s>(1<<30)/l)return null;const g=l*s+u>>>o.__kBitsPerCharTableShift,a=new o((g+29)/30|0,!1),c=e<10?e:10,f=e>10?e-10:0;if(e&e-1){a.__initializeDigits();let i=!1,s=0;do{let g=0,h=1;for(;;){let o;if(_-48>>>0<c)o=_-48;else{if(!((32|_)-97>>>0<f)){i=!0;break}o=(32|_)-87}const l=h*e;if(l>1073741823)break;if(h=l,g=g*e+o,s++,++r===n){i=!0;break}_=t.charCodeAt(r)}u=30*o.__kBitsPerCharTableMultiplier-1;const d=(l*s+u>>>o.__kBitsPerCharTableShift)/30|0;a.__inplaceMultiplyAdd(h,g,d)}while(!i)}else{l>>=o.__kBitsPerCharTableShift;const e=[],i=[];let s=!1;do{let o=0,u=0;for(;;){let e;if(_-48>>>0<c)e=_-48;else{if(!((32|_)-97>>>0<f)){s=!0;break}e=(32|_)-87}if(u+=l,o=o<<l|e,++r===n){s=!0;break}if(_=t.charCodeAt(r),u+l>30)break}e.push(o),i.push(u)}while(!s);o.__fillFromParts(a,e,i)}if(r!==n){if(!o.__isWhitespace(_))return null;for(r++;r<n;r++)if(_=t.charCodeAt(r),!o.__isWhitespace(_))return null}return a.sign=-1===i,a.__trim()}static __fillFromParts(t,e,i){let n=0,r=0,_=0;for(let o=e.length-1;o>=0;o--){const s=e[o],l=i[o];r|=s<<_,_+=l,30===_?(t.__setDigit(n++,r),_=0,r=0):_>30&&(t.__setDigit(n++,1073741823&r),_-=30,r=s>>>l-_)}if(0!==r){if(n>=t.length)throw new Error("implementation bug");t.__setDigit(n++,r)}for(;n<t.length;n++)t.__setDigit(n,0)}static __toStringBasePowerOfTwo(t,e){const i=t.length;let n=e-1;n=(n>>>1&85)+(85&n),n=(n>>>2&51)+(51&n),n=(n>>>4&15)+(15&n);const r=n,_=e-1,s=t.__digit(i-1);let l=(30*i-o.__clz30(s)+r-1)/r|0;if(t.sign&&l++,l>1<<28)throw new Error("string too long");const u=new Array(l);let g=l-1,a=0,c=0;for(let e=0;e<i-1;e++){const i=t.__digit(e),n=(a|i<<c)&_;u[g--]=o.__kConversionChars[n];const s=r-c;for(a=i>>>s,c=30-s;c>=r;)u[g--]=o.__kConversionChars[a&_],a>>>=r,c-=r}const f=(a|s<<c)&_;for(u[g--]=o.__kConversionChars[f],a=s>>>r-c;0!==a;)u[g--]=o.__kConversionChars[a&_],a>>>=r;if(t.sign&&(u[g--]="-"),-1!==g)throw new Error("implementation bug");return u.join("")}static __toStringGeneric(t,e,i){const n=t.length;if(0===n)return"";if(1===n){let n=t.__unsignedDigit(0).toString(e);return!1===i&&t.sign&&(n="-"+n),n}const r=30*n-o.__clz30(t.__digit(n-1)),_=o.__kMaxBitsPerChar[e]-1;let s=r*o.__kBitsPerCharTableMultiplier;s+=_-1,s=s/_|0;const l=s+1>>1,u=o.exponentiate(o.__oneDigit(e,!1),o.__oneDigit(l,!1));let g,a;const c=u.__unsignedDigit(0);if(1===u.length&&c<=32767){g=new o(t.length,!1),g.__initializeDigits();let i=0;for(let e=2*t.length-1;e>=0;e--){const n=i<<15|t.__halfDigit(e);g.__setHalfDigit(e,n/c|0),i=n%c|0}a=i.toString(e)}else{const i=o.__absoluteDivLarge(t,u,!0,!0);g=i.quotient;const n=i.remainder.__trim();a=o.__toStringGeneric(n,e,!0)}g.__trim();let f=o.__toStringGeneric(g,e,!0);for(;a.length<l;)a="0"+a;return!1===i&&t.sign&&(f="-"+f),f+a}static __unequalSign(t){return t?-1:1}static __absoluteGreater(t){return t?-1:1}static __absoluteLess(t){return t?1:-1}static __compareToBigInt(t,e){const i=t.sign;if(i!==e.sign)return o.__unequalSign(i);const n=o.__absoluteCompare(t,e);return n>0?o.__absoluteGreater(i):n<0?o.__absoluteLess(i):0}static __compareToNumber(t,e){if(o.__isOneDigitInt(e)){const i=t.sign,n=e<0;if(i!==n)return o.__unequalSign(i);if(0===t.length){if(n)throw new Error("implementation bug");return 0===e?0:-1}if(t.length>1)return o.__absoluteGreater(i);const r=Math.abs(e),_=t.__unsignedDigit(0);return _>r?o.__absoluteGreater(i):_<r?o.__absoluteLess(i):0}return o.__compareToDouble(t,e)}static __compareToDouble(t,e){if(e!=e)return e;if(e===1/0)return-1;if(e===-1/0)return 1;const i=t.sign;if(i!==e<0)return o.__unequalSign(i);if(0===e)throw new Error("implementation bug: should be handled elsewhere");if(0===t.length)return-1;o.__kBitConversionDouble[0]=e;const n=o.__kBitConversionInts[1]>>>20&2047;if(2047===n)throw new Error("implementation bug: handled elsewhere");const r=n-1023;if(r<0)return o.__absoluteGreater(i);const _=t.length;let s=t.__digit(_-1);const l=o.__clz30(s),u=30*_-l,g=r+1;if(u<g)return o.__absoluteLess(i);if(u>g)return o.__absoluteGreater(i);let a=1048575&o.__kBitConversionInts[1]|1048576,c=o.__kBitConversionInts[0];const f=29-l;if(f!==((u-1)%30|0))throw new Error("implementation bug");let h,d=0;if(f<20){const t=20-f;d=t+32,h=a>>>t,a=a<<32-t|c>>>t,c<<=32-t}else if(20===f)d=32,h=a,a=c,c=0;else{const t=f-20;d=32-t,h=a<<t|c>>>32-t,a=c<<t,c=0}if(s>>>=0,h>>>=0,s>h)return o.__absoluteGreater(i);if(s<h)return o.__absoluteLess(i);for(let e=_-2;e>=0;e--){d>0?(d-=30,h=a>>>2,a=a<<30|c>>>2,c<<=30):h=0;const n=t.__unsignedDigit(e);if(n>h)return o.__absoluteGreater(i);if(n<h)return o.__absoluteLess(i)}if(0!==a||0!==c){if(0===d)throw new Error("implementation bug");return o.__absoluteLess(i)}return 0}static __equalToNumber(t,e){return o.__isOneDigitInt(e)?0===e?0===t.length:1===t.length&&t.sign===e<0&&t.__unsignedDigit(0)===Math.abs(e):0===o.__compareToDouble(t,e)}static __comparisonResultToBool(t,e){switch(e){case 0:return t<0;case 1:return t<=0;case 2:return t>0;case 3:return t>=0}}static __compare(t,e,i){if(t=o.__toPrimitive(t),e=o.__toPrimitive(e),"string"==typeof t&&"string"==typeof e)switch(i){case 0:return t<e;case 1:return t<=e;case 2:return t>e;case 3:return t>=e}if(o.__isBigInt(t)&&"string"==typeof e)return null!==(e=o.__fromString(e))&&o.__comparisonResultToBool(o.__compareToBigInt(t,e),i);if("string"==typeof t&&o.__isBigInt(e))return null!==(t=o.__fromString(t))&&o.__comparisonResultToBool(o.__compareToBigInt(t,e),i);if(t=o.__toNumeric(t),e=o.__toNumeric(e),o.__isBigInt(t)){if(o.__isBigInt(e))return o.__comparisonResultToBool(o.__compareToBigInt(t,e),i);if("number"!=typeof e)throw new Error("implementation bug");return o.__comparisonResultToBool(o.__compareToNumber(t,e),i)}if("number"!=typeof t)throw new Error("implementation bug");if(o.__isBigInt(e))return o.__comparisonResultToBool(o.__compareToNumber(e,t),2^i);if("number"!=typeof e)throw new Error("implementation bug");switch(i){case 0:return t<e;case 1:return t<=e;case 2:return t>e;case 3:return t>=e}}__clzmsd(){return o.__clz30(this.__digit(this.length-1))}static __absoluteAdd(t,e,i){if(t.length<e.length)return o.__absoluteAdd(e,t,i);if(0===t.length)return t;if(0===e.length)return t.sign===i?t:o.unaryMinus(t);let n=t.length;(0===t.__clzmsd()||e.length===t.length&&0===e.__clzmsd())&&n++;const r=new o(n,i);let _=0,s=0;for(;s<e.length;s++){const i=t.__digit(s)+e.__digit(s)+_;_=i>>>30,r.__setDigit(s,1073741823&i)}for(;s<t.length;s++){const e=t.__digit(s)+_;_=e>>>30,r.__setDigit(s,1073741823&e)}return s<r.length&&r.__setDigit(s,_),r.__trim()}static __absoluteSub(t,e,i){if(0===t.length)return t;if(0===e.length)return t.sign===i?t:o.unaryMinus(t);const n=new o(t.length,i);let r=0,_=0;for(;_<e.length;_++){const i=t.__digit(_)-e.__digit(_)-r;r=i>>>30&1,n.__setDigit(_,1073741823&i)}for(;_<t.length;_++){const e=t.__digit(_)-r;r=e>>>30&1,n.__setDigit(_,1073741823&e)}return n.__trim()}static __absoluteAddOne(t,e,i=null){const n=t.length;null===i?i=new o(n,e):i.sign=e;let r=1;for(let e=0;e<n;e++){const n=t.__digit(e)+r;r=n>>>30,i.__setDigit(e,1073741823&n)}return 0!==r&&i.__setDigitGrow(n,1),i}static __absoluteSubOne(t,e){const i=t.length,n=new o(e=e||i,!1);let r=1;for(let e=0;e<i;e++){const i=t.__digit(e)-r;r=i>>>30&1,n.__setDigit(e,1073741823&i)}if(0!==r)throw new Error("implementation bug");for(let t=i;t<e;t++)n.__setDigit(t,0);return n}static __absoluteAnd(t,e,i=null){let n=t.length,r=e.length,_=r;if(n<r){_=n;const i=t,o=n;t=e,n=r,e=i,r=o}let s=_;null===i?i=new o(s,!1):s=i.length;let l=0;for(;l<_;l++)i.__setDigit(l,t.__digit(l)&e.__digit(l));for(;l<s;l++)i.__setDigit(l,0);return i}static __absoluteAndNot(t,e,i=null){const n=t.length,r=e.length;let _=r;n<r&&(_=n);let s=n;null===i?i=new o(s,!1):s=i.length;let l=0;for(;l<_;l++)i.__setDigit(l,t.__digit(l)&~e.__digit(l));for(;l<n;l++)i.__setDigit(l,t.__digit(l));for(;l<s;l++)i.__setDigit(l,0);return i}static __absoluteOr(t,e,i=null){let n=t.length,r=e.length,_=r;if(n<r){_=n;const i=t,o=n;t=e,n=r,e=i,r=o}let s=n;null===i?i=new o(s,!1):s=i.length;let l=0;for(;l<_;l++)i.__setDigit(l,t.__digit(l)|e.__digit(l));for(;l<n;l++)i.__setDigit(l,t.__digit(l));for(;l<s;l++)i.__setDigit(l,0);return i}static __absoluteXor(t,e,i=null){let n=t.length,r=e.length,_=r;if(n<r){_=n;const i=t,o=n;t=e,n=r,e=i,r=o}let s=n;null===i?i=new o(s,!1):s=i.length;let l=0;for(;l<_;l++)i.__setDigit(l,t.__digit(l)^e.__digit(l));for(;l<n;l++)i.__setDigit(l,t.__digit(l));for(;l<s;l++)i.__setDigit(l,0);return i}static __absoluteCompare(t,e){const i=t.length-e.length;if(0!==i)return i;let n=t.length-1;for(;n>=0&&t.__digit(n)===e.__digit(n);)n--;return n<0?0:t.__unsignedDigit(n)>e.__unsignedDigit(n)?1:-1}static __multiplyAccumulate(t,e,i,n){if(0===e)return;const r=32767&e,_=e>>>15;let s=0,l=0;for(let e=0;e<t.length;e++,n++){let u=i.__digit(n);const g=t.__digit(e),a=32767&g,c=g>>>15,f=o.__imul(a,r),h=o.__imul(a,_),d=o.__imul(c,r);u+=l+f+s,s=u>>>30,u&=1073741823,u+=((32767&h)<<15)+((32767&d)<<15),s+=u>>>30,l=o.__imul(c,_)+(h>>>15)+(d>>>15),i.__setDigit(n,1073741823&u)}for(;0!==s||0!==l;n++){let t=i.__digit(n);t+=s+l,l=0,s=t>>>30,i.__setDigit(n,1073741823&t)}}static __internalMultiplyAdd(t,e,i,n,r){let _=i,s=0;for(let i=0;i<n;i++){const n=t.__digit(i),l=o.__imul(32767&n,e),u=o.__imul(n>>>15,e),g=l+((32767&u)<<15)+s+_;_=g>>>30,s=u>>>15,r.__setDigit(i,1073741823&g)}if(r.length>n)for(r.__setDigit(n++,_+s);n<r.length;)r.__setDigit(n++,0);else if(_+s!==0)throw new Error("implementation bug")}__inplaceMultiplyAdd(t,e,i){i>this.length&&(i=this.length);const n=32767&t,r=t>>>15;let _=0,s=e;for(let t=0;t<i;t++){const e=this.__digit(t),i=32767&e,l=e>>>15,u=o.__imul(i,n),g=o.__imul(i,r),a=o.__imul(l,n);let c=s+u+_;_=c>>>30,c&=1073741823,c+=((32767&g)<<15)+((32767&a)<<15),_+=c>>>30,s=o.__imul(l,r)+(g>>>15)+(a>>>15),this.__setDigit(t,1073741823&c)}if(0!==_||0!==s)throw new Error("implementation bug")}static __absoluteDivSmall(t,e,i=null){null===i&&(i=new o(t.length,!1));let n=0;for(let r=2*t.length-1;r>=0;r-=2){let _=(n<<15|t.__halfDigit(r))>>>0;const o=_/e|0;n=_%e|0,_=(n<<15|t.__halfDigit(r-1))>>>0;const s=_/e|0;n=_%e|0,i.__setDigit(r>>>1,o<<15|s)}return i}static __absoluteModSmall(t,e){let i=0;for(let n=2*t.length-1;n>=0;n--){i=((i<<15|t.__halfDigit(n))>>>0)%e|0}return i}static __absoluteDivLarge(t,e,i,n){const r=e.__halfDigitLength(),_=e.length,s=t.__halfDigitLength()-r;let l=null;i&&(l=new o(s+2>>>1,!1),l.__initializeDigits());const u=new o(r+2>>>1,!1);u.__initializeDigits();const g=o.__clz15(e.__halfDigit(r-1));g>0&&(e=o.__specialLeftShift(e,g,0));const a=o.__specialLeftShift(t,g,1),c=e.__halfDigit(r-1);let f=0;for(let t=s;t>=0;t--){let n=32767;const s=a.__halfDigit(t+r);if(s!==c){const i=(s<<15|a.__halfDigit(t+r-1))>>>0;n=i/c|0;let _=i%c|0;const l=e.__halfDigit(r-2),u=a.__halfDigit(t+r-2);for(;o.__imul(n,l)>>>0>(_<<16|u)>>>0&&(n--,_+=c,!(_>32767)););}o.__internalMultiplyAdd(e,n,0,_,u);let g=a.__inplaceSub(u,t,r+1);0!==g&&(g=a.__inplaceAdd(e,t,r),a.__setHalfDigit(t+r,a.__halfDigit(t+r)+g&32767),n--),i&&(1&t?f=n<<15:l.__setDigit(t>>>1,f|n))}if(n)return a.__inplaceRightShift(g),i?{quotient:l,remainder:a}:a;if(i)return l;throw new Error("unreachable")}static __clz15(t){return o.__clz30(t)-15}__inplaceAdd(t,e,i){let n=0;for(let r=0;r<i;r++){const i=this.__halfDigit(e+r)+t.__halfDigit(r)+n;n=i>>>15,this.__setHalfDigit(e+r,32767&i)}return n}__inplaceSub(t,e,i){const n=i-1>>>1;let r=0;if(1&e){e>>=1;let _=this.__digit(e),o=32767&_,s=0;for(;s<n;s++){const i=t.__digit(s),n=(_>>>15)-(32767&i)-r;r=n>>>15&1,this.__setDigit(e+s,(32767&n)<<15|32767&o),_=this.__digit(e+s+1),o=(32767&_)-(i>>>15)-r,r=o>>>15&1}const l=t.__digit(s),u=(_>>>15)-(32767&l)-r;r=u>>>15&1,this.__setDigit(e+s,(32767&u)<<15|32767&o);const g=l>>>15;if(e+s+1>=this.length)throw new RangeError("out of bounds");1&i||(_=this.__digit(e+s+1),o=(32767&_)-g-r,r=o>>>15&1,this.__setDigit(e+t.length,1073709056&_|32767&o))}else{e>>=1;let n=0;for(;n<t.length-1;n++){const i=this.__digit(e+n),_=t.__digit(n),o=(32767&i)-(32767&_)-r;r=o>>>15&1;const s=(i>>>15)-(_>>>15)-r;r=s>>>15&1,this.__setDigit(e+n,(32767&s)<<15|32767&o)}const _=this.__digit(e+n),o=t.__digit(n),s=(32767&_)-(32767&o)-r;r=s>>>15&1;let l=0;1&i||(l=(_>>>15)-(o>>>15)-r,r=l>>>15&1),this.__setDigit(e+n,(32767&l)<<15|32767&s)}return r}__inplaceRightShift(t){if(0===t)return;let e=this.__digit(0)>>>t;const i=this.length-1;for(let n=0;n<i;n++){const i=this.__digit(n+1);this.__setDigit(n,i<<30-t&1073741823|e),e=i>>>t}this.__setDigit(i,e)}static __specialLeftShift(t,e,i){const n=t.length,r=new o(n+i,!1);if(0===e){for(let e=0;e<n;e++)r.__setDigit(e,t.__digit(e));return i>0&&r.__setDigit(n,0),r}let _=0;for(let i=0;i<n;i++){const n=t.__digit(i);r.__setDigit(i,n<<e&1073741823|_),_=n>>>30-e}return i>0&&r.__setDigit(n,_),r}static __leftShiftByAbsolute(t,e){const i=o.__toShiftAmount(e);if(i<0)throw new RangeError("BigInt too big");const n=i/30|0,r=i%30,_=t.length,s=0!==r&&t.__digit(_-1)>>>30-r!=0,l=_+n+(s?1:0),u=new o(l,t.sign);if(0===r){let e=0;for(;e<n;e++)u.__setDigit(e,0);for(;e<l;e++)u.__setDigit(e,t.__digit(e-n))}else{let e=0;for(let t=0;t<n;t++)u.__setDigit(t,0);for(let i=0;i<_;i++){const _=t.__digit(i);u.__setDigit(i+n,_<<r&1073741823|e),e=_>>>30-r}if(s)u.__setDigit(_+n,e);else if(0!==e)throw new Error("implementation bug")}return u.__trim()}static __rightShiftByAbsolute(t,e){const i=t.length,n=t.sign,r=o.__toShiftAmount(e);if(r<0)return o.__rightShiftByMaximum(n);const _=r/30|0,s=r%30;let l=i-_;if(l<=0)return o.__rightShiftByMaximum(n);let u=!1;if(n){const e=(1<<s)-1;if(t.__digit(_)&e)u=!0;else for(let e=0;e<_;e++)if(0!==t.__digit(e)){u=!0;break}}if(u&&0===s){0==~t.__digit(i-1)&&l++}let g=new o(l,n);if(0===s){g.__setDigit(l-1,0);for(let e=_;e<i;e++)g.__setDigit(e-_,t.__digit(e))}else{let e=t.__digit(_)>>>s;const n=i-_-1;for(let i=0;i<n;i++){const n=t.__digit(i+_+1);g.__setDigit(i,n<<30-s&1073741823|e),e=n>>>s}g.__setDigit(n,e)}return u&&(g=o.__absoluteAddOne(g,!0,g)),g.__trim()}static __rightShiftByMaximum(t){return t?o.__oneDigit(1,!0):o.__zero()}static __toShiftAmount(t){if(t.length>1)return-1;const e=t.__unsignedDigit(0);return e>o.__kMaxLengthBits?-1:e}static __toPrimitive(t,e="default"){if("object"!=typeof t)return t;if(t.constructor===o)return t;if("undefined"!=typeof Symbol&&"symbol"==typeof Symbol.toPrimitive){const i=t[Symbol.toPrimitive];if(i){const t=i(e);if("object"!=typeof t)return t;throw new TypeError("Cannot convert object to primitive value")}}const i=t.valueOf;if(i){const e=i.call(t);if("object"!=typeof e)return e}const n=t.toString;if(n){const e=n.call(t);if("object"!=typeof e)return e}throw new TypeError("Cannot convert object to primitive value")}static __toNumeric(t){return o.__isBigInt(t)?t:+t}static __isBigInt(t){return"object"==typeof t&&null!==t&&t.constructor===o}static __truncateToNBits(t,e){const i=(t+29)/30|0,n=new o(i,e.sign),r=i-1;for(let t=0;t<r;t++)n.__setDigit(t,e.__digit(t));let _=e.__digit(r);if(t%30!=0){const e=32-t%30;_=_<<e>>>e}return n.__setDigit(r,_),n.__trim()}static __truncateAndSubFromPowerOfTwo(t,e,i){const n=(t+29)/30|0,r=new o(n,i);let _=0;const s=n-1;let l=0;const u=Math.min(s,e.length);for(;_<u;_++){const t=0-e.__digit(_)-l;l=t>>>30&1,r.__setDigit(_,1073741823&t)}for(;_<s;_++)r.__setDigit(_,1073741823&-l);let g=s<e.length?e.__digit(s):0;const a=t%30;let c;if(0===a)c=0-g-l,c&=1073741823;else{const t=32-a;g=g<<t>>>t;const e=1<<32-t;c=e-g-l,c&=e-1}return r.__setDigit(s,c),r.__trim()}__digit(t){return this[t]}__unsignedDigit(t){return this[t]>>>0}__setDigit(t,e){this[t]=0|e}__setDigitGrow(t,e){this[t]=0|e}__halfDigitLength(){const t=this.length;return this.__unsignedDigit(t-1)<=32767?2*t-1:2*t}__halfDigit(t){return this[t>>>1]>>>15*(1&t)&32767}__setHalfDigit(t,e){const i=t>>>1,n=this.__digit(i),r=1&t?32767&n|e<<15:1073709056&n|32767&e;this.__setDigit(i,r)}static __digitPow(t,e){let i=1;for(;e>0;)1&e&&(i*=t),e>>>=1,t*=t;return i}static __isOneDigitInt(t){return(1073741823&t)===t}}o.__kMaxLength=1<<25,o.__kMaxLengthBits=o.__kMaxLength<<5,o.__kMaxBitsPerChar=[0,0,32,51,64,75,83,90,96,102,107,111,115,119,122,126,128,131,134,136,139,141,143,145,147,149,151,153,154,156,158,159,160,162,163,165,166],o.__kBitsPerCharTableShift=5,o.__kBitsPerCharTableMultiplier=1<<o.__kBitsPerCharTableShift,o.__kConversionChars="0123456789abcdefghijklmnopqrstuvwxyz".split(""),o.__kBitConversionBuffer=new ArrayBuffer(8),o.__kBitConversionDouble=new Float64Array(o.__kBitConversionBuffer),o.__kBitConversionInts=new Int32Array(o.__kBitConversionBuffer),o.__clz30=Math.clz32?function(t){return Math.clz32(t)-2}:function(t){return 0===t?30:29-(Math.log(t>>>0)/Math.LN2|0)|0},o.__imul=Math.imul||function(t,e){return t*e|0};class s{constructor(t,e){this.cols=t||0,this.rows=e||0,this.data=new Uint8Array(this.rows*this.cols)}get reservedBit(){return this._reservedBit||(this._reservedBit=new Uint8Array(this.data.length)),this._reservedBit}set(t,e,i,n){const r=t*this.cols+e;this.data[r]="number"==typeof i?i:i?1:0,n&&(this.reservedBit[r]=1)}get(t,e){return this.data[t*this.cols+e]}getRowData(t){const e=(t||0)*this.cols;return this.data.slice(e,e+this.cols)}getColData(t){return this.data.filter(((e,i)=>i%this.cols===t))}xor(t,e,i){this.data[t*this.cols+e]^=i?1:0}fill(t,e,i,n){const r=(e||0)*this.cols+(i||0);let _=0;_="number"==typeof n?n>0?r+n:this.data.length+n:this.data.length,this.data.fill(t,r,_)}setArray(t,e,i){const n=(e||0)*this.cols+(i||0);this.data.set(t,n)}addRow(t,e,i){e>=this.data.length?console.error(`offset: ${e} 越界`):(i&&i<t.length&&(t=t.slice(0,i)),this.data.set(t,e))}isReserved(t,e){return this._reservedBit?this._reservedBit[t*this.cols+e]:0}}class l{static isISO_8859_1(t){if(!t)return!1;for(let e=0;e<t.length;e++)if(t.charCodeAt(e)>255)return!1;return!0}static getCharCodes(t){t=t||"";const e=[];for(let i=0;i<t.length;i++)e.push(t.charCodeAt(i));return e}static getCharCodeArrayString(t){return t.map((t=>String.fromCharCode(t))).join("")}static encodeUtf8(t){const e=[];for(let i=0;i<t.length;i++){let n=t.charCodeAt(i);if(n>=55296&&n<=56319&&t.length>i+1){const e=t.charCodeAt(i+1);e>=56320&&e<=57343&&(n=1024*(n-55296)+e-56320+65536,i+=1)}n<128?e.push(n):n<2048?(e.push(n>>6|192),e.push(63&n|128)):n<55296||n>=57344&&n<65536?(e.push(224|n>>12),e.push(128|n>>6&63),e.push(128|63&n)):n>=65536&&n<=1114111?(e.push(240|n>>18),e.push(128|n>>12&63),e.push(128|n>>6&63),e.push(128|63&n)):e.push(239,191,189)}return new Uint8Array(e)}static getBytes_Utf8(t){try{return(new TextEncoder).encode(t)}catch(e){return console.log("DzTextEncoder.encode: TextEncoder is not defined"),this.encodeUtf8(t)}}static getBytes_ISO8859_1(t){return t=unescape(encodeURIComponent(t)),Uint8Array.from(this.getCharCodes(t))}static getBytes_Unicode(t){return Uint8Array.from(this.getCharCodes(t))}static getBytes(t,e){return t=t||"",e?l.getBytes_Utf8(t):l.getBytes_Unicode(t)}static hasBase256Chars(t){const e="string"==typeof t?t.split("").map((t=>t.charCodeAt(0))):t;if((null==e?void 0:e.length)>0)for(const t of e)if(t>=128)return!0;return!1}static encodeUnicodeFromUtf8(t){let e=0,i=0,n=0;const r=[];do{if(t[i]<=127)r[n]=t[i],e=i+1,n++;else{if(t[i]>=128&&t[i]<=191)return;if(t[i]>=192&&t[i]<=193)return;if(t[i]>=194&&t[i]<=223)r[n]=((31&t[i])<<6)+(63&t[i+1]),e=i+2,n++;else if(t[i]>=224&&t[i]<=239)r[n]=((15&t[i])<<12)+((63&t[i+1])<<6)+(63&t[i+2]),e=i+3,n++;else if(t[i]>=240)return}i=e}while(i<t.length);return r}}class u{constructor(){this.mErrorCode=0,this.textlatch={"01":[27],"02":[28],"03":[28,25],10:[28,28],12:[28],13:[28,25],20:[28],21:[27],23:[25],30:[29],31:[29,27],32:[29,28]}}static getInstance(){return this.sInstance||(this.sInstance=new u)}static create(t){return u.getInstance().build(t)}static register(t){t&&t.register(this.getInstance())}get ErrorCode(){return this.mErrorCode}get barcodeType(){return"Pdf417"}encode(t){return this.build(t)}build(t){const i=(t.text||"").trim();if(!i)return void(this.mErrorCode=1);const n=t.aspectRatio&&t.aspectRatio>0?t.aspectRatio:u.ASPECT_RATIO,_=t.quietZone&&t.quietZone>0?Math.floor(t.quietZone):u.QUIET_ZONE_DEFAULT,o=_,l=void 0!==t.eccLevel?t.eccLevel:-1;let g=this.getCodeWords(i);900==g[0]&&g.shift();var a=g.length;if(a>925)return;const c=this.getErrorCorrectionLevel(l,a);var f=2<<c,h=a+f+1;let d=1;d=t.cols&&t.cols>=1?t.cols:Math.round((Math.sqrt(4761+68*n*u.ROWHEIGHT*h)-69)/34),d<1?d=1:d>30&&(d=30);let b=Math.ceil(h/d),m=d*b;(b<3||b>90)&&(b<3?b=3:b>90&&(b=90),d=Math.ceil(m/b),m=d*b),m>928&&(Math.abs(n-493/32)<Math.abs(n-272/58)?(d=29,b=32):(d=16,b=58),m=928);const p=m-h;if(p>0)if(m-b==h)--b,m-=b;else{const t=new Uint16Array(p);t.fill(r.LATCH_TO_TEXT),g=g.concat(Array.from(t))}const T=m-f;g.unshift(T);const D=this.getErrorCorrection(g,c);g=g.concat(Array.from(D));const w=b*u.ROWHEIGHT+2*o,C=new s(17*(d+2)+35+2*_,w);let B=0;for(let t=0,i=0;t<b;++t){let n=0;const r=[];switch(_>0&&u.encodeChar(0,r,_),u.encodeChar(u.START_PATTERN,r),i){case 0:n=30*Math.floor(t/3)+Math.floor((b-1)/3);break;case 1:n=30*Math.floor(t/3)+3*c+(b-1)%3;break;case 2:n=30*Math.floor(t/3)+(d-1)}u.encodeChar(e[i][n],r);for(let t=0;t<d;++t)u.encodeChar(e[i][g[B]],r),++B;switch(i){case 0:n=30*Math.floor(t/3)+(d-1);break;case 1:n=30*Math.floor(t/3)+Math.floor((b-1)/3);break;case 2:n=30*Math.floor(t/3)+3*c+(b-1)%3}u.encodeChar(e[i][n],r),u.encodeChar(u.STOP_PATTERN,r,u.WIDTH_PATTERN_END),_>0&&u.encodeChar(0,r,_);const s=(o+t)*C.cols;C.addRow(r,s,C.cols),++i,i>2&&(i=0)}return C}getNumSeqs(t){return t.match(/([0-9]{13,})/g)||[]}getCodeWords(t){let e=[],i=_.TEXT_COMPACTION;const n=[],r=this.getNumSeqs(t);for(let e=0,i=0;e<r.length;e++){const _=r[e];i=t.indexOf(_,i),n.push({content:_,offset:i}),i+=_.length}n.push({content:"",offset:t.length});for(let r=0,o=0;r<n.length;r++){const s=n[r];if(s.offset>0){const n=t.substring(o,s.offset),r=[],l=n.match(/([\x09\x0a\x0d\x20-\x7e]{5,})/g)||[];for(let t=0;t<l.length;t++){const e=l[t];o=n.indexOf(e),r.push({content:e,offset:o})}r.push({content:"",offset:n.length});for(let t=0,o=0;t<r.length;t++){const s=r[t],l=(s.content||"").length;if(s.offset>0){const t=n.substring(o,s.offset);t.length>0&&(e=e.length>0&&i==_.TEXT_COMPACTION?this.encodeBinary(t,_.TEXT_COMPACTION,e):this.encodeBinary(t,i,e),i=_.BYTE_COMPACTION)}l>0&&(e=this.encodeText(s.content,e)),o=s.offset+l}}s.content&&(e=this.encodeNumberic(s.content,e),i=_.NUMERIC_COMPACTION),o=s.offset+s.content.length}return e}encodeNumberic(t,e){let i,n=e?e.slice(0):[];const _=[];n.push(r.LATCH_TO_NUMERIC);const s=o.BigInt(900),l=o.BigInt(0);for(;t.length>0;){_.splice(0),t.length>44?(i=t.substring(44),t=t.substring(0,44)):i="";let e=o.BigInt("1"+t);do{_.unshift(o.toNumber(o.remainder(e,s))),e=o.divide(e,s)}while(o.greaterThan(e,l));t=i,n=n.concat(_)}return n}encodeText(e,i){let n=0,_=[];var o=e.length;const s=i?i.slice(0):[];s.push(r.LATCH_TO_TEXT);for(let i=0;i<o;++i){const r=e.charCodeAt(i);let s=0;if((s=t[n].indexOf(r))>=0)_.push(s);else for(let l=0;l<t.length;++l)if(l!=n&&(s=t[l].indexOf(r))>=0){(i+1==o||i+1<o&&t[n].indexOf(e.charCodeAt(i+1))>=0)&&(3==l||0==l&&1==n)?3==l?_.push(29):_.push(27):(_=_.concat(this.textlatch[`${n}${l}`]),n=l),_.push(s);break}}var l=_.length;l%2!=0&&(_.push(29),++l);for(let t=0;t<l;t+=2)s.push(30*_[t]+_[t+1]);return s}encodeBinary(t,e,i){const n=l.getBytes(t,!0);let s=i?i.slice(0):[],u=0;const g=n.length;if(1==g&&e==_.TEXT_COMPACTION?s.push(r.SHIFT_TO_BYTE):g%6==0?s.push(r.LATCH_TO_BYTE):s.push(r.LATCH_TO_BYTE_PADDED),g>=6){const t=o.BigInt(900);for(;g-u>=6;){const e=[];let i=o.BigInt(0);for(let t=0;t<6;t++)i=o.add(i,o.leftShift(o.BigInt(255&n[u+t]),o.BigInt(8*(5-t))));for(let n=0;n<5;n++){const n=o.toNumber(o.remainder(i,t));e.unshift(n),i=o.divide(i,t)}s=s.concat(e),u+=6}}for(let t=u;t<g;t++){const e=255&n[t];s.push(e)}return s}getErrorCorrectionLevel(t,e){let i=8;const n=928-e;for(;i>0;){if(n>=2<<t)break;--i}return(t<0||t>8)&&(t=e<41?2:e<161?3:e<321?4:e<864?5:i),t>i&&(t=i),t}getErrorCorrection(t,e){const n=i[e],r=2<<e,_=r-1;let o=new Uint16Array(r);for(let e=0;e<t.length;e++){const i=(t[e]+o[_])%929;let r=0,s=0;for(let t=_;t>0;--t)r=i*n[t]%929,s=929-r,o[t]=(o[t-1]+s)%929;r=i*n[0]%929,s=929-r,o[0]=s%929}for(let t=0;t<o.length;t++)0!=o[t]&&(o[t]=929-o[t]);return o=o.reverse(),o}static encodeChar(t,e,i){const n="number"==typeof i&&i>=0?i:u.WIDTH_PATTERN,r=t.toString(2);if(n<=0)return e;if(r.length>=n)for(let t=r.length-n;t<r.length;t++)e.push(+r[t]);else{const t=n-r.length;for(let i=0;i<t;i++)e.push(0);for(let t=0;t<r.length;t++)e.push(+r[t])}return e}}u.ROWHEIGHT=1,u.QUIET_ZONE_DEFAULT=0,u.ECC_LEVEL_DEFAULT=2,u.ASPECT_RATIO=3,u.START_PATTERN=130728,u.STOP_PATTERN=260649,u.WIDTH_PATTERN=17,u.WIDTH_PATTERN_END=18;const g=()=>u.getInstance(),a=t=>{u.register(t)};export{_ as EncodingMode,r as LatchMode,u as PDF417,n as PDF47_ECC_LEVEL,g as getInstance,a as register};

// printer.js
// 使用德佟SDK适配层替换原有SUPVANAPIT50PRO SDK
import bleTool from '../../adapters/BLETool.js'
import bleToothManage from '../../adapters/BLEToothManage.js'
import constants from '../../adapters/Constants.js'

// 导入配置文件
const onlineStoreConfig = require('../../config/onlineStore.js');
const {
  getStatusMessage,
  getErrorLevel,
  getStatusCategory,
  isSuccessStatus
} = require('../../config/printerStatus.js');
const {
  getDefaultTemplates,
  getDetongP2Templates,
  isDetongP2Compatible,
  adaptTemplateForDetongP2
} = require('../../config/templates.js');

Page({
  data: {
    // 打印机状态
    printerStatus: 'disconnected', // disconnected, connecting, error, connected, printing
    printerDeviceSn: '',
    printerErrorCode: null, // 错误状态码
    printerErrorMessage: '', // 错误信息
    isPrinting: false, // 是否正在打印
    isConnecting: false, // 是否正在连接

    // 模版相关
    templates: [], // 模版列表
    selectedTemplateIndex: 0, // 当前选中的模版索引
    previewImagePath: '', // 预览图路径
    previewGenerating: false, // 是否正在生成预览图
    currentPreviewId: 0, // 当前预览图生成ID，用于防止竞态条件

    // 标签内容
    labelContent: {
      productName: '品名',
      operator: '操作人',
      date: '',
      copies: 1
    },

    // 蓝牙相关
    blueList: [],
    isScanning: false,
    showDeviceSelector: false,

    // 联系我们弹窗
    showContactModal: false,

    // 系统信息
    statusBarHeight: 0, // 状态栏高度
    navBarHeight: 40, // 导航栏高度，默认40px

    // Canvas相关
    templateWidth: 560,
    templateHeight: 302,
    barCodeWidth: 214,
    barCodeHeight: 72,
    qrCodeWidth: 20,
    qrCodeHeight: 20,
    pixelRatio: 2,
    canvasBarCode: null,
    canvasText: null,

    // 耗材信息相关
    materialInfo: null, // 当前耗材信息
    materialMismatch: false, // 耗材规格是否不匹配
    forcePrint: false // 是否允许强制打印
  },



  onLoad() {
    this.getSystemInfo()
    this.initPage()
  },


  onReady() {
    // 尝试连接上次使用的打印机
    // this.tryConnectLastPrinter()
  },

  onUnload(){
    this.disconnectDevice()
  },

  /**
   * 获取系统信息，设置状态栏和导航栏高度
   */
  getSystemInfo() {
    const systemInfo = wx.getSystemInfoSync()
    const { statusBarHeight, platform } = systemInfo

    // 设置状态栏高度
    this.setData({
      statusBarHeight: statusBarHeight || 20
    })

    // 根据平台设置导航栏高度
    let navBarHeight = 40 // iOS默认高度，减小一些
    if (platform === 'android') {
      navBarHeight = 46 // Android稍高一些
    }

    this.setData({
      navBarHeight: navBarHeight
    })

    console.log('系统信息:', { statusBarHeight, navBarHeight, platform })
  },

  /**
   * 初始化页面
   */
  initPage() {
    // 设置当前日期
    const today = new Date()
    const dateStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`

    // 加载上次保存的标签内容
    const savedContent = wx.getStorageSync('lastLabelContent')
    const labelContent = savedContent ? {
      ...savedContent,
      date: /*savedContent.date ||*/ dateStr // 如果没有保存日期，使用当前日期
    } : {
      productName: '品名',
      operator: '操作人',
      date: dateStr,
      copies: 1
    }

    this.setData({
      labelContent: labelContent,
      pixelRatio: wx.getWindowInfo().pixelRatio,
      canvasText: null, // 德佟SDK不需要显式Canvas对象
      canvasBarCode: wx.createSelectorQuery().in(this)
    })

    // 加载模版配置
    this.loadTemplates()

    // 尝试连接上次使用的打印机
    // this.tryConnectLastPrinter()
  },

  /**
   * 加载模版配置
   */
  loadTemplates() {
    // 从本地存储获取上次选中的模版索引
    const lastSelectedIndex = wx.getStorageSync('lastSelectedTemplateIndex') || 0

    // 尝试从网络加载模版配置文件，如果失败则使用德佟P2兼容配置
    this.loadTemplatesFromNetwork().then(templates => {
      // 确保模板与德佟P2兼容
      const compatibleTemplates = templates.map(template => adaptTemplateForDetongP2(template))
      this.setData({
        templates: compatibleTemplates,
        selectedTemplateIndex: lastSelectedIndex < compatibleTemplates.length ? lastSelectedIndex : 0
      })
      this.generatePreview()
    }).catch(() => {
      // 网络加载失败，使用德佟P2兼容的默认配置
      const templates = getDetongP2Templates()

      this.setData({
        templates: templates,
        selectedTemplateIndex: lastSelectedIndex < templates.length ? lastSelectedIndex : 0
      })
      this.generatePreview()
    })
  },

  /**
   * 从网络加载模版配置
   */
  loadTemplatesFromNetwork() {
    return new Promise((resolve, reject) => {
      // 这里可以从服务器加载配置文件
      // 目前使用本地默认配置
      reject('使用本地配置')
    })
  },

  /**
   * 尝试连接上次使用的打印机
   */
  tryConnectLastPrinter() {
    const lastPrinter = wx.getStorageSync('lastConnectedPrinter')
    if (lastPrinter) {
      this.setData({
        printerDeviceSn: lastPrinter.name || lastPrinter.deviceId
      })
      // 这里可以尝试连接上次的打印机
      // bleTool.connectBleDevice(lastPrinter)...
      this.connectDevice(lastPrinter)
    }
  },

  /**
   * 选择模版
   */
  selectTemplate(e) {
    const index = e.currentTarget.dataset.index
    this.setData({
      selectedTemplateIndex: index
    })

    // 保存选择到本地存储
    wx.setStorageSync('lastSelectedTemplateIndex', index)

    // 检查耗材规格匹配
    if (this.data.printerStatus === 'connected') {
      this.checkMaterialCompatibility()
    }

    // 重新生成预览图
    this.generatePreview()
  },

  /**
   * 生成预览图
   */
  generatePreview() {
    const template = this.data.templates[this.data.selectedTemplateIndex]
    if (!template) return

    // 如果正在生成预览图，跳过本次请求
    if (this.data.previewGenerating) {
      console.log('预览图正在生成中，跳过本次请求')
      return
    }

    // 生成唯一的预览ID，用于防止竞态条件
    const previewId = ++this.data.currentPreviewId

    // 设置生成状态
    this.setData({
      previewGenerating: true
    })

    // 创建临时模版对象，更新内容
    const tempTemplate = JSON.parse(JSON.stringify(template))
    tempTemplate.Copies = parseInt(this.data.labelContent.copies) || 1

    // 更新DrawObjects中的内容（只更新可编辑的内容，保留固定的标题和下划线）
    if (tempTemplate.DrawObjects && tempTemplate.DrawObjects.length >= 6) {
      // 更新品名内容（索引1）
      tempTemplate.DrawObjects[1].Content = this.data.labelContent.productName
      // 更新操作人内容（索引3）
      tempTemplate.DrawObjects[3].Content = this.data.labelContent.operator
      // 更新日期内容（索引5）
      tempTemplate.DrawObjects[5].Content = this.data.labelContent.date
    }

    const that = this
    bleToothManage.doDrawPreview(this.data.canvasText, [tempTemplate], this.data.canvasBarCode, res => {
      // 检查是否为最新的预览请求，防止旧请求覆盖新请求的结果
      if (previewId !== that.data.currentPreviewId) {
        console.log(`预览图请求已过期，忽略结果。当前ID: ${that.data.currentPreviewId}, 请求ID: ${previewId}`)
        return
      }

      if (res.ResultCode == constants.globalResultCode.ResultCode100) {
        let resultValue = res.ResultValue
        that.setData({
          templateWidth: resultValue.width,
          templateHeight: resultValue.height,
          barCodeWidth: resultValue.barcodeWidth,
          barCodeHeight: resultValue.barcodeHeight,
          qrCodeWidth: resultValue.qrcodeWidth,
          qrCodeHeight: resultValue.qrcodeHeight,
        })
      } else if (res.ResultCode == constants.globalResultCode.ResultCodeSuccess || res.ResultCode === 0) {
        console.log('德佟P2预览图生成成功:', res)

        // 处理德佟SDK返回的预览数据
        let previewUrl = '';
        if (res.previewData && res.previewData.length > 0) {
          // 德佟SDK返回的是dataUrls数组
          previewUrl = res.previewData[0];
        } else if (res.data && res.data.dataUrls && res.data.dataUrls.length > 0) {
          // 备用路径
          previewUrl = res.data.dataUrls[0];
        } else if (res.ResultValue && res.ResultValue.previewList && res.ResultValue.previewList.length > 0) {
          // 原有格式兼容
          previewUrl = res.ResultValue.previewList[0];
        } else if (res.data) {
          // 兼容其他格式
          previewUrl = res.data;
        }

        that.setData({
          previewImagePath: previewUrl
        })

        console.log('预览图URL:', previewUrl);
      }

      // 重置生成状态
      that.setData({
        previewGenerating: false
      })
    }).catch(error => {
      console.error('生成预览图失败:', error)
      // 重置生成状态
      that.setData({
        previewGenerating: false
      })
    })
  },

  /**
   * 保存标签内容到本地存储
   */
  saveLabelContent() {
    wx.setStorageSync('lastLabelContent', this.data.labelContent)
  },

  /**
   * 获取耗材信息
   * 德佟P2不支持获取耗材信息，返回默认兼容信息
   */
  getMaterialInfo() {
    const that = this
    return new Promise((resolve, reject) => {
      console.log('德佟P2打印机：使用默认兼容耗材信息...')

      // 德佟P2不支持获取耗材信息，直接返回默认兼容信息
      bleToothManage.ConsumableInformation().then(res => {
        console.log('德佟P2耗材信息API响应:', res)
        if (res && res.ResultCode === 0) {
          // 获取成功，使用返回的默认兼容信息
          const materialInfo = res.ResultValue
          console.log('德佟P2默认耗材信息:', materialInfo)
          that.setData({
            materialInfo: materialInfo
          })
          resolve(materialInfo)
        } else {
          // 获取失败，创建默认兼容信息
          console.log('德佟P2：创建默认兼容耗材信息')
          const defaultMaterialInfo = {
            width: 40,  // 默认宽度
            height: 30, // 默认高度
            gap: 0,     // 自动间隙
            type: 'label',
            compatible: true, // 德佟P2默认兼容
            autoDetected: true // 标记为自动检测
          }
          that.setData({
            materialInfo: defaultMaterialInfo
          })
          resolve(defaultMaterialInfo)
        }
      }).catch(error => {
        console.log('德佟P2：耗材信息获取异常，使用默认兼容信息', error)
        // 创建默认兼容信息
        const defaultMaterialInfo = {
          width: 40,
          height: 30,
          gap: 0,
          type: 'label',
          compatible: true,
          autoDetected: true
        }
        that.setData({
          materialInfo: defaultMaterialInfo
        })
        resolve(defaultMaterialInfo)
      })
    })
  },

  /**
   * 检查耗材规格兼容性
   * @param {boolean} forceRefresh - 是否强制重新获取耗材信息，默认false使用缓存
   */
  checkMaterialCompatibility(forceRefresh = false) {
    if (!this.data.materialInfo || forceRefresh) {
      // 如果没有耗材信息或强制刷新，先获取最新信息
      console.log('重新获取耗材信息进行兼容性检查...')
      this.getMaterialInfo().then(() => {
        this.performCompatibilityCheck()
      }).catch(error => {
        console.error('无法获取耗材信息进行兼容性检查:', error)
      })
    } else {
      console.log('使用缓存的耗材信息进行兼容性检查')
      this.performCompatibilityCheck()
    }
  },

  /**
   * 执行兼容性检查
   * 德佟P2默认兼容策略：自动适配间隙，按模板尺寸直接匹配
   */
  performCompatibilityCheck() {
    const template = this.data.templates[this.data.selectedTemplateIndex]
    const materialInfo = this.data.materialInfo

    console.log('德佟P2兼容性检查:', {
      hasTemplate: !!template,
      hasMaterialInfo: !!materialInfo,
      template: template ? {
        name: template.TemplateName,
        width: template.Width,
        height: template.Height,
        gap: template.Gap,
        autoGap: template.AutoGap
      } : null,
      materialInfo: materialInfo
    })

    if (!template || !materialInfo) {
      console.log('兼容性检查跳过：缺少模板或耗材信息')
      return
    }

    // 德佟P2特殊兼容性检查逻辑
    let isCompatible = true
    let compatibilityReason = '德佟P2自动兼容'

    // 如果耗材信息标记为自动检测或兼容，直接认为兼容
    if (materialInfo.autoDetected || materialInfo.compatible) {
      isCompatible = true
      compatibilityReason = '德佟P2默认兼容模式'
      console.log('✅ 德佟P2默认兼容模式：直接兼容')
    } else {
      // 如果有具体的耗材尺寸信息，进行比较
      const heightMatch = materialInfo.paperDirectionSize === template.Height ||
                         materialInfo.height === template.Height
      const widthMatch = materialInfo.printHeadDirectionSize === template.Width ||
                        materialInfo.width === template.Width

      // 德佟P2自动适配间隙，不检查gap匹配
      const gapMatch = true // 德佟P2自动间隙，总是匹配

      isCompatible = heightMatch && widthMatch && gapMatch
      compatibilityReason = isCompatible ? '尺寸匹配，自动间隙' : '尺寸不匹配'
    }

    console.log('德佟P2兼容性检查结果:', {
      template: {
        name: template.TemplateName,
        width: template.Width,
        height: template.Height,
        gap: template.Gap,
        autoGap: template.AutoGap
      },
      material: {
        width: materialInfo.printHeadDirectionSize || materialInfo.width,
        height: materialInfo.paperDirectionSize || materialInfo.height,
        gap: materialInfo.gap,
        autoDetected: materialInfo.autoDetected,
        compatible: materialInfo.compatible
      },
      isCompatible: isCompatible,
      reason: compatibilityReason
    })

    // 德佟P2默认兼容策略：除非明确不兼容，否则都认为兼容
    this.setData({
      materialMismatch: !isCompatible
    })

    if (!isCompatible) {
      // 更新状态栏显示不匹配信息（德佟P2很少出现这种情况）
      this.setData({
        printerErrorMessage: '当前耗材规格与模版不一致（德佟P2通常自动适配）',
        printerErrorCode: null
      })

      console.log('⚠️ 德佟P2耗材规格不匹配 - 已设置 materialMismatch = true')
    } else {
      // 规格匹配，清除错误信息
      if (this.data.printerErrorMessage && this.data.printerErrorMessage.includes('耗材规格')) {
        this.setData({
          printerErrorMessage: '',
          printerErrorCode: null
        })
      }
      console.log('✅ 德佟P2耗材兼容 - 已设置 materialMismatch = false')
    }
  },



  /**
   * 处理打印机状态码
   */
  handlePrinterStatus(resultCode, useUserFriendlyMessage = false) {
    const message = getStatusMessage(resultCode, useUserFriendlyMessage);
    const category = getStatusCategory(resultCode);
    const errorLevel = getErrorLevel(resultCode);
    const isSuccess = isSuccessStatus(resultCode);

    // 成功状态码 - 设备连接正常
    if (isSuccess) {
      return {
        status: 'connected',
        message: message,
        isConnected: true,
        category: category,
        errorLevel: errorLevel
      }
    }

    // 硬件/耗材相关错误 - 设备已连接但打印有问题
    if (category === 'hardware') {
      return {
        status: 'connected', // 设备仍然连接，只是打印有问题
        message: message,
        isPrintError: true,
        isConnected: true,
        category: category,
        errorLevel: errorLevel
      }
    }

    // 蓝牙连接相关错误 - 设备连接问题
    if (category === 'bluetooth') {
      return {
        status: 'error',
        message: message,
        isConnected: false,
        category: category,
        errorLevel: errorLevel
      }
    }

    // 数据处理错误 - 根据具体情况判断
    if (category === 'dataProcessing') {
      // 某些数据处理错误不影响连接状态
      const nonConnectionErrors = [108, 116, 118, 119, 120, 121, 122, 123, 124, 135];
      const isConnected = nonConnectionErrors.includes(resultCode);

      return {
        status: isConnected ? 'connected' : 'error',
        message: message,
        isConnected: isConnected,
        isPrintError: isConnected,
        category: category,
        errorLevel: errorLevel
      }
    }

    // 其他未知错误
    return {
      status: 'error',
      message: message,
      isConnected: false,
      category: 'unknown',
      errorLevel: 'error'
    }
  },

  /**
   * 更新打印机状态
   */
  updatePrinterStatus(resultCode, customMessage = '', useUserFriendlyMessage = false) {
    const statusInfo = this.handlePrinterStatus(resultCode, useUserFriendlyMessage)

    this.setData({
      printerStatus: statusInfo.status,
      printerErrorCode: resultCode,
      printerErrorMessage: customMessage || statusInfo.message
    })

    // 如果设备仍然连接，保持设备信息
    if (statusInfo.isConnected && this.data.printerDeviceSn) {
      // 保持设备连接信息不变
    } else if (!statusInfo.isConnected) {
      // 设备断开连接，清除设备信息
      this.setData({
        printerDeviceSn: ''
      })
    }
  },

  /**
   * 显示用户友好的错误提示
   */
  showUserFriendlyError(resultCode, title = '操作失败') {
    const userFriendlyMessage = getStatusMessage(resultCode, true);
    const errorLevel = getErrorLevel(resultCode);

    // 根据错误级别选择不同的显示方式
    if (errorLevel === 'warning') {
      wx.showModal({
        title: '提示',
        content: userFriendlyMessage,
        showCancel: false,
        confirmText: '知道了'
      });
    } else if (errorLevel === 'error') {
      wx.showModal({
        title: title,
        content: userFriendlyMessage,
        showCancel: false,
        confirmText: '确定'
      });
    } else {
      wx.showToast({
        title: userFriendlyMessage,
        icon: 'none',
        duration: 3000
      });
    }
  },

  /**
   * 输入品名
   */
  onProductNameInput(e) {
    this.setData({
      'labelContent.productName': e.detail.value.slice(0, 20) // 限制20个字符
    })
    this.saveLabelContent() // 保存内容
    this.generatePreview()
  },

  /**
   * 输入操作人
   */
  onOperatorInput(e) {
    this.setData({
      'labelContent.operator': e.detail.value.slice(0, 20) // 限制20个字符
    })
    this.saveLabelContent() // 保存内容
    this.generatePreview()
  },

  /**
   * 选择日期
   */
  onDateChange(e) {
    this.setData({
      'labelContent.date': e.detail.value
    })
    this.saveLabelContent() // 保存内容
    this.generatePreview()
  },

  /**
   * 输入打印份数
   */
  onCopiesInput(e) {
    let value = e.detail.value
    // 允许空值，用户可能正在删除内容
    if (value === '') {
      this.setData({
        'labelContent.copies': ''
      })
      return
    }

    let copies = parseInt(value) || 1
    copies = Math.max(1, Math.min(250, copies)) // 限制1-250
    this.setData({
      'labelContent.copies': copies
    })
    this.saveLabelContent() // 保存内容
    // 移除预览图刷新，因为份数与内容无关
  },

  /**
   * 增加打印份数
   */
  increaseCopies() {
    let copies = parseInt(this.data.labelContent.copies) || 1
    copies = Math.min(250, copies + 1) // 最大250
    this.setData({
      'labelContent.copies': copies
    })
    this.saveLabelContent() // 保存内容
    // 移除预览图刷新，因为份数与内容无关
  },

  /**
   * 减少打印份数
   */
  decreaseCopies() {
    let copies = parseInt(this.data.labelContent.copies) || 1
    copies = Math.max(1, copies - 1) // 最小1
    this.setData({
      'labelContent.copies': copies
    })
    this.saveLabelContent() // 保存内容
    // 移除预览图刷新，因为份数与内容无关
  },

  /**
   * 连接打印机/打印标签/停止打印按钮
   */
  onPrintAction() {
    // 防止重复操作
    if (this.data.isConnecting || this.data.isScanning) {
      const message = this.data.isScanning ? '正在搜索设备...' : '正在连接中...'
      wx.showToast({
        title: message,
        icon: 'none'
      })
      return
    }

    if (this.data.isPrinting) {
      // 正在打印，停止打印
      this.stopPrint()
    } else if (this.data.printerStatus === 'connected') {
      // 已连接，执行打印
      this.printLabel()
    } else {
      // 未连接，开始连接流程
      this.startConnectPrinter()
    }
  },

  /**
   * 开始连接打印机流程
   */
  startConnectPrinter() {
    // 检查蓝牙授权
    wx.authorize({
      scope: 'scope.bluetooth',
      success: () => {
        this.scanAndConnectPrinter()
      },
      fail: () => {
        wx.showModal({
          title: '需要蓝牙权限',
          content: '请授权蓝牙功能以连接打印机',
          success: (res) => {
            if (res.confirm) {
              wx.openSetting()
            }
          }
        })
      }
    })
  },

  /**
   * 扫描并连接打印机
   */
  scanAndConnectPrinter() {
    this.setData({
      isScanning: true,
      blueList: []
    })

    const that = this

    wx.showLoading({
      title: '搜索设备中...',
      mask: true
    })

    // 设置智能连接成功回调
    bleTool.onAutoConnectSuccess = (device) => {
      wx.hideLoading()
      console.log('德佟P2智能连接成功:', device)
      that.setData({
        isScanning: false,
        isConnecting: false,
        printerStatus: 'connected',
        connectedDevice: device,
        printerErrorCode: null,
        printerErrorMessage: ''
      })

      // 连接成功后检查耗材兼容性
      that.checkMaterialCompatibility()

      wx.showToast({
        title: '连接成功',
        icon: 'success'
      })
    }

    // 开始智能扫描
    bleTool.scanBleDeviceList((res) => {
      if (res.ResultCode == 0) {
        if (res && res.ResultValue && res.ResultValue.devices[0]) {
          const device = res.ResultValue.devices[0]
          console.log('德佟P2发现设备:', device.name)

          // 更新设备列表（用于显示）
          that.data.blueList.push(device)
          that.setData({
            blueList: that.data.blueList
          })
        }
      }
    }).catch(error => {
      wx.hideLoading()
      console.log('德佟P2搜索蓝牙设备失败:', error)
      that.setData({
        isScanning: false,
        printerStatus: 'error',
        printerErrorMessage: error.message || '搜索设备失败',
        printerErrorCode: error.ResultCode
      })

      let errorMessage = '搜索设备失败';
      if (error.ResultCode === 104) {
        errorMessage = '未发现设备，请检查打印机是否开启';
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 3000
      })
    })

    // 设置总体超时（15秒）
    setTimeout(() => {
      if (that.data.isScanning) {
        wx.hideLoading()
        bleTool.clearTimeouts()
        bleTool.stopScanBleDevices()
        that.setData({
          isScanning: false,
          printerStatus: 'error',
          printerErrorMessage: '连接超时，请重试',
          printerErrorCode: null
        })
        wx.showToast({
          title: '连接超时，请重试',
          icon: 'none'
        })
      }
    }, 15000)
  },

  /**
   * 连接指定设备
   */
  connectDevice(device) {
    // 设置连接中状态
    this.setData({
      isConnecting: true,
      printerStatus: 'connecting',
      printerErrorCode: null,
      printerErrorMessage: ''
    })

    const that = this
    bleTool.connectBleDevice(device).then(res => {
      console.log('连接结果:', res)

      // 处理连接结果状态
      const statusInfo = that.handlePrinterStatus(res.ResultCode)

      if (statusInfo.isConnected) {
        // 设备连接成功
        that.setData({
          isConnecting: false,
          printerStatus: statusInfo.status,
          printerDeviceSn: device.name || device.deviceId,
          showDeviceSelector: false,
          printerErrorCode: statusInfo.isPrintError ? res.ResultCode : null,
          printerErrorMessage: statusInfo.isPrintError ? statusInfo.message : ''
        })

        // 保存到本地存储
        wx.setStorageSync('lastConnectedPrinter', device)

        // 更新模版的DeviceSn
        const templates = that.data.templates
        templates.forEach(template => {
          template.DeviceSn = device.name || device.deviceId
        })
        that.setData({ templates })

        // 连接成功后获取耗材信息并检查兼容性
        that.getMaterialInfo().then(() => {
          that.checkMaterialCompatibility()
        }).catch(error => {
          console.log('获取耗材信息失败，但设备已连接:', error)
        })

        if (statusInfo.isPrintError) {
          wx.showToast({
            title: `设备已连接，但${statusInfo.message}`,
            icon: 'none',
            duration: 3000
          })
        } else {
          wx.showToast({
            title: '连接成功',
            icon: 'success'
          })
        }
      } else {
        // 连接失败
        that.setData({
          isConnecting: false,
          printerStatus: 'error',
          printerErrorCode: res.ResultCode,
          printerErrorMessage: statusInfo.message,
          showDeviceSelector: false
        })

        // 使用用户友好的错误消息
        that.showUserFriendlyError(res.ResultCode, '连接失败')
      }
    }).catch(error => {
      console.log('连接异常:', error)
      that.setData({
        isConnecting: false,
        printerStatus: 'error',
        printerErrorCode: 109,
        printerErrorMessage: '连接蓝牙异常'
      })
      wx.showToast({
        title: '连接蓝牙异常',
        icon: 'error'
      })
    })
  },

  /**
   * 选择设备对话框中的设备选择
   */
  selectDevice(e) {
    const index = e.currentTarget.dataset.index
    const device = this.data.blueList[index]

    // 停止扫描
    if (this.data.isScanning) {
      bleTool.stopScanBleDevices()
      this.setData({
        isScanning: false
      })
    }

    // 检查是否为当前已连接设备
    const currentDeviceId = this.data.printerDeviceSn
    const selectedDeviceId = device.name || device.deviceId

    if (currentDeviceId === selectedDeviceId) {
      // 选择的是当前设备，不需要重新连接
      wx.showToast({
        title: '当前设备已连接',
        icon: 'success'
      })
      this.setData({
        showDeviceSelector: false
      })
      return
    }

    // 连接新设备
    this.connectDevice(device)
  },

  /**
   * 关闭设备选择对话框
   */
  closeDeviceSelector() {
    // 停止扫描
    if (this.data.isScanning) {
      bleTool.stopScanBleDevices()
    }

    this.setData({
      showDeviceSelector: false,
      isScanning: false
    })
  },

  /**
   * 更改设备
   */
  changeDevice() {
    // 防止重复操作
    if (this.data.isScanning || this.data.isConnecting) {
      wx.showToast({
        title: '操作进行中，请稍候',
        icon: 'none'
      })
      return
    }

    // 开始搜索设备并显示选择对话框
    this.setData({
      isScanning: true,
      blueList: [],
      showDeviceSelector: true
    })

    const that = this

    // 开始扫描，持续搜索直到用户手动停止
    bleTool.scanBleDeviceList((res) => {
      if (res.ResultCode == 0) {
        if (res && res.ResultValue && res.ResultValue.devices[0]) {
          const device = res.ResultValue.devices[0]
          // 检查是否已存在
          const exists = that.data.blueList.find(item =>
            (item.deviceId === device.deviceId) || (item.name === device.name)
          )
          if (!exists) {
            that.data.blueList.push(device)
            that.setData({
              blueList: that.data.blueList
            })
          }
        }
      }
    }).catch(error => {
      console.log('搜索蓝牙设备失败:', error)
      that.setData({
        isScanning: false,
        showDeviceSelector: false
      })
      wx.showToast({
        title: '搜索设备失败',
        icon: 'error'
      })
    })
  },

  /**
   * 打印标签
   */
  printLabel() {
    const template = this.data.templates[this.data.selectedTemplateIndex]
    if (!template) {
      this.updatePrinterStatus(116) // 模板对象不能为空
      wx.showToast({
        title: '请选择模版',
        icon: 'none'
      })
      return
    }



    // 每次打印前都重新获取最新的耗材信息，避免缓存问题
    console.log('打印前重新获取最新耗材信息...')
    this.getMaterialInfo().then(() => {
      console.log('最新耗材信息获取完成，开始兼容性检查')
      this.performCompatibilityCheck()

      console.log('当前耗材匹配状态:', {
        materialMismatch: this.data.materialMismatch,
        forcePrint: this.data.forcePrint,
        materialInfo: this.data.materialInfo,
        template: {
          name: template.TemplateName,
          width: template.Width,
          height: template.Height,
          gap: template.Gap
        }
      })

      // 检查耗材规格兼容性
      if (this.data.materialMismatch) {
        // 如果设置了强制打印，弹出确认对话框
        if (this.data.forcePrint) {
          wx.showModal({
            title: '耗材规格不匹配',
            content: '当前耗材规格与模版不一致，确定要强制打印吗？',
            confirmText: '确定打印',
            cancelText: '取消',
            success: (res) => {
              if (res.confirm) {
                // 用户确认强制打印
                this.executePrint()
              }
            }
          })
          return
        } else {
          // 如果没有设置强制打印，直接阻止打印
          wx.showToast({
            title: '耗材规格不匹配，无法打印',
            icon: 'none',
            duration: 2000
          })
          return
        }
      }

      this.executePrint()
    }).catch(error => {
      console.error('获取耗材信息失败，无法进行兼容性检查:', error)
      wx.showToast({
        title: '获取耗材信息失败，请检查设备连接',
        icon: 'none',
        duration: 2000
      })
    })
  },

  /**
   * 执行打印
   */
  executePrint() {
    // 设置打印状态
    this.setData({
      isPrinting: true,
      printerStatus: 'printing'
    })

    const template = this.data.templates[this.data.selectedTemplateIndex]

    // 创建打印模版对象
    const printTemplate = JSON.parse(JSON.stringify(template))
    printTemplate.Copies = parseInt(this.data.labelContent.copies) || 1

    // 更新DrawObjects中的内容（只更新可编辑的内容，保留固定的标题和下划线）
    if (printTemplate.DrawObjects && printTemplate.DrawObjects.length >= 6) {
      // 更新品名内容（索引1）
      printTemplate.DrawObjects[1].Content = this.data.labelContent.productName
      // 更新操作人内容（索引3）
      printTemplate.DrawObjects[3].Content = this.data.labelContent.operator
      // 更新日期内容（索引5）
      printTemplate.DrawObjects[5].Content = this.data.labelContent.date
    }

    const that = this
    // 执行打印
    bleToothManage.doPrintMatrix(this.data.canvasText, [printTemplate], this.data.canvasBarCode, res => {
      console.log('打印回调:', res)

      // 处理打印状态
      const statusInfo = that.handlePrinterStatus(res.ResultCode)

      // 停止打印状态
      that.setData({
        isPrinting: false
      })

      if (isSuccessStatus(res.ResultCode)) {
        // 打印成功，保持强制打印标志不变（用户可能需要连续打印）
        that.setData({
          printerStatus: 'connected',
          printerErrorCode: null,
          printerErrorMessage: ''
        })

        // 如果之前有耗材不匹配的警告，重新检查
        if (that.data.materialMismatch) {
          that.checkMaterialCompatibility()
        }

        wx.showToast({
          title: '打印成功',
          icon: 'success'
        })
      } else {
        // 打印失败，检查是否需要保持连接
        const shouldKeepConnection = res.keepConnection !== false; // 默认保持连接

        if (shouldKeepConnection) {
          // 设备仍然连接，但打印有问题
          console.log('德佟P2打印失败，但保持设备连接状态');
          that.setData({
            printerStatus: 'connected', // 保持连接状态
            printerErrorCode: res.ResultCode,
            printerErrorMessage: res.message || statusInfo.message
          })
        } else {
          // 设备连接断开
          console.log('德佟P2打印失败，设备连接断开');
          that.setData({
            printerStatus: 'error',
            printerErrorCode: res.ResultCode,
            printerErrorMessage: res.message || statusInfo.message
          })
        }

        // 使用用户友好的错误消息
        that.showUserFriendlyError(res.ResultCode, '打印失败')
      }
    }).catch(error => {
      console.log('打印异常:', error)
      that.setData({
        isPrinting: false,
        printerStatus: 'error',
        printerErrorCode: 132,
        printerErrorMessage: '打印异常终止'
      })
      wx.showToast({
        title: '打印异常终止',
        icon: 'error'
      })
    })
  },

  /**
   * 停止打印
   */
  stopPrint() {
    if (!this.data.isPrinting) {
      wx.showToast({
        title: '当前没有打印任务',
        icon: 'none'
      })
      return
    }

    const that = this
    bleTool.stopPrint(res => {
      console.log('停止打印回调:', res)
      that.setData({
        isPrinting: false,
        printerStatus: 'connected',
        printerErrorCode: null,
        printerErrorMessage: ''
      })
      wx.showToast({
        title: '已停止打印',
        icon: 'success'
      })
    }).catch(error => {
      console.error('停止打印失败:', error)
      that.setData({
        isPrinting: false
      })
      wx.showToast({
        title: '停止打印失败',
        icon: 'error'
      })
    })
  },
  /**
   * 断开蓝牙
   * @CallIn index.wxml 中触发点击断开蓝牙按钮点击事件
   * @CallOut bleTool.disconnectBleDevice
   */
  disconnectDevice() {
    bleTool.disconnectBleDevice().then(res => {
      console.log(res);
    }).catch(err => {
      console.log(err);
    })
  },
  /**
   * 打开在线商城
   */
  openOnlineStore() {
    // 检查配置是否完整
    if (onlineStoreConfig.appId === 'YOUR_TARGET_APPID' || onlineStoreConfig.appId === '') {
      wx.showModal({
        title: '配置提醒',
        content: onlineStoreConfig.errorMessages.configNotSet,
        showCancel: false
      });
      return;
    }

    // 构建传递给目标小程序的参数
    const extraData = {
      // 来源标识
      source: 'printer_miniprogram',

      // 时间戳
      timestamp: Date.now()
    };

    // 根据配置决定是否传递设备信息
    if (onlineStoreConfig.passDeviceInfo && this.data.printerDeviceSn) {
      extraData.deviceSn = this.data.printerDeviceSn;
      extraData.printerStatus = this.data.printerStatus;
    }

    // 根据配置决定是否传递标签内容
    if (onlineStoreConfig.passLabelContent) {
      extraData.labelContent = this.data.labelContent;
    }

    // 根据配置决定是否传递模板信息
    if (onlineStoreConfig.passTemplateInfo && this.data.templates[this.data.selectedTemplateIndex]) {
      extraData.selectedTemplate = this.data.templates[this.data.selectedTemplateIndex];
    }

    // 添加自定义业务参数
    if (onlineStoreConfig.customParams && Object.keys(onlineStoreConfig.customParams).length > 0) {
      Object.assign(extraData, onlineStoreConfig.customParams);
    }

    // 调试模式下打印参数
    if (onlineStoreConfig.debug) {
      console.log('跳转在线商城参数:', {
        appId: onlineStoreConfig.appId,
        path: onlineStoreConfig.path,
        extraData: extraData
      });
    }

    wx.navigateToMiniProgram({
      appId: onlineStoreConfig.appId,
      path: onlineStoreConfig.path,
      extraData: extraData,
      envVersion: onlineStoreConfig.envVersion,
      success: (res) => {
        if (onlineStoreConfig.debug) {
          console.log('跳转在线商城成功', res);
        }
      },
      fail: (err) => {
        console.error('跳转在线商城失败', err);

        // 根据错误类型显示相应的提示信息
        let errorMessage = onlineStoreConfig.errorMessages.defaultError;

        if (err.errMsg && err.errMsg.includes('navigateToMiniProgram:fail can not navigateToMiniProgram in current scene')) {
          errorMessage = onlineStoreConfig.errorMessages.sceneNotSupported;
        } else if (err.errMsg && err.errMsg.includes('appId not exist')) {
          errorMessage = onlineStoreConfig.errorMessages.appNotExist;
        } else if (err.errMsg && err.errMsg.includes('path not exist')) {
          errorMessage = onlineStoreConfig.errorMessages.pathNotExist;
        }

        wx.showModal({
          title: '跳转失败',
          content: errorMessage,
          showCancel: false
        });
      }
    });
  },

  /**
   * 显示联系我们弹窗
   */
  showContact() {
    this.setData({
      showContactModal: true
    })
  },

  /**
   * 关闭联系我们弹窗
   */
  closeContact() {
    this.setData({
      showContactModal: false
    })
  },

  /**
   * 复制联系信息
   */
  copyContact(e) {
    const text = e.currentTarget.dataset.text
    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: '已复制',
          icon: 'success'
        })
      }
    })
  },

  /**
   * 刷新耗材信息
   * 手动刷新当前耗材信息，用于用户更换耗材后的情况
   */
  refreshMaterialInfo() {
    if (this.data.printerStatus !== 'connected') {
      wx.showToast({
        title: '请先连接打印机',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: '获取耗材信息...'
    })

    this.getMaterialInfo().then(() => {
      wx.hideLoading()
      this.performCompatibilityCheck()
      wx.showToast({
        title: '耗材信息已更新',
        icon: 'success'
      })
    }).catch(error => {
      wx.hideLoading()
      console.error('刷新耗材信息失败:', error)
      wx.showToast({
        title: '获取耗材信息失败',
        icon: 'none'
      })
    })
  },

  /**
   * 调试方法：手动检查兼容性
   * 可以在控制台调用 getCurrentPages()[0].debugCheckCompatibility()
   */
  debugCheckCompatibility() {
    console.log('=== 手动兼容性检查 ===')
    console.log('当前状态:', {
      materialMismatch: this.data.materialMismatch,
      forcePrint: this.data.forcePrint,
      printerStatus: this.data.printerStatus,
      selectedTemplateIndex: this.data.selectedTemplateIndex
    })

    if (this.data.printerStatus === 'connected') {
      this.checkMaterialCompatibility(true) // 强制刷新
    } else {
      console.log('打印机未连接，无法检查兼容性')
    }
  }
})

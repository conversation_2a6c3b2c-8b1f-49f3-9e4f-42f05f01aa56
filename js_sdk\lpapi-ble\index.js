function t(t,e,i,s){return new(i||(i=Promise))((function(r,n){function a(t){try{h(s.next(t))}catch(t){n(t)}}function o(t){try{h(s.throw(t))}catch(t){n(t)}}function h(t){var e;t.done?r(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(a,o)}h((s=s.apply(t,e||[])).next())}))}function e(t,e,i,s){return new(i||(i=Promise))((function(r,n){function a(t){try{h(s.next(t))}catch(t){n(t)}}function o(t){try{h(s.throw(t))}catch(t){n(t)}}function h(t){var e;t.done?r(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(a,o)}h((s=s.apply(t,e||[])).next())}))}"function"==typeof SuppressedError&&SuppressedError,"function"==typeof SuppressedError&&SuppressedError;class i{static formatDate(t,e){let i=t||"yyyy-MM-dd HH:mm:ss.SSS";const s={"y+":(e=e||new Date).getFullYear(),"M+":e.getMonth()+1,"d+":e.getDate(),"h+":e.getHours(),"H+":e.getHours(),"m+":e.getMinutes(),"s+":e.getSeconds(),"q+":Math.floor((e.getMonth()+3)/3),"S+":e.getMilliseconds()};let r;for(const t in s)if(r=new RegExp(`(${t})`).exec(i)){const e=`${s[t]}`,n=`00${e}`,a=r[1].length>n.length?0:n.length-r[1].length;i=i.replace(r[1],1===r[1].length?e:n.substring(a))}return i}static createRect(t,e,i,s){return{x:t||0,y:e||0,width:i,height:s}}static parseRect(t){if(!t)return;t=t.trim();const e=/^\[?\(?\s*([0-9.+-]+)\s*,\s*([0-9.+-]+)\s*,\s*([0-9.+-]+)\s*,\s*([0-9.+-]+)\s*\)?]?$/.exec(t);return e?{x:parseFloat(e[1]),y:parseFloat(e[2]),width:parseFloat(e[3]),height:parseFloat(e[4])}:void 0}static isInnerRect(t,e){return t.x>=e.x&&t.x+t.width<=e.x+e.width&&t.y>=e.y&&t.y+t.height<=e.y+e.height}static hasIntersection(t,e){return t.x<e.x+e.width&&e.x<t.x+t.width&&t.y<e.y+e.height&&e.y<t.y+t.height}static getUnionRect(t,e){const i=Math.min(t.x,e.x),s=Math.min(t.y,e.y);return{x:i,y:s,width:Math.max(t.x+t.width,e.x+e.width)-i,height:Math.max(t.y+t.height,e.y+e.height)-s}}}var s,r,n,a,o,h,c,l,d;!function(t){t[t.NONE=0]="NONE",t[t.ERROR=1]="ERROR",t[t.WARN=2]="WARN",t[t.INFO=3]="INFO",t[t.LOG=4]="LOG"}(s||(s={}));class u{static innerLog(t){this.output?this.output.log(t):console.log(t)}static innerWarn(t){this.output?this.output.warn(t):console.warn(t)}static innerError(t){this.output?this.output.error(t):console.error(t)}static setLevel(t){this.logRecords.push(this.logLevel),this.logLevel=t}static setLogOutput(t){this.output=t}static restorePrev(){const t=this.logRecords.shift();void 0!==t&&(this.logLevel=t)}static log(t,e){if(this.logLevel>=s.LOG){const s=i.formatDate("HH:mm:ss.SSS");"object"==typeof t||e?(u.innerLog(`【LOG  】[${s}]: ${e||""}`),u.innerLog(t)):u.innerLog(`【LOG  】[${s}]: ${t}`)}}static info(t,e){if(this.logLevel>=s.INFO){const s=i.formatDate("HH:mm:ss.SSS");"object"==typeof t||e?(u.innerLog(`【INFO 】[${s}]: ${e||""}`),u.innerLog(t)):u.innerLog(`【INFO 】[${s}]: ${t}`)}}static warn(t,e){if(this.logLevel>=s.WARN){const s=i.formatDate("HH:mm:ss.SSS");"object"==typeof t||e?(u.innerWarn(`【WARN 】[${s}]: ${e||""}`),u.innerWarn(t)):u.innerWarn(`【WARN 】[${s}]: ${t}`)}}static error(t,e){if(this.logLevel>=s.ERROR){const s=i.formatDate("HH:mm:ss.SSS");"object"==typeof t||e?(u.innerError(`【ERROR】[${s}]: ${e||""}`),u.innerError(t)):u.innerError(`【ERROR】[${s}]: ${t}`)}}}u.logRecords=[],u.logLevel=s.WARN,function(t){t[t.Top=0]="Top",t[t.Bottom=1]="Bottom",t[t.Left=2]="Left",t[t.Right=3]="Right"}(r||(r={})),function(t){t[t.None=0]="None",t[t.UnderLine=1]="UnderLine",t[t.ThroughLine=2]="ThroughLine",t[t.OverLine=3]="OverLine"}(n||(n={})),function(t){t[t.REGULAR=0]="REGULAR",t[t.BOLD=1]="BOLD",t[t.ITALIC=2]="ITALIC",t[t.UNDERLINE=4]="UNDERLINE",t[t.STRIKEOUT=8]="STRIKEOUT"}(a||(a={})),function(t){t[t.Unset=255]="Unset",t[t.Start=0]="Start",t[t.Center=1]="Center",t[t.End=2]="End",t[t.Stretch=3]="Stretch"}(o||(o={})),function(t){t[t.None=0]="None",t[t.Left=1]="Left",t[t.HInner=2]="HInner",t[t.Right=4]="Right",t[t.HOuter=8]="HOuter",t[t.Top=16]="Top",t[t.VInner=32]="VInner",t[t.Bottom=64]="Bottom",t[t.VOuter=128]="VOuter",t[t.Inner=34]="Inner",t[t.Outer=136]="Outer"}(h||(h={})),function(t){t[t.None=0]="None",t[t.Char=1]="Char",t[t.Word=2]="Word"}(c||(c={})),function(t){t[t.Auto=0]="Auto",t[t.MM=1]="MM",t[t.Pix=2]="Pix"}(l||(l={})),function(t){t[t.LineSpace_Custom=0]="LineSpace_Custom",t[t.LineSpace_1_0=1]="LineSpace_1_0",t[t.LineSpace_1_2=1.2]="LineSpace_1_2",t[t.LineSpace_1_5=1.5]="LineSpace_1_5",t[t.LineSpace_2_0=2]="LineSpace_2_0"}(d||(d={}));class g{static getLineMode(t){switch(t=t||""){case g.LineSpace_1_0:return d.LineSpace_1_0;case g.LineSpace_1_2:return d.LineSpace_1_2;case g.LineSpace_1_5:return d.LineSpace_1_5;case g.LineSpace_2_0:return d.LineSpace_2_0;default:return t?d.LineSpace_Custom:d.LineSpace_1_0}}static toString(t,e){switch(t){case d.LineSpace_1_0:return g.LineSpace_1_0;case d.LineSpace_1_2:return g.LineSpace_1_2;case d.LineSpace_1_5:return g.LineSpace_1_5;case d.LineSpace_2_0:return g.LineSpace_2_0;case d.LineSpace_Custom:return"number"==typeof e?String(e):""}}static getModeValue(t,e){switch(t){case d.LineSpace_1_2:return.2*e;case d.LineSpace_1_5:return.5*e;case d.LineSpace_2_0:return 1*e;case d.LineSpace_1_0:case d.LineSpace_Custom:return 0;default:return t>1?e*(t-1):0}}static valueOf(t,e){if("string"==typeof t){const i=this.getLineMode(t);return i===d.LineSpace_Custom?Number.parseFloat(t):this.getModeValue(i,e)}return this.getModeValue(t,e)}}g.LineSpace_1_0="1_0",g.LineSpace_1_2="1_2",g.LineSpace_1_5="1_5",g.LineSpace_2_0="2_0";class p{static isNull(t){return null==t}static isTransparent(t){return"transparent"===(t=(t||"").toLowerCase())||t.match(/^#[0-9a-f]{3}0$/i)||t.match(/^#[0-9a-f]{6}00$/i)}static isHorizontal(t){switch(t){case 0:case 2:case 180:return!0;default:return!1}}static isPortrait(t){switch(t){case 1:case 3:case 90:case 270:return!0;default:return!1}}static validateColorStr(t){return t?/^[0-9A-Fa-f]+$/.exec(t)?`#${t}`:/^#([0-9A-Fa-f]+)$/.exec(t)?t:/^0[xX]([0-9A-Fa-f]+)$/.exec(t)?`#${t.substring(2)}`:t:t}get supportLineDash(){return this.supports("setLineDash")}get Canvas(){return this._canvas||this.setCanvas(this.createCanvas()),this._canvas}setCanvas(t){if(!t)return;this._canvas=t,this.initOptions.position&&this._canvas.style&&(this._canvas.style.position=this.initOptions.position);const e=!!this.IsApiMode;this.ctx=this._canvas.getContext("2d",{willReadFrequently:e}),this.setTextBaseline(p.TEXT_BASELINE_DEFAULT),this.setTextAlign("left")}get Context(){return this.ctx}get Width(){return this.width}get Height(){return this.height}get Orientation(){return this.orientation}get BorderAlign(){return this._borderAlign}set BorderAlign(t){this._borderAlign=t}get ModulePixels(){return this._modulePixels}set ModulePixels(t){t>0&&(this._modulePixels=t)}get DashLen(){return this._dashLen||[]}set DashLen(t){this._dashLen=t}get IsApiMode(){return this._apiMode||!1}set IsApiMode(t){this._apiMode=t}get PixPerUnit(){return this._pixPerUnit}get B1DTextAlign(){return this._barcodeTextAlign}set B1DTextAlign(t){this._barcodeTextAlign="number"==typeof t?t:o.Unset}get HorizontalAlign(){return this._horizontalAlign}set HorizontalAlign(t){this._horizontalAlign="number"==typeof t?t:o.Unset}get VerticalAlign(){return this._verticalAlign}set VerticalAlign(t){this._verticalAlign="number"==typeof t?t:o.Unset}get ItemOrientation(){return this._rotation||0}set ItemOrientation(t){this._rotation=t}get LineWidth(){return this._lineWidth>0?this._lineWidth:p.LINE_WIDTH}set LineWidth(t){t>=0&&(this._lineWidth=t)}get Foreground(){return this._foreground||p.COLOR_FG_DEFAULT}set Foreground(t){this._foreground=t}get Background(){return this._background||p.COLOR_BG_DEFAULT}set Background(t){this._background=t}get AutoReturn(){return this._autoReturn}set AutoReturn(t){this._autoReturn=t}get FontName(){return this.fontName}set FontName(t){this.fontName=t}get FontHeight(){return this.fontHeight}set FontHeight(t){t>=0&&(this.fontHeight=t)}get FontStyle(){return this.fontStyle}set FontStyle(t){t>=0&&(this.fontStyle=t)}get LineSpace(){return this._lineSpace}set LineSpace(t){this._lineSpace=t}get CharSpace(){return this._charSpace}set CharSpace(t){this._charSpace=t}constructor(t){this.fontName=p.FONT_NAME,this.fontStyle=0,this.fontHeight=0,this.width=0,this.height=0,this.orientation=0,this.jobName="",this._modulePixels=2,this._lineWidth=0,this._dashLen=[],this._foreground=p.COLOR_FG_DEFAULT,this._rotation=0,this._barcodeTextAlign=o.Unset,this._horizontalAlign=o.Unset,this._verticalAlign=o.Unset,this._autoReturn=c.Char,this._charSpace=0,this._lineSpace=0,this._borderAlign=h.Inner,this._pixPerUnit=1;const e=t||{};this.initOptions=e,this._canvasCreator=e.creator,this._canvasClearAction=e.onCanvasClear,this._apiMode=e.apiMode,e.background&&(this._background=e.background),e.foreground&&(this._foreground=e.foreground),e.canvas&&this.setCanvas(e.canvas)}createCanvas(){return this._canvasCreator?this._canvasCreator():document&&void 0!==document.createElement?document.createElement("canvas"):void 0}getReturnMode(t){return void 0!==t?t:this.AutoReturn}getLineSpace(t,e,i){void 0===t&&"string"==typeof i&&(i.match(/^[0-9]+.?[0-9]*$/)?i=Number(i):(t=i,i=this.LineSpace));const s="string"==typeof t?g.getLineMode(t):t;return void 0===s||s===d.LineSpace_Custom?i||this.LineSpace:g.valueOf(s,e)}setTextBaseline(t){const e=this.ctx;void 0!==this.ctx.textBaseline&&(this.ctx.textBaseline=t),"function"==typeof e.setTextBaseline&&e.setTextBaseline(p.TEXT_BASELINE_DEFAULT)}setTextAlign(t){const e=this.ctx;void 0!==this.ctx.textAlign&&(this.ctx.textAlign=t),"function"==typeof e.setTextAlign&&e.setTextAlign(t)}appendTo(t){return t instanceof Element&&(t.innerHTML="",t.appendChild(this.Canvas),!0)}supports(t){switch(t){case"getImageData":return void 0!==this.ctx.getImageData;case"setLineDash":return void 0!==this.ctx.setLineDash;case"toDataURL":return void 0!==this.Canvas.toDataURL;case"toDataURLWithQuality":try{return this.Canvas.toDataURL("image/jpeg",0),!0}catch(t){return!1}case"measureText":return void 0!==this.ctx.measureText;default:return!1}}clearAll(){this.ctx.clearRect(0,0,this.Canvas.width,this.Canvas.height),this._background&&(this.ctx.fillStyle=p.validateColorStr(this.Background),this.ctx.fillRect(0,0,this.Canvas.width,this.Canvas.height)),"function"==typeof this._canvasClearAction&&this._canvasClearAction(this.Canvas,this.ctx)}getLineWidth(t){return t&&t>0?t:this.LineWidth}getDashLen(t){return t||this.DashLen}setFont(t,e,i){const s=Math.round(t||this.fontHeight),r=e||this.fontStyle,n=i||this.fontName||p.FONT_NAME,o=r&a.ITALIC?"italic":"normal",h=r&a.BOLD?"bold":"normal";this.ctx.font=`${o} normal ${h} ${s}px ${n}`}processPaddings(t,e){const i=e[0]||0,s=e.length>1?e[1]:i,r=e.length>2?e[2]:i,n=e.length>3?e[3]:s;t.x=t.x||0,t.y=t.y||0,t.x+=n,"number"==typeof t.width&&(t.width-=n+s),t.y+=i,"number"==typeof t.height&&(t.height-=i+r)}processPadding(t){return Array.isArray(t.padding)&&t.padding.length>0?this.processPaddings(t,t.padding):"number"==typeof t.padding?this.processPaddings(t,[t.padding]):t}getHorizontalAlignment(t){return"number"==typeof t?t:this._horizontalAlign}getVerticalAlignment(t){return"number"==typeof t?t:this._verticalAlign}getB1DTextAlignment(t){return"number"==typeof t?t:this.B1DTextAlign}getItemRotation(t){let e="number"==typeof t?t:this.ItemOrientation;return e>=360&&(e%=360),e>0&&e<4&&(e*=90),e}startJob(t){if("number"!=typeof t.width&&(t.width=0),"number"!=typeof t.height&&(t.height=0),"number"!=typeof t.printerWidth&&(t.printerWidth=0),t.width<=0&&t.printerWidth>0&&(t.width=t.printerWidth),!(t.width<=0&&t.height<=0))return t.canvas&&this.setCanvas(t.canvas),this.width=Math.round(t.width||t.height||0),this.height=Math.round(t.height||t.width),this.orientation=t.orientation||0,t.jobName&&(this.jobName=t.jobName),this.Canvas.width=this.width,this.Canvas.height=this.height,t.printMode?(this._background=p.COLOR_BG_DEFAULT,this._foreground=p.COLOR_FG_DEFAULT):(t.backgroundColor&&(this._background=t.backgroundColor),t.color&&(this._foreground=t.color)),this.clearAll(),!t.printMode&&t.backgroundImage&&this.drawImage({image:t.backgroundImage,width:t.width,height:t.height}),this.Canvas;u.warn("---- 未指定标签大小！")}commitJob(){return this.Canvas}setRotation(t,e,i){if((t=(t||0)%360)>0){const s=e||{x:0,y:0},r=i||{width:0,height:0};s.x=Math.round(s.x+.5*r.width)+.5,s.y=Math.round(s.y+.5*r.height)+.5,this.ctx.translate(s.x,s.y),this.ctx.rotate(t*Math.PI/180),this.ctx.translate(-s.x,-s.y)}}drawLine(t){let e=t.x1||0,i=t.y1||0,s="number"==typeof t.x2?t.x2:e,r="number"==typeof t.y2?t.y2:i;const n="number"==typeof t.x?t.x:e,a="number"==typeof t.y?t.y:i;if(e===s&&i===r){const o=t.width||0,h=t.height||0;if(o>0||h>0){const c=Math.min(o,h);c>0&&(t.lineWidth=c),o>h?(t.x1=e=n,t.x2=s=n+o,t.y1=i=a+.5*h,t.y2=r=i):(t.x1=e=n+.5*o,t.x2=s=e,t.y1=i=a,t.y2=r=a+h)}else t.x1=e=n,t.x2=s=n+this.width,t.y1=i=a,t.y2=r=a}else i===r?"number"!=typeof t.y1&&"number"!=typeof t.y2&&"number"==typeof t.y&&(t.y1=i=t.y,t.y2=r=t.y):e===s&&"number"!=typeof t.x1&&"number"!=typeof t.x2&&"number"==typeof t.x&&(t.x1=e=t.x,t.x2=s=t.x);this.ctx.save();const o=this.getItemRotation(t.orientation);this.setRotation(o,{x:.5*(e+s),y:.5*(i+r)});const h=this._drawLine(t);return this.ctx.restore(),h}_drawLine(t){const e=Math.floor(t.x1||0)+.5,i=Math.floor(t.y1||0)+.5,s="number"==typeof t.x2?Math.floor(t.x2)+.5:e,r="number"==typeof t.y2?Math.floor(t.y2)+.5:i;this.ctx.strokeStyle=p.validateColorStr(t.color||this.Foreground),this.ctx.lineWidth=Math.ceil(this.getLineWidth(t.lineWidth)),this.ctx.beginPath();let n=t.dashLens;return(!n||n.length<=0)&&(n=(t.dashLen||"").split(",").map((t=>+t))),this.supportLineDash&&this.ctx.setLineDash(n||[]),this.ctx.moveTo(e,i),this.ctx.lineTo(s,r),this.ctx.stroke(),this.supportLineDash&&this.ctx.setLineDash([]),!0}drawRect(t){const e=t.x||0,i=t.y||0,s=t.width||0,r=t.height||0;if(t.cornerWidth&&t.cornerWidth>0||t.cornerHeight&&t.cornerHeight>0)return this.drawRoundRect(t);if(s>0&&r>0){this.ctx.save();const n=this.getItemRotation(t.orientation);return this.setRotation(n,{x:e,y:i},{width:s,height:r}),this._drawRect(t),this.ctx.restore(),!0}return!1}_drawRect(t){const e=p.validateColorStr(t.color||this.Foreground);let s=t.x||0,r=t.y||0,n=t.width||t.height||0,a=t.height||t.width||0;if(n<=0)return!1;if(e&&p.isTransparent(e))return!0;if(t.fill){const t=this.adjustRect(i.createRect(s,r,n,a),this.BorderAlign);this.ctx.fillStyle=e,this.ctx.fillRect(t.x,t.y,t.width,t.height)}else{let o=this.getLineWidth(t.lineWidth),h=t.dashLens;(!h||h.length<=0)&&(h=(t.dashLen||"").split(",").map((t=>+t))),o=Math.ceil(o),s+=.5*o,r+=.5*o,n-=o,a-=o;const c=this.adjustRect(i.createRect(s,r,n,a),this.BorderAlign);this.ctx.lineWidth=o||this.LineWidth,this.ctx.strokeStyle=e,this.supportLineDash&&h.length>0&&this.ctx.setLineDash(h),this.ctx.strokeRect(c.x,c.y,c.width,c.height),this.supportLineDash&&this.ctx.setLineDash([])}return!0}adjustRect(t,e){let i=t.x,s=t.y,r=t.x+t.width,n=t.y+t.height;const a=240&e;switch(15&e){case h.Left:i=Math.floor(i),r=Math.floor(r);break;case h.HInner:i=Math.ceil(i),r=Math.floor(r);break;case h.Right:i=Math.ceil(i),r=Math.ceil(r);break;case h.HOuter:i=Math.floor(i),r=Math.ceil(r);break;default:i=Math.round(i),r=Math.round(r)}switch(a){case h.Top:s=Math.floor(s),n=Math.floor(n);break;case h.VInner:s=Math.ceil(s),n=Math.floor(n);break;case h.Bottom:s=Math.ceil(s),n=Math.ceil(n);break;case h.VOuter:s=Math.floor(s),n=Math.ceil(n);break;default:s=Math.round(s),n=Math.round(n)}const o=r-i,c=n-s;return{x:i,y:s,width:o>0?o:1,height:c>0?c:1}}drawRoundRect(t){let e=t.x||0,i=t.y||0,s=t.width||0,r=t.height||0;const n=t.cornerWidth||t.cornerHeight||0;if(s<=0&&r<=0)return!1;if(n<=0)return this.drawRect(t);const a=.5*Math.min(s,r),o=p.validateColorStr(t.color||this.Foreground);let h=this.getLineWidth(t.lineWidth);t.fill||(h>a&&(h=a),e+=.5*h,i+=.5*h,s-=h,r-=h),this.ctx.save();const c=this.getItemRotation(t.orientation);return this.setRotation(c,{x:e,y:i},{width:s,height:r}),this.ctx.translate(e,i),this.ctx.fillStyle=o,this.ctx.strokeStyle=o,this.drawRoundRectPath(s,r,n),t.fill?this.ctx.fill():(this.ctx.lineWidth=h,this.ctx.stroke()),this.ctx.restore(),!0}drawRoundRectPath(t,e,i){i=Math.min(i,.5*t,.5*e),this.ctx.beginPath(),this.ctx.arc(t-i,e-i,i,0,Math.PI/2),this.ctx.lineTo(i,e),this.ctx.arc(i,e-i,i,Math.PI/2,Math.PI),this.ctx.lineTo(0,i),this.ctx.arc(i,i,i,Math.PI,3*Math.PI/2),this.ctx.lineTo(t-i,0),this.ctx.arc(t-i,i,i,3*Math.PI/2,2*Math.PI),this.ctx.lineTo(t,e-i),this.ctx.closePath()}drawEllipse(t){let e=t.x||0,i=t.y||0,s=t.width||t.height||0,r=t.height||t.width||0;if(s<=0&&r<=0)return!1;if(s===r)return this.drawCircle(t);const n=p.validateColorStr(t.color||this.Foreground),a=this.getLineWidth(t.lineWidth);e+=.5*a,i+=.5*a,s-=a,r-=a;const o=.5*s,h=.5*r;e+=o,i+=h;const c=this.getItemRotation(t.orientation);if(this.ctx.save(),this.setRotation(c,{x:e,y:i}),this.ctx.lineWidth=a,this.ctx.fillStyle=n,this.ctx.strokeStyle=n,this.ctx.ellipse)this.ctx.beginPath(),this.ctx.ellipse(e,i,o,h,0,0,2*Math.PI);else{const t=.5*o,s=.6*h;this.ctx.translate(e,i),this.ctx.beginPath(),this.ctx.moveTo(0,h),this.ctx.bezierCurveTo(t,h,o,s,o,0),this.ctx.bezierCurveTo(o,-s,t,-h,0,-h),this.ctx.bezierCurveTo(-t,-h,-o,-s,-o,0),this.ctx.bezierCurveTo(-o,s,-t,h,0,h),this.ctx.closePath()}return t.fill?this.ctx.fill():this.ctx.stroke(),this.ctx.restore(),!0}drawCircle(t){let e=t.x||0,i=t.y||0,s=t.radius||0;if(s<=0){const r=t.width||0,n=t.height||0;if(r>0&&n>0)s=.5*Math.min(r,n),e+=.5*r,i+=.5*n;else if(r>0)s=.5*r,e+=s;else{if(!(n>0))return!1;s=.5*n,i+=s}}const r=p.validateColorStr(t.color||this.Foreground),n=this.getLineWidth(t.lineWidth);return s-=.5*n,this.ctx.lineWidth=n,this.ctx.fillStyle=r,this.ctx.strokeStyle=r,this.ctx.beginPath(),this.ctx.arc(e,i,s,0,2*Math.PI),this.ctx.closePath(),t.fill?this.ctx.fill():this.ctx.stroke(),!0}drawText(t){this.processPadding(t);const e=this.getItemRotation(t.orientation);if(e>0){const i=t.x||0,s=t.y||0;let r=t.width||0,n=t.height||0;if((r<=0||n<=0)&&void 0!==this.ctx.measureText){const e=this.measureTextExt(t);r<=0&&(r=e.width),n<=0&&(n=e.height)}if(r<=0||n<=0)return!1;const a=i+.5*r,o=s+.5*n;this._apiMode&&p.isPortrait(e)&&("boolean"!=typeof t.fixedSize||t.fixedSize)&&(t.width=n,t.height=r,t.x=a-.5*n,t.y=o-.5*r),this.ctx.save(),this.setRotation(e,{x:a,y:o});const h=this._drawText(t);return this.ctx.restore(),h}return this._drawText(t)}_drawText(t){if(null==t.text||null==t.text){if(void 0===t.content)return!1;t.text=t.content}const e=this.getReturnMode(t.autoReturn),i="boolean"!=typeof t.autoShrink||t.autoShrink,s=t.x||0;let r=t.y||0;const n=t.width||0;let a=t.height||0;(!t.texts||t.texts.length<=0)&&(t.texts=Array.isArray(t.text)?t.text:[String(t.text)]);const h=t.texts||[],l=t.fontHeight||a||this.fontHeight;let d=this.getHorizontalAlignment(t.horizontalAlignment),u=this.getVerticalAlignment(t.verticalAlignment);if(l<=0)return!1;(d>o.Stretch||d<o.Start)&&(d=o.Start),(u>o.Stretch||u<o.Start)&&(u=o.Start),"number"!=typeof t.charSpace&&(t.charSpace=this.CharSpace);const g=t.charSpace>0?t.charSpace:0;this.ctx.fillStyle=p.validateColorStr(t.color||this.Foreground),this.setFont(l,t.fontStyle,t.fontName),this.ctx.textBaseline=p.TEXT_BASELINE_DEFAULT;let m=[];for(let t of h)"string"!=typeof t?m.push(String(t)):(t=t.replace("\t",""),m.push(...t.split("\n")));e!==c.None&&n>0&&(m=this.splitText(Object.assign(Object.assign({},t),{text:m,width:n,fontHeight:l,autoReturn:e})));let f=this.getLineSpace(t.lineSpaceMode,l,t.lineSpace);if(m.length>0){const e=l*m.length,c=e+(m.length-1)*f;a<=0&&(a=c),c<=a||!i||l<7?(u===o.Stretch?f=(a-e)/(m.length-1):u===o.End?r+=a-c:u===o.Center&&(r+=.5*(a-c)),this.drawTextList1({texts:m,x:s,y:r,width:n,fontHeight:l,lineSpace:f,charSpace:g,horizontalAlignment:d,fontStyle:t.fontStyle||this.fontStyle,color:t.color})):this._drawText(Object.assign(t,{text:h,width:n,height:a,fontHeight:.95*l}))}return!0}drawTextList1(t){const e=t.lineSpace>0?t.lineSpace:0;let i=0;for(const s of t.texts)this._drawSingleLineText(s,t.x,t.y+i,t.width,t.fontHeight,t.horizontalAlignment,t.charSpace,t.fontStyle,t.color),i+=t.fontHeight+e}_drawSingleLineText(t,e,i,s,r,n,h,c,l){t=String(t);const d=.14*r,u=r-d,g=void 0!==this.ctx.measureText?this.ctx.measureText(t):{width:0},p="number"==typeof g.actualBoundingBoxAscent?g.actualBoundingBoxAscent:u,m="number"==typeof g.actualBoundingBoxDescent?g.actualBoundingBoxDescent:d,f=t.length>1&&h>0?(t.length-1)*h:0,C=n||o.Start;let P=(null==g?void 0:g.width)||0,b=.05*r;b<1&&(b=1);const y=i+u,A=p+m;if(g&&C===o.Stretch&&s>g.width)h=(s-g.width)/(t.length-1),1===t.length?this.ctx.fillText(t,e+.5*(s-g.width),y):this.fillCharSpaceText(t,e,y,h),P=s;else{const i=g?g.width+f:0;(s=g?s:0)>i&&(C===o.End?e=e+s-i:C===o.Center&&(e+=.5*(s-i))),s>0&&s<i?(this.fillCharSpaceText(t,e,y,h,s),P=s):(this.fillCharSpaceText(t,e,y,h),P=i)}c&a.STRIKEOUT&&this._drawRect({x:e,y:y-p+.5*(A-b),width:P,height:b,color:l,fill:!0}),c&a.UNDERLINE&&this._drawRect({x:e,y:i+r-.5*b,width:P,height:b,color:l,fill:!0})}fillCharSpaceText(t,e,i,s,r){if(t.length<=0)return;const n=void 0!==this.ctx.measureText?this.ctx.measureText(t):void 0,a=t.length>1?(t.length-1)*s:0;if(n&&s>0){const o=n.width+a,h=r&&r<o?r/o:1;let c=e;for(const e of t){const t=this.ctx.measureText(e).width;this.ctx.fillText(e,c,i,t*h),c+=(t+s)*h}}else r&&r>0?this.ctx.fillText(t,e,i,r):this.ctx.fillText(t,e,i)}drawArcText(t){let e=t.x||0,i=t.y||0;const s=t.width||0,r=t.height||0,n=t.lineWidth||0,a="number"==typeof t.padding?t.padding:0;let o=t.radius||0;if(o<=0){if(!(s>0||r>0))return!1;{const n=s>0&&r>0?Math.min(s,r):s>0?s:r;t.radius=o=.5*n,s>0&&(t.x=e+=.5*s),r>0&&(t.y=i+=.5*r)}}n>0&&(this.drawCircle(t),o-=n);const h=String(null===t.text||void 0===t.text?t.content||"":t.text),c=t.fontHeight;o-=c,a>0&&a<o&&(o-=a);const l=void 0!==this.ctx.measureText,d=2*Math.PI*o,u=(l?this.measureText({text:h,fontHeight:c}).width:0)/d*Math.PI*2;this.setFont(c),this.ctx.fillStyle=p.validateColorStr(t.color||this.Foreground),this.ctx.save();const g=this.getItemRotation(t.orientation);this.setRotation(g,{x:e,y:i}),this.ctx.translate(e,i),this.ctx.rotate(.5*-Math.PI);const m=this.ctx.textBaseline;return this.ctx.textBaseline="bottom",h.split("").forEach(((t,e)=>{const i=l?this.ctx.measureText(t).width:c,s=i/d*Math.PI*2;0===e?this.ctx.rotate(.5*-u+.5*s):this.ctx.rotate(s),this.ctx.save(),this.ctx.rotate(Math.PI/2),this.ctx.translate(.5*-i,-o),this.ctx.fillText(t,0,0),this.ctx.restore()})),this.ctx.textBaseline=m,this.ctx.restore(),!0}getTextWidths(t){const e=[];if(!t||void 0===this.ctx.measureText)return e;for(const i of t)e.push(this.ctx.measureText(i).width);return e}draw1DBarcode(t){const e=t.datas,i=t.x||0,s=t.y||0;let r=t.width||0,n=t.height||0,a="number"==typeof t.textHeight&&t.textHeight>0?t.textHeight:0,l=this.getHorizontalAlignment(t.horizontalAlignment);const d="number"==typeof t.autoScaleLevel?t.autoScaleLevel:p.AUTO_SCALE_LEVEL,u="number"!=typeof t.textFlag||t.topText?2:t.textFlag;a=u?a:0;let g=t.topText?a:0;const m=t.datas.map((t=>t.data||"")).join(""),f=m.length*this.PixPerUnit;r=r<f?f:r,(l>o.Stretch||l<o.Start)&&(l=o.Center);let C=r,P=0,b=C/m.length;if(d>0&&b/this.PixPerUnit<d&&l<=o.End)switch(b=1*this.PixPerUnit,C=m.length*b,l){case o.Start:break;case o.End:P=r-C;break;default:P=.5*(r-C)}const y=a>0?p.BarTextMargin*b:0;let A=p.MinBarHeight;a>0&&t.topText?A=2*(a+y)+p.MinBarHeight:a&&(A=a+y+p.MinBarHeight),n<=0?(a<=0&&(a=25),n=3*a):n<A&&(n=A);const I=this.getItemRotation(t.orientation);this.ctx.save(),this.setRotation(I,{x:i,y:s},{width:r,height:n});const R=a+y,E=.5*a+y+1,v="number"==typeof t.textAlign?t.textAlign:t.textAlignment,x=b;let w=i+P,D=s+g+y,S=D,_=s;if(t.topText&&e.length>=7){const r=e.slice(1,6).map((t=>t.data)).join("").length*x;g+=y,this._drawText({text:t.topText,x:i+e[0].data.length*x,y:s,width:r,height:a,fontHeight:a,fontStyle:t.fontStyle,fontName:t.fontName,color:t.color,autoReturn:c.None,horizontalAlignment:"number"==typeof v?v:o.Center,charSpace:t.charSpace})}for(const i of e){if(n>R){const e=i.text?R:R-E,r=n-e-g;1===u?(S=s,D=S+e):(D=s+g,S=D+r,_=S+y),this.BorderAlign=h.None;for(let a=0;a<i.data.length;a++,w+=x)"1"===i.data[a]?(t.bgColor&&this._drawRect({x:w,y:S,width:x,height:e,color:t.bgColor,fill:!0}),this._drawRect({x:w,y:D,width:x,height:r,color:t.color,fill:!0})):t.bgColor&&this._drawRect({x:w,y:s,width:x,height:n,color:t.bgColor,fill:!0})}if(i.text){const s=e.length>2&&i.text.length>1?p.DockCharMargin*x:0,r=i.data.length*x;this._drawText({text:i.text,x:w-r+s,y:_,width:r-2*s,height:a,fontHeight:a,fontStyle:t.fontStyle,fontName:t.fontName,color:t.color,autoReturn:c.None,horizontalAlignment:"number"==typeof v?v:o.Center,charSpace:t.charSpace})}}return this.ctx.restore(),!0}drawQrcode(t){const e=t.data;let s=t.x||0,r=t.y||0,n=t.width||0,a=t.height||n;const c=t.zoneSize||0,l="number"==typeof t.autoScaleLevel?t.autoScaleLevel:p.AUTO_SCALE_LEVEL,d=t.data.cols||0,u=d+2*c,g=u*this.PixPerUnit;n<=0?n=d*this.ModulePixels:n<g&&(n=g),a<=0?a=n:a<g&&(a=g);const m=this.getItemRotation(t.orientation);this.ctx.save(),this.setRotation(m,{x:s,y:r},{width:n,height:a});let f=Math.min(n,a),C=Math.floor(f/u*.4);C=C<1?1:C,c<=0&&(s+=C,r+=C,n-=2*C,a-=2*C),f=Math.min(n,a);let P=f/(u*this.PixPerUnit),b=f/u;l>0&&P<l&&(P=P<1?1:Math.floor(P),b=P*this.PixPerUnit,f=b*u);let y=this.getHorizontalAlignment(t.horizontalAlignment),A=this.getVerticalAlignment(t.verticalAlignment);(y>o.Stretch||y<o.Start)&&(y=o.Center),(A>o.Stretch||A<o.Start)&&(A=o.Center);let I=0,R=0;switch(y){case o.Start:break;case o.End:I=Math.round(n-f);break;default:I=Math.round(.5*(n-f))}switch(A){case o.Start:break;case o.End:R=Math.round(a-f);break;default:R=Math.round(.5*(a-f))}let E=c*b;const v=b,x=i.createRect(Math.floor(s+I),Math.floor(r+R),Math.ceil(f),Math.ceil(f));E<=0&&(E=C,x.x+=C,x.y+=C,x.width-=2*C,x.height-=2*C),this.BorderAlign=h.None,t.bgColor&&this._drawRect({x:x.x,y:x.y,width:x.width,height:x.height,color:t.bgColor,fill:!0});const w=Math.floor(E);x.x-=w,x.y-=w,x.width+=2*w,x.height+=2*w;for(let i=0,s=x.y;i<d;i++,s+=v)for(let r=0,n=x.x;r<d;r++,n+=v)e.data[i*d+r]?this._drawRect({x:n,y:s,width:v,height:v,color:t.color,fill:!0}):t.bgColor&&this._drawRect({x:n,y:s,width:v,height:v,color:t.bgColor,fill:!0});this.ctx.restore()}draw2DBarcode(t){const e=t.data,i=t.x||0,s=t.y||0;let r=t.width||0,n=t.height||r;const a=t.zoneSize||0,c=t.barPixels||this.ModulePixels,l="number"==typeof t.autoScaleLevel?t.autoScaleLevel:p.AUTO_SCALE_LEVEL,d=e.rows||0,u=e.cols||0;if(d<=0||u<=0)return!1;const g=d+2*a,m=u+2*a,f=m*this.PixPerUnit,C=g*this.PixPerUnit;let P=this.getHorizontalAlignment(t.horizontalAlignment),b=this.getVerticalAlignment(t.verticalAlignment),y=0,A=0;(P>o.Stretch||P<o.Start)&&(P=o.Center),(b>o.Stretch||b<o.Start)&&(b=o.Center),r<=0?(y=c*this.PixPerUnit,r=y*m):r<=f?(y=this.PixPerUnit,r=f):y=r/m,n<=0?(A=c*this.PixPerUnit,n=A*g):n<=C?(A=this.PixPerUnit,n=C):A=n/g;const I=this.getItemRotation(t.orientation);this.ctx.save(),this.setRotation(I,{x:i,y:s},{width:r,height:n}),P<o.Stretch&&(y=Math.min(y,A),y<l*this.PixPerUnit&&(y=Math.floor(y/this.PixPerUnit)*this.PixPerUnit)),b<o.Stretch&&(A=Math.min(y,A),A<l*this.PixPerUnit&&(A=Math.floor(A/this.PixPerUnit)*this.PixPerUnit));const R=m*y,E=g*A;let v=0,x=0;switch(P){case o.Start:break;case o.End:v=Math.round(r-R);break;default:v=Math.round(.5*(r-R))}switch(b){case o.Start:break;case o.End:x=Math.round(n-E);break;default:x=Math.round(.5*(n-E))}const w=Math.floor(i+v),D=Math.floor(s+x);this.BorderAlign=h.None,t.bgColor&&a>0&&(this._drawRect({x:w,y:D,width:Math.round(R),height:Math.round(A*a),color:t.bgColor,fill:!0}),this._drawRect({x:w,y:Math.round(D+(d+a)*A),width:Math.round(R),height:Math.round(a*A),color:t.bgColor,fill:!0}),this._drawRect({x:w,y:D,width:Math.round(y*a),height:Math.round(E),color:t.bgColor,fill:!0}),this._drawRect({x:Math.round(w+(u+a)*y),y:D,width:Math.round(a*y),height:Math.round(E),color:t.bgColor,fill:!0}));for(let i=0,s=D+A*a;i<d;i++,s+=A)for(let r=0,n=w+y*a;r<u;r++,n+=y)e.data[i*(e.cols||0)+r]?this._drawRect({x:n,y:s,width:y,height:A,color:t.color,fill:!0}):t.bgColor&&this._drawRect({x:n,y:s,width:y,height:A,color:t.bgColor,fill:!0});return this.ctx.restore(),!0}drawImage(t){const e=t.image||t.img,i=e?e.dzSrc||e:void 0;if(!i)return!1;const s=Math.ceil(t.x||0),r=Math.ceil(t.y||0),n=Math.floor(t.width||0)||i.width,a=Math.floor(t.height||0)||i.height;this.ctx.save();const o=this.getItemRotation(t.orientation);return this.setRotation(o,{x:s,y:r},{width:n,height:a}),t.swidth&&t.sheight?this.ctx.drawImage(i,t.sx||0,t.sy||0,t.swidth,t.sheight,s,r,n,a):t.width||t.height?this.ctx.drawImage(i,s||0,r||0,n,a):this.ctx.drawImage(i,s,r),this.ctx.restore(),!0}drawImageResizeLabel(t,e){if(!t.img||this.width<=0||this.height<=0)return;const i=t.img,s=i.width||t.imageWidth||0,r=i.height||t.imageHeight||0;let n=0,a=0,o=0;if(s<=0||r<=0)return void u.warn("---- drawImageResizeLabel: 无法获取 img 对象的像素大小，所以无法对目标图片进行分割绘制！");s/r<this.width/this.height?(o=this.height,a=s*this.height/s,n=this.height/r):(a=this.width,o=this.width*r/s,n=this.width/s);let h=n;t.fullOfLabel?h=n:t.relativeScale&&t.relativeScale>0?h=t.relativeScale*n:e&&(h=e);const c=Math.floor(t.left),l=Math.floor(t.top),d=Math.ceil(t.right),g=Math.ceil(t.bottom),p=d-c,m=g-l,f=s-d,C=r-g,P=a/(c+f+1),b=o/(l+C+1),y=Math.min(P,b);h>y&&(h=t.relativeScale&&t.relativeScale>0?y:n);const A=Math.floor(c*h),I=Math.floor(l*h),R=Math.floor(f*h),E=Math.floor(C*h),v=this.width-R,x=this.height-E;if(this.ctx.drawImage(i,0,0,c,l,0,0,A,I),this.ctx.drawImage(i,d,0,f,l,v,0,R,I),this.ctx.drawImage(i,d,g,f,C,v,x,R,E),this.ctx.drawImage(i,0,g,c,C,0,x,A,E),t.tileMode){const t=a-A-R,e=o-I-E;let s=t,r=e,n=p,h=m;if(t>0)for(let e=A;e<v;e+=t)v-e<t?(s=v-e,n=p*s/t):(s=t,n=p),this.ctx.drawImage(i,c,0,n,l,e,0,s,I),this.ctx.drawImage(i,c,g,n,C,e,x,s,E);if(e>0)for(let t=I;t<x;t+=e)x-t<e?(r=x-t,h=m*r/e):(r=e,h=m),this.ctx.drawImage(i,0,l,c,h,0,t,A,r),this.ctx.drawImage(i,d,l,f,h,v,t,R,r)}else{const t=v-A,e=x-I;this.ctx.drawImage(i,c,0,p,l,A,0,t,I),this.ctx.drawImage(i,d,l,f,m,v,I,R,e),this.ctx.drawImage(i,c,g,p,C,A,x,t,E),this.ctx.drawImage(i,0,l,c,m,0,I,A,e)}}measureText(t){return(t.fontHeight||t.fontStyle||t.fontName)&&this.setFont(t.fontHeight,t.fontStyle,t.fontName),void 0!==this.ctx.measureText?this.ctx.measureText(t.text||""):{width:0}}measureTextExt(t){const e=t.fontHeight||this.fontHeight;let i=t.width||0;const s=this.splitText(Object.assign(t,{text:t.text||""})),r=t.lineSpace||0,n=t.charSpace||0,a=s.length>1?(s.length-1)*r:0;if(i<=0&&void 0!==this.ctx.measureText)for(let t=0;t<s.length;t++)if(s[t].length>0){const e=this.ctx.measureText(s[t]).width+(s[t].length-1)*n;e>i&&(i=e)}return{width:i,height:e*s.length+a}}measureFontSize(t){const e=t.text||"",i=t.width||0,s=t.minFontSize||16;if(i<=0||e.length<=0||void 0===this.ctx.measureText)return t.fontHeight||0;let r,n=t.fontHeight||this.fontHeight;do{if(r=this.measureText({text:e,fontHeight:n,fontStyle:t.fontStyle,fontName:t.fontName}),r.width<=i)return n;n*=.95}while(n<=s);return s}findSplitPosition(t,e,i,s){if(t.length<=1)return t.length;const r=s||0;let n=1,a=this.ctx.measureText(t.substring(0,n)).width;if(a>=e)return n;let o=0,h=0;if(r>0)for(;h<e&&o<t.length;)o+r>t.length?o=t.length:(h>a&&(n=o,a=h),o+=r),h=this.ctx.measureText(t.substring(0,o)).width+(o-1)*i;else o=t.length,h=this.ctx.measureText(t).width+(o-1)*i;if(h<=e)return o;for(;n<o&&o!==n+1;){const s=n+Math.floor((o-n)/2),r=this.ctx.measureText(t.substring(0,s)).width+(s-1)*i;if(r>e)o=s,h=r;else if(n=s,a=r,r>=e)break}return n}findWordSplitPos(t,e){let i=0;if(e>=t.length||/\W/.exec(t.charAt(e)))return e;for(i=e-1;i>=0&&!/\W/.exec(t.charAt(i));i--);return i+1}splitText(t){if(p.isNull(t.text))return[];"number"!=typeof t.charSpace&&(t.charSpace=this.CharSpace);const e=t.charSpace>0?t.charSpace:0,i=Array.isArray(t.text)?t.text:[String(t.text)],s=[],r=void 0!==this.ctx.measureText;for(const t of i)s.push(...t.split("\n"));let n=0,a=0;const o=[];this.setFont(t.fontHeight,t.fontStyle,t.fontName);for(const i of s)if(t.autoReturn&&t.width&&t.width>0&&r){let s=i;for(;s.length>0;)n=this.findSplitPosition(s,t.width,e,t.measureOptimizeStep),t.autoReturn===c.Word&&(a=this.findWordSplitPos(s,n),a>0&&a<n&&(n=a)),o.push(s.substring(0,n)),s=n<s.length?s.substring(n):""}else o.push(i);return o}inverseColors(){const t=this.ctx,e=this.getImageData();if(e){const i=e.data;for(let t=0;t<i.length;t+=4)i[t]=255-i[t],i[t+1]=255-i[t+1],i[t+2]=255-i[t+2];return t.putImageData(e,0,0),!0}return!1}horizontalFlip(){const t=this.ctx,e=this.getImageData(),i=t.createImageData(this.Canvas.width,this.Canvas.height);if(e&&i){const s=e.width,r=e.height;for(let t=0;t<r;t++)for(let r=0;r<s;r++)i.data[t*s*4+4*r+0]=e.data[t*s*4+4*(s-r)+0],i.data[t*s*4+4*r+1]=e.data[t*s*4+4*(s-r)+1],i.data[t*s*4+4*r+2]=e.data[t*s*4+4*(s-r)+2],i.data[t*s*4+4*r+3]=e.data[t*s*4+4*(s-r)+3];return t.putImageData(i,0,0),!0}return!1}getImageData(){return"function"==typeof this.ctx.getImageData?this.ctx.getImageData(0,0,this.Canvas.width,this.Canvas.height):void u.info("---- 当前绘制环境不支持函数：getImageData")}processCanvasPixels(t){if(this.ctx){const e=this.ctx.createImageData(this.Canvas.width,this.Canvas.height),i=e.data,s=this.ctx.getImageData(0,0,this.Canvas.width,this.Canvas.height).data;let r=0;for(let e=0;e<s.length;e+=4)r=t(s[e],s[e+1],s[e+2],s[0]),i[e]=i[e+1]=i[e+2]=r,i[e+3]=s[e+3];this.ctx.putImageData(e,0,0)}}toGray256(){this.processCanvasPixels(((t,e,i,s)=>Math.round(.3*t+.59*e+.11*i)))}toBlackWhite(t){const e=t||128;this.processCanvasPixels(((t,i,s,r)=>Math.round(.3*t+.59*i+.11*s)<=e?0:255))}}p.NO_START_STOP=4,p.MinBarHeight=2,p.BarTextMargin=1,p.DockHorizMargin=7,p.DockCharMargin=1,p.BarIsbnMargin=0,p.AUTO_SCALE_LEVEL=2,p.COLOR_FG_DEFAULT="#000",p.COLOR_BG_DEFAULT="#fff",p.LINE_WIDTH=28,p.FONT_NAME="黑体",p.TEXT_BASELINE_DEFAULT="alphabetic";class m{constructor(t){this._dpi=203,this._dpm=203/25.4,this._offsetX=0,this._offsetY=0,this.cvs=new p(t)}get Base(){return this.cvs}get ScaleUnit(){return this._scaleUnit||l.Auto}get Dpi(){return this._dpi}set Dpi(t){t&&t>0&&(this._dpi=t,this._dpm=t/25.4)}get DPM(){return this._dpm}set DPM(t){t>0&&(this._dpm=t,this._dpi=25.4*t)}get OffsetX(){return this._offsetX}set OffsetX(t){this._offsetX=t}get OffsetY(){return this._offsetY}set OffsetY(t){this._offsetY=t}get Width(){return this.cvs.Width/this.DPM}get Height(){return this.cvs.Height/this.DPM}get CanvasWidth(){return this.cvs.Canvas.width}get CanvasHeight(){return this.cvs.Canvas.height}get Foreground(){return this.cvs.Foreground}set Foreground(t){this.cvs.Foreground=t}setFontName(t){this.cvs.FontName=t}getFontHeight(){return this.invertCvt(this.cvs.FontHeight)}setFontHeight(t){this.cvs.FontHeight=this.cvt(t)}setLineSpace(t){this.cvs.LineSpace=this.cvt(t)}setCharSpace(t){this.cvs.CharSpace=this.cvt(t)}getLineWidth(){return this.invertCvt(this.cvs.LineWidth)}setLineWidth(t){this.cvs.LineWidth=this.cvt(t)}setRotation(t,e,i){return e&&(e.x=this.cvt(e.x),e.y=this.cvt(e.y)),i&&(i.width=this.cvt(i.width),i.height=this.cvt(i.height)),this.cvs.setRotation(t,e,i)}supports(t){return this.cvs.supports(t)}startJob(t){return this._scaleUnit=t.scaleUnit,t.dpi&&(this.Dpi=t.dpi),this.cvs.startJob(this.cvtDrawOptions(t))?this.cvs:void 0}commitJob(){return this.cvs.commitJob()?this.cvs:void 0}drawLine(t){return this.cvs.drawLine(this.cvtDrawOptions(t))}drawRect(t){return this.cvs.drawRect(this.cvtDrawOptions(t))}drawRoundRect(t){return this.cvs.drawRoundRect(this.cvtDrawOptions(t))}drawEllipse(t){return this.cvs.drawEllipse(this.cvtDrawOptions(t))}drawCircle(t){return this.cvs.drawCircle(this.cvtDrawOptions(t))}drawText(t){return this.cvs.drawText(this.cvtDrawOptions(t))}drawArcText(t){return this.cvs.drawArcText(this.cvtDrawOptions(t))}draw1DBarcode(t){return(t=this.cvtDrawOptions(t)).textHeight=this.cvt(t.textHeight),this.cvs.draw1DBarcode(t)}drawQrcode(t){return this.cvs.drawQrcode(this.cvtDrawOptions(t))}draw2DBarcode(t){return this.cvs.draw2DBarcode(this.cvtDrawOptions(t))}drawImage(t){return this.cvs.drawImage(this.cvtDrawOptions(t))}drawImageResizeLabel(t){return this.cvs.drawImageResizeLabel(t,this.DPM/20)}splitText(t){return this.cvs.splitText(this.cvtDrawOptions(t))}measureText(t){return t.fontHeight=this.cvt(t.fontHeight),this.cvs.measureText(t)}measureFontSize(t){(t=this.cvtDrawOptions(t)).minFontSize=this.cvt(t.minFontSize);const e=this.cvs.measureFontSize(t);return this.invertCvt(e)}cvt(t){return"number"==typeof t?t*this._dpm:t}invertCvt(t){return"number"==typeof t?t/this._dpm:t}cvtArray(t){if(Array.isArray(t))for(let e=0;e<t.length;e++)t[e]=this.cvt(t[e]);return t}cvtDrawOptions(t){if(this.ScaleUnit===l.Pix)return t;(t=Object.assign({},t)).x&&(t.x=this.cvt(t.x+this.OffsetX)),t.y&&(t.y=this.cvt(t.y+this.OffsetY)),t.width&&(t.width=this.cvt(t.width)),t.height&&(t.height=this.cvt(t.height)),"number"==typeof t.margin?t.margin=this.cvt(t.margin):this.cvtArray(t.margin),"number"==typeof t.padding?t.padding=this.cvt(t.padding):this.cvtArray(t.padding);const e=t;e.lineWidth&&(e.lineWidth=this.cvt(e.lineWidth)),e.radius&&(e.radius=this.cvt(e.radius));const i=t;i.cornerWidth&&(i.cornerWidth=this.cvt(i.cornerWidth)),i.cornerHeight&&(i.cornerHeight=this.cvt(i.cornerHeight));const s=t;s.x1&&(s.x1=this.cvt(s.x1+this.OffsetX)),s.y1&&(s.y1=this.cvt(s.y1+this.OffsetY)),s.x2&&(s.x2=this.cvt(s.x2+this.OffsetX)),s.y2&&(s.y2=this.cvt(s.y2+this.OffsetY)),s.dashLens&&(s.dashLens=s.dashLens.map((t=>this.cvt(t)))),s.dashLen&&(s.dashLen=s.dashLen.split(",").map((t=>this.cvt(+t))).join(","));const r=t;return r.fontHeight&&(r.fontHeight=this.cvt(r.fontHeight)),r.lineSpace&&"number"==typeof r.lineSpace&&(r.lineSpace=this.cvt(r.lineSpace)),r.charSpace&&(r.charSpace=this.cvt(r.charSpace)),t}inverseColors(){return this.cvs.inverseColors()}horizontalFlip(){return this.cvs.horizontalFlip()}getImageData(){return this.cvs.getImageData()}}function f(t,e,i,s){return new(i||(i=Promise))((function(r,n){function a(t){try{h(s.next(t))}catch(t){n(t)}}function o(t){try{h(s.throw(t))}catch(t){n(t)}}function h(t){var e;t.done?r(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(a,o)}h((s=s.apply(t,e||[])).next())}))}m.BORDER_DPM_DEFAULT=8,"function"==typeof SuppressedError&&SuppressedError;class C{constructor(t,e){this.cols=t||0,this.rows=e||0,this.data=new Uint8Array(this.rows*this.cols)}get reservedBit(){return this._reservedBit||(this._reservedBit=new Uint8Array(this.data.length)),this._reservedBit}set(t,e,i,s){const r=t*this.cols+e;this.data[r]="number"==typeof i?i:i?1:0,s&&(this.reservedBit[r]=1)}get(t,e){return this.data[t*this.cols+e]}getRowData(t){const e=(t||0)*this.cols;return this.data.slice(e,e+this.cols)}getColData(t){return this.data.filter(((e,i)=>i%this.cols===t))}xor(t,e,i){this.data[t*this.cols+e]^=i?1:0}fill(t,e,i,s){const r=(e||0)*this.cols+(i||0);let n=0;n="number"==typeof s?s>0?r+s:this.data.length+s:this.data.length,this.data.fill(t,r,n)}setArray(t,e,i){const s=(e||0)*this.cols+(i||0);this.data.set(t,s)}addRow(t,e,i){e>=this.data.length?console.error(`offset: ${e} 越界`):(i&&i<t.length&&(t=t.slice(0,i)),this.data.set(t,e))}isReserved(t,e){return this._reservedBit?this._reservedBit[t*this.cols+e]:0}}const P=Object.freeze({nullCharacter:0,maxAsciiCharacter:127,lineFeed:10,LF:10,carriageReturn:13,CR:13,lineSeparator:8232,paragraphSeparator:8233,nextLine:133,space:32,nonBreakingSpace:160,enQuad:8192,emQuad:8193,enSpace:8194,emSpace:8195,threePerEmSpace:8196,fourPerEmSpace:8197,sixPerEmSpace:8198,figureSpace:8199,punctuationSpace:8200,thinSpace:8201,hairSpace:8202,zeroWidthSpace:8203,narrowNoBreakSpace:8239,ideographicSpace:12288,mathematicalSpace:8287,ogham:5760,_:95,$:36,num_0:48,num_9:57,a:97,z:122,A:65,Z:90,ampersand:38,asterisk:42,at:64,backslash:92,backtick:96,bar:124,caret:94,closeBrace:125,closeBracket:93,closeParen:41,colon:58,comma:44,dot:46,doubleQuote:34,equals:61,exclamation:33,greaterThan:62,hash:35,lessThan:60,minus:45,openBrace:123,openBracket:91,openParen:40,percent:37,plus:43,question:63,semicolon:59,singleQuote:39,slash:47,tilde:126,backspace:8,formFeed:12,byteOrderMark:65279,tab:9,verticalTab:11});class b{static isDigit(t){return"number"==typeof t?t>=P.num_0&&t<=P.num_9:t>="0"&&t<="9"}static isDigits(t,e,i){const s=e||0,r=i?s+i:t.length;return!(r>t.length)&&t.substring(s,r).match(/^[0-9]+$/)}static isUpper(t){return"number"==typeof t?t>=P.A&&t<=P.Z:t>="A"&&t<="Z"}static isLower(t){return"number"==typeof t?t>=P.a&&t<=P.z:t>="a"&&t<="z"}static ctoi(t){const e="string"==typeof t?t.charCodeAt(0):t;return e>=P.num_0&&e<=P.num_9?e-P.num_0:e>=P.A&&e<=P.Z?e-P.A+10:e>=P.a&&e<=P.z?e-P.a+10:-1}static itoc(t){return t>=0&&t<=9?`${t}`:String.fromCharCode(t-10+P.A)}static repeatChar(t,e){const i=[];for(let s=0;s<e;s++)i.push(t);return i.join("")}static preFillChar(t,e,i){return t.length<e?Array(e-t.length+1).join(i)+t:t}}class y{static isISO_8859_1(t){if(!t)return!1;for(let e=0;e<t.length;e++)if(t.charCodeAt(e)>255)return!1;return!0}static getCharCodes(t){t=t||"";const e=[];for(let i=0;i<t.length;i++)e.push(t.charCodeAt(i));return e}static getCharCodeArrayString(t){return t.map((t=>String.fromCharCode(t))).join("")}static encodeUtf8(t){const e=[];for(let i=0;i<t.length;i++){let s=t.charCodeAt(i);if(s>=55296&&s<=56319&&t.length>i+1){const e=t.charCodeAt(i+1);e>=56320&&e<=57343&&(s=1024*(s-55296)+e-56320+65536,i+=1)}s<128?e.push(s):s<2048?(e.push(s>>6|192),e.push(63&s|128)):s<55296||s>=57344&&s<65536?(e.push(224|s>>12),e.push(128|s>>6&63),e.push(128|63&s)):s>=65536&&s<=1114111?(e.push(240|s>>18),e.push(128|s>>12&63),e.push(128|s>>6&63),e.push(128|63&s)):e.push(239,191,189)}return new Uint8Array(e)}static getBytes_Utf8(t){try{return(new TextEncoder).encode(t)}catch(e){return console.log("DzTextEncoder.encode: TextEncoder is not defined"),this.encodeUtf8(t)}}static getBytes_ISO8859_1(t){return t=unescape(encodeURIComponent(t)),Uint8Array.from(this.getCharCodes(t))}static getBytes_Unicode(t){return Uint8Array.from(this.getCharCodes(t))}static getBytes(t,e){return t=t||"",e?y.getBytes_Utf8(t):y.getBytes_Unicode(t)}static hasBase256Chars(t){const e="string"==typeof t?t.split("").map((t=>t.charCodeAt(0))):t;if((null==e?void 0:e.length)>0)for(const t of e)if(t>=128)return!0;return!1}static encodeUnicodeFromUtf8(t){let e=0,i=0,s=0;const r=[];do{if(t[i]<=127)r[s]=t[i],e=i+1,s++;else{if(t[i]>=128&&t[i]<=191)return;if(t[i]>=192&&t[i]<=193)return;if(t[i]>=194&&t[i]<=223)r[s]=((31&t[i])<<6)+(63&t[i+1]),e=i+2,s++;else if(t[i]>=224&&t[i]<=239)r[s]=((15&t[i])<<12)+((63&t[i+1])<<6)+(63&t[i+2]),e=i+3,s++;else if(t[i]>=240)return}i=e}while(i<t.length);return r}}var A;!function(t){t[t.UPC_A=20]="UPC_A",t[t.UPC_E=21]="UPC_E",t[t.EAN13=22]="EAN13",t[t.EAN8=23]="EAN8",t[t.CODE39=24]="CODE39",t[t.ITF25=25]="ITF25",t[t.CODABAR=26]="CODABAR",t[t.CODE93=27]="CODE93",t[t.CODE128=28]="CODE128",t[t.ISBN=29]="ISBN",t[t.ECODE39=30]="ECODE39",t[t.ITF14=31]="ITF14",t[t.ChinaPost=32]="ChinaPost",t[t.Matrix25=33]="Matrix25",t[t.Industrial25=34]="Industrial25",t[t.GS1_128=35]="GS1_128",t[t.EAN128=35]="EAN128",t[t.AUTO=60]="AUTO"}(A||(A={}));class I{constructor(t,e){this.data=t||"",this.text=e.text||t||"",this.options=e}encode(t,e){return{}}}const R=103,E=104,v=105,x={[R]:0,[E]:1,[v]:2},w={101:0,100:1,99:2},D=String.fromCharCode(208),S=String.fromCharCode(209),_=String.fromCharCode(210),T="[\0-_È-Ï]",O="[ -È-Ï]",B=[11011001100,11001101100,11001100110,10010011e3,10010001100,10001001100,10011001e3,10011000100,10001100100,11001001e3,11001000100,11000100100,10110011100,10011011100,10011001110,10111001100,10011101100,10011100110,11001110010,11001011100,11001001110,11011100100,11001110100,11101101110,11101001100,11100101100,11100100110,11101100100,11100110100,11100110010,11011011e3,11011000110,11000110110,10100011e3,10001011e3,10001000110,10110001e3,10001101e3,10001100010,11010001e3,11000101e3,11000100010,10110111e3,10110001110,10001101110,10111011e3,10111000110,10001110110,11101110110,11010001110,11000101110,11011101e3,11011100010,11011101110,11101011e3,11101000110,11100010110,11101101e3,11101100010,11100011010,11101111010,11001000010,11110001010,1010011e4,10100001100,1001011e4,10010000110,10000101100,10000100110,1011001e4,10110000100,1001101e4,10011000010,10000110100,10000110010,11000010010,1100101e4,11110111010,11000010100,10001111010,10100111100,10010111100,10010011110,10111100100,10011110100,10011110010,11110100100,11110010100,11110010010,11011011110,11011110110,11110110110,10101111e3,10100011110,10001011110,10111101e3,10111100010,11110101e3,11110100010,10111011110,10111101110,11101011110,11110101110,11010000100,1101001e4,11010011100,1100011101011];class M extends I{constructor(t,e){super(t.substring(1),e),this.bytes=t.split("").map((t=>t.charCodeAt(0)))}valid(){return/^[\x00-\x7F\xC8-\xD3]+$/.test(this.data)}encode(){const t=this.bytes,e=(t.shift()||0)-105,i=x[e];if(void 0===i)throw new RangeError("The encoding does not start with a start character.");this.shouldEncodeAsEan128()&&t.unshift(207);const s=M.next(t,1,i);return{items:[{text:this.text===this.data?this.text.replace(/[^\x20-\x7E]/g,""):this.text,data:M.getBar(e)+s.result+M.getBar((s.checksum+e)%103)+M.getBar(106)}],text:this.text,options:this.options}}shouldEncodeAsEan128(){return this.options.ean128}static getBar(t){return B[t]?B[t].toString():""}static correctIndex(t,e){if(0===e){const e=t.shift()||0;return e<32?e+64:e-32}return 1===e?(t.shift()||0)-32:10*((t.shift()||0)-48)+(t.shift()||0)-48}static next(t,e,i){if(!t.length)return{result:"",checksum:0};let s,r;if(t[0]>=200){r=(t.shift()||0)-105;const n=w[r]||0;void 0!==n?s=M.next(t,e+1,n):(0!==i&&1!==i||98!==r||(t[0]=0===i?t[0]>95?t[0]-96:t[0]:t[0]<32?t[0]+96:t[0]),s=M.next(t,e+1,i))}else r=M.correctIndex(t,i),s=M.next(t,e+1,i);const n=r*e;return{result:M.getBar(r)+s.result,checksum:n+s.checksum}}}const L=t=>{const e=t.match(new RegExp(`^${T}*`));return e?e[0].length:0},N=t=>{const e=t.match(new RegExp(`^${O}*`));return e?e[0].length:0},F=t=>{const e=t.match(new RegExp("^(Ï*[0-9]{2}Ï*)*"));return e?e[0]:""};function j(t,e){const i=e?T:O,s=t.match(new RegExp(`^(${i}+?)(([0-9]{2}){2,})([^0-9]|$)`));if(s)return s[1]+String.fromCharCode(204)+U(t.substring(s[1].length));const r=t.match(new RegExp(`^${i}+`)),n=r?r[0]:"";return n.length===t.length?t:n+String.fromCharCode(e?205:206)+j(t.substring(n.length),!e)}function U(t){const e=F(t),i=e.length;if(i===t.length)return t;t=t.substring(i);const s=L(t)>=N(t);return e+String.fromCharCode(s?206:205)+j(t,s)}class W extends M{constructor(t,e){if(/^[\x00-\x7F\xC8-\xD3]+$/.test(t)){let i;if(F(t).length>=2)i=_+U(t);else{const e=L(t)>N(t);i=(e?D:S)+j(t,e)}super(i.replace(/[\xCD\xCE]([^])[\xCD\xCE]/,((t,e)=>String.fromCharCode(203)+e)),e)}else super(t,e)}}const $={L:["0001101","0011001","0010011","0111101","0100011","0110001","0101111","0111011","0110111","0001011"],G:["0100111","0110011","0011011","0100001","0011101","0111001","0000101","0010001","0001001","0010111"],R:["1110010","1100110","1101100","1000010","1011100","1001110","1010000","1000100","1001000","1110100"],O:["0001101","0011001","0010011","0111101","0100011","0110001","0101111","0111011","0110111","0001011"],E:["0100111","0110011","0011011","0100001","0011101","0111001","0000101","0010001","0001001","0010111"]},k=(t,e,i)=>{let s=t.split("").map(((t,i)=>$[e[i]])).map(((e,i)=>e?e[t[i]]:""));if(i){const e=t.length-1;s=s.map(((t,s)=>s<e?t+i:t))}return s.join("")};let H=class t extends I{constructor(t,e){super(t,e),this.quietZones=0,this.guardWhitespace=!1,this.quietZones=e.quietZones||0,this.guardWhitespace=e.guardWhitespace||!1}encode(){const t=this.options.flat?this.encodeFlat():this.encodeGuarded();return{options:this.options,items:t,text:this.text}}leftText(t,e){return this.text.substr(t,e)}leftEncode(t,e){return k(t||"",e||"")}rightText(t,e){return this.text.substr(t,e)}rightEncode(t,e){return k(t||"",e||"")}encodeGuarded(){const e=[{data:t.SIDE_BIN,text:""},{data:this.leftEncode(),text:this.leftText(0)},{data:t.MIDDLE_BIN,text:""},{data:this.rightEncode(),text:this.rightText()},{data:t.SIDE_BIN,text:""}];if(this.quietZones>0){const t=Array(this.quietZones).fill("0").join("");e.unshift({data:t,text:this.guardWhitespace?"< ":""}),e.push({data:t,text:this.guardWhitespace?" >":""})}return e}encodeFlat(){return[{data:[t.SIDE_BIN,this.leftEncode(),t.MIDDLE_BIN,this.rightEncode(),t.SIDE_BIN].join(""),text:this.text}]}};H.SIDE_BIN="101",H.MIDDLE_BIN="01010";class J extends I{constructor(t,e){super(t,e)}valid(){return-1!==this.data.search(/^[0-9]{2}$/)}encode(){const t=J.EAN2_STRUCTURE[parseInt(this.data)%4],e={data:"1011"+k(this.data,t,"01"),text:this.text};return{options:this.options,items:[e],text:this.text}}}J.EAN2_STRUCTURE=["LL","LG","GL","GG"];class G extends I{static checksum(t){return t.split("").map((t=>+t)).reduce(((t,e,i)=>i%2?t+9*e:t+3*e),0)%10}constructor(t,e){super(t,e)}valid(){return-1!==this.data.search(/^[0-9]{5}$/)}encode(){const t=G.EAN5_STRUCTURE[G.checksum(this.data)];return{items:[{data:"1011"+k(this.data,t,"01"),text:this.text}],text:this.text,options:this.options}}}G.EAN5_STRUCTURE=["GGLLL","GLGLL","GLLGL","GLLLG","LGGLL","LLGGL","LLLGG","LGLGL","LGLLG","LLGLG"];class V extends H{static checksum(t){return(10-t.substring(0,7).split("").map((t=>+t)).reduce(((t,e,i)=>i%2?t+e:t+3*e),0)%10)%10}constructor(t,e){-1!==t.search(/^[0-9]{7}$/)&&(t+=V.checksum(t)),super(t,e)}valid(){return-1!==this.data.search(/^[0-9]{8}$/)&&+this.data[7]===V.checksum(this.data)}leftText(){return super.leftText(0,4)}leftEncode(){const t=this.data.substring(0,4);return super.leftEncode(t,"LLLL")}rightText(){return super.rightText(4,4)}rightEncode(){const t=this.data.substring(4,8);return super.rightEncode(t,"RRRR")}}let K=class t extends H{static checksum(t){return(10-t.substring(0,12).split("").map((t=>+t)).reduce(((t,e,i)=>i%2?t+3*e:t+e),0)%10)%10}constructor(e,i){-1!==e.search(/^[0-9]{12}$/)&&(e+=t.checksum(e)),super(e,i)}valid(){return-1!==this.data.search(/^[0-9]{13}$/)&&+this.data[12]===t.checksum(this.data)}leftText(){return super.leftText(1,6)}leftEncode(){const e=this.data.substring(1,7),i=t.EAN13_STRUCTURE[Number(this.data[0])];return super.leftEncode(e,i)}rightText(){return super.rightText(7,6)}rightEncode(){const t=this.data.substring(7,13);return super.rightEncode(t,"RRRRRR")}encodeGuarded(){const t=super.encodeGuarded();return this.quietZones>0?t[0].text=this.text[0]:t.unshift({data:"00000",text:this.text[0]}),t}};K.EAN13_STRUCTURE=["LLLLLL","LLGLGG","LLGGLG","LLGGGL","LGLLGG","LGGLLG","LGGGLL","LGLGLG","LGLGGL","LGGLGL"];class z extends I{static checksum(t){let e,i=0;for(e=1;e<11;e+=2)i+=parseInt(t[e]);for(e=0;e<11;e+=2)i+=3*parseInt(t[e]);return(10-i%10)%10}constructor(t,e){-1!==t.search(/^[0-9]{11}$/)&&(t+=z.checksum(t)),super(t,e),this.displayValue=e.displayValue}valid(){return-1!==this.data.search(/^[0-9]{12}$/)&&parseInt(this.data[11])===z.checksum(this.data)}encode(){return{items:this.options.flat?this.flatEncoding():this.guardedEncoding(),text:this.text,options:this.options}}flatEncoding(){let t="";return t+="101",t+=k(this.data.substring(0,6),"LLLLLL"),t+="01010",t+=k(this.data.substring(6,6),"RRRRRR"),t+="101",[{data:t,text:this.text}]}guardedEncoding(){const t=[],e="number"==typeof this.options.quietZones&&this.options.quietZones>2?this.options.quietZones:2,i=Array(e).fill("0").join("");return t.push({data:i,text:this.text.substring(0,1)}),t.push({data:"101"+k(this.data[0],"L")}),t.push({data:k(this.data.substring(1,6),"LLLLL"),text:this.text.substring(1,6)}),t.push({data:"01010"}),t.push({data:k(this.data.substring(6,11),"RRRRR"),text:this.text.substring(6,11)}),t.push({data:k(this.data[11],"R")+"101"}),t.push({data:i,text:this.text.substring(11,12)}),t}}const X=["XX00000XXX","XX10000XXX","XX20000XXX","XXX00000XX","XXXX00000X","XXXXX00005","XXXXX00006","XXXXX00007","XXXXX00008","XXXXX00009"],Z=[["EEEOOO","OOOEEE"],["EEOEOO","OOEOEE"],["EEOOEO","OOEEOE"],["EEOOOE","OOEEEO"],["EOEEOO","OEOOEE"],["EOOEEO","OEEOOE"],["EOOOEE","OEEEOO"],["EOEOEO","OEOEOE"],["EOEOOE","OEOEEO"],["EOOEOE","OEEOEO"]];class q extends I{static expandToUPCA(t,e){const i=parseInt(t[t.length-1]),s=X[i];let r="",n=0;for(let e=0;e<s.length;e++){const i=s[e];r+="X"===i?t[n++]:i}return r=`${e}${r}`,`${r}${z.checksum(r)}`}constructor(t,e){if(super(t,e),this.middleDigits="",this.upcA="",this.isValid=!1,-1!==t.search(/^[0-9]{6}$/))this.middleDigits=t,this.upcA=q.expandToUPCA(t,"0"),this.text=e.text||`${this.upcA[0]}${t}${this.upcA[this.upcA.length-1]}`,this.isValid=!0;else{if(-1===t.search(/^[01][0-9]{7}$/))return;if(this.middleDigits=t.substring(1,t.length-1),this.upcA=q.expandToUPCA(this.middleDigits,t[0]),this.upcA[this.upcA.length-1]!==t[t.length-1])return;this.isValid=!0}this.displayValue=e.displayValue}valid(){return this.isValid}encode(){return{items:this.options.flat?this.flatEncoding():this.guardedEncoding(),text:this.text,options:this.options}}flatEncoding(){let t="";return t+="101",t+=this.encodeMiddleDigits(),t+="010101",[{data:t,text:this.text}]}guardedEncoding(){const t=[],e="number"==typeof this.options.quietZones&&this.options.quietZones>2?this.options.quietZones:2,i=Array(e).fill("0").join("");return t.push({data:i,text:this.text[0]}),t.push({data:"101",text:""}),t.push({data:this.encodeMiddleDigits(),text:this.text.substring(1,7)}),t.push({data:"010101",text:""}),t.push({data:i,text:this.text[7]}),t}encodeMiddleDigits(){const t=this.upcA[0],e=this.upcA[this.upcA.length-1],i=Z[parseInt(e)][parseInt(t)];return k(this.middleDigits,i)}}class Y{constructor(){this.latch=!0,this.allEncodes=""}static getDigitText(t){return(t||"").replace(/\D/g,"")}static getInstance(){return this.instance||(this.instance=new Y)}static encode(t){return this.getInstance().init().expand(t)}static checkDigit(t,e){const i=e||t.length;let s=0,r=1&i?3:1;for(let e=0;e<i;e++)s+=r*parseInt(t[e]),r^=2;return String((10-s%10)%10)}init(){return this.latch=!0,this.allEncodes="",this}expand(t){let e="";for(let i=0,s=0;i<t.length;i++)s=parseInt(t[i]),e+=Array(s).fill(this.latch?"1":"0").join(""),this.latch=!this.latch;return this.allEncodes+=e,e}}const Q=["BBBAAA","BBABAA","BBAABA","BBAAAB","BABBAA","BAABBA","BAAABB","BABABA","BABAAB","BAABAB"],tt=["AAABBB","AABABB","AABBAB","AABBBA","ABAABB","ABBAAB","ABBBAA","ABABAB","ABABBA","ABBABA"],et=["AA","AB","BA","BB"],it=["BBAAA","BABAA","BAABA","BAAAB","ABBAA","AABBA","AAABB","ABABA","ABAAB","AABAB"],st=["AAAAA","ABABB","ABBAB","ABBBA","BAABB","BBAAB","BBBAA","BABAB","BABBA","BBABA"],rt=["3211","2221","2122","1411","1132","1231","1114","1312","1213","3112"],nt=["1123","1222","2212","1141","2311","1321","4111","2131","3121","2113"],at=["112211","211121","121121","221111","112121","212111","122111","111221","211211","121211"],ot=["1111212111","2111111121","1121111121","2121111111","1111211121","2111211111","1121211111","1111112121","2111112111","1121112111"],ht=["11221","21112","12112","22111","11212","21211","12211","11122","21121","12121"],ct=["1111","211"],lt=["411111","41111"],dt=["212111","21112"],ut=["1111","211"];class gt extends I{constructor(t,e){super(t=Y.getDigitText(t),e)}static gs1_check_digit(t,e){"number"==typeof e&&(e>t.length?t=t.padStart(e,"0"):e<t.length&&(t=t.substring(0,e)));let i=1&t.length?3:1,s=0;for(let e=0;e<t.length;e++)s+=i*parseInt(t[e]),i^=2;return""+(10*Math.ceil(s/10)-s)}static c25_common(t,e,i,s,r){t=Y.getDigitText(t),s&&s>0&&t.length>s&&(t=t.substring(0,s)),r.checkDigit&&(t+=gt.gs1_check_digit(t));const n=[];if(i)for(let e=0;e<t.length;e++)n.push(at[t.charCodeAt(e)-P.num_0]);else for(let e=0;e<t.length;e++)n.push(ot[t.charCodeAt(e)-P.num_0]);return{options:r,items:[{text:t,data:Y.encode([e[0],...n,e[1]].join(""))}],text:t}}static c25_inter_common(t,e){(t=Y.getDigitText(t)).length>125&&(t=t.substring(0,125)),(t.length%2==1&&!e.checkDigit||t.length%2==1&&e.checkDigit)&&(t="0"+t),e.checkDigit&&(t+=gt.gs1_check_digit(t));const i=[];for(let e=0;e<t.length;e+=2){const s=ht[parseInt(t[e])],r=ht[parseInt(t[e+1])];for(let t=0;t<5;t++)i.push(`${s[t]}${r[t]}`)}return{options:e,items:[{text:t,data:Y.encode([ct[0],...i,ct[1]].join(""))}],text:t}}encode(){return gt.c25_inter_common(this.data,this.options)}}class pt extends gt{encode(){return gt.c25_common(this.data,lt,!0,80,this.options)}}class mt extends gt{encode(){return gt.c25_common(this.data,dt,!1,45,this.options)}}class ft extends gt{encode(){return gt.c25_common(this.data,ut,!0,80,this.options)}}class Ct extends gt{encode(){let t=this.data;return t.length>13?t=t.substring(0,13):t.length<13&&(t=t.padStart(13,"0")),t+=gt.gs1_check_digit(t),this.data=this.text=t,gt.c25_inter_common(t,this.options)}}class Pt extends I{constructor(t,e){0===t.search(/^[0-9\-\$\:\.\+\/]+$/)&&(t="A"+t+"A"),super(t.toUpperCase(),e),this.text=this.options.text||this.text.replace(/[A-D]/g,"")}valid(){return-1!==this.data.search(/^[A-D][0-9\-\$\:\.\+\/]+[A-D]$/)}encode(){const t=[],e=this.getEncodings();for(let i=0;i<this.data.length;i++)t.push(e[this.data.charAt(i)]),i!==this.data.length-1&&t.push("0");return{items:[{text:this.text,data:t.join("")}],text:this.text,options:this.options}}getEncodings(){return{0:"101010011",1:"101011001",2:"101001011",3:"110010101",4:"101101001",5:"110101001",6:"100101011",7:"100101101",8:"100110101",9:"110100101","-":"101001101",$:"101100101",":":"1101011011","/":"1101101011",".":"1101101101","+":"1011011011",A:"1011001001",B:"1001001011",C:"1010010011",D:"1010011001"}}}const bt="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%abcd",yt=["1112212111","2112111121","1122111121","2122111111","1112211121","2112211111","1122211111","1112112121","2112112111","1122112111","2111121121","1121121121","2121121111","1111221121","2111221111","1121221111","1111122121","2111122111","1121122111","1111222111","2111111221","1121111221","2121111211","1111211221","2111211211","1121211211","1111112221","2111112211","1121112211","1111212211","2211111121","1221111121","2221111111","1211211121","2211211111","1221211111","1211112121","2211112111","1221112111","1212121111","1212111211","1211121211","1112121211"],At=["%U","$A","$B","$C","$D","$E","$F","$G","$H","$I","$J","$K","$L","$M","$N","$O","$P","$Q","$R","$S","$T","$U","$V","$W","$X","$Y","$Z","%A","%B","%C","%D","%E"," ","/A","/B","/C","/D","/E","/F","/G","/H","/I","/J","/K","/L","-",".","/O","0","1","2","3","4","5","6","7","8","9","/Z","%F","%G","%H","%I","%J","%V","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","%K","%L","%M","%N","%O","%W","+A","+B","+C","+D","+E","+F","+G","+H","+I","+J","+K","+L","+M","+N","+O","+P","+Q","+R","+S","+T","+U","+V","+W","+X","+Y","+Z","%P","%Q","%R","%S","%T"],It=["bU","aA","aB","aC","aD","aE","aF","aG","aH","aI","aJ","aK","aL","aM","aN","aO","aP","aQ","aR","aS","aT","aU","aV","aW","aX","aY","aZ","bA","bB","bC","bD","bE"," ","cA","cB","cC","$","%","cF","cG","cH","cI","cJ","+","cL","-",".","/","0","1","2","3","4","5","6","7","8","9","cZ","bF","bG","bH","bI","bJ","bV","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","bK","bL","bM","bN","bO","bW","dA","dB","dC","dD","dE","dF","dG","dH","dI","dJ","dK","dL","dM","dN","dO","dP","dQ","dR","dS","dT","dU","dV","dW","dX","dY","dZ","bP","bQ","bR","bS","bT"],Rt=["131112","111213","111312","111411","121113","121212","121311","111114","131211","141111","211113","211212","211311","221112","221211","231111","112113","112212","112311","122112","132111","111123","111222","111321","121122","131121","212112","212211","211122","211221","221121","222111","112122","112221","122121","123111","121131","311112","311211","321111","112131","113121","211131","121221","312111","311121","122211"];class Et extends I{constructor(t,e){const i=function(t){const e=[],i=[];let s=0,r=0;for(let n=0,a="",o="";n<t.length;n++)if(a=t[n],r=t.charCodeAt(n),o=It[r],r>127)u.warn(`---- [encode with code93] invalid char: '${a}'[${r}]`);else{if(s+o.length>107){u.warn(`---- content too long, discarded content[${n} --\x3e ${t.length}]: "${t.substring(n)}"`);break}e.push(o),i.push(a>" "&&127!==r?a:" "),s+=o.length}return{data:e.join(""),text:i.join("")}}(t);e.text=i.text,super(i.data,e)}encode(){const t=[],e=[];for(let e=0;e<this.data.length;e++)t.push(bt.indexOf(this.data[e]));let i=0,s=1;for(let e=this.data.length-1;e>=0;e--)i+=t[e]*s,s++,21==s&&(s=1);i%=47,t.push(i);let r=0;s=1;for(let e=this.data.length;e>=0;e--)r+=t[e]*s,s++,16==s&&(s=1);r%=47,t.push(r),e.push("111141");for(let i=0;i<t.length;i++)e.push(Rt[t[i]]);return e.push("1111411"),{items:[{data:Y.encode(e.join("")),text:this.options.mod43?`${this.text}${bt[i]}${bt[r]}`:this.text}],text:this.text,options:this.options}}valid(){return-1!==this.data.search(/^[0-9A-Z\-\.\ \$\/\+\%]+$/)}}function vt(t){const e=t.charCodeAt(0);return At[e]}class xt extends I{constructor(t,e){if(t=t.toUpperCase(),e.mod43){const i=e.text||t,s=function(t){const e=t.length%43;return bt[e]}(t);t+=s,e.text=i+s}super(t,e)}encode(){const t=[],e=[],i=this.data;for(let e=0;e<i.length;e++){const s=bt.indexOf(i[e]);s>=0?t.push(s):u.warn(`---- 检测到无效字符[encode with code39]: '${i[e]}'`)}e.push("1211212111");for(let s=0;s<i.length;s++)e.push(yt[t[s]]);e.push("121121211");const s={data:Y.encode(e.join("")),text:this.options.showStartEndChar?`*${this.text}*`:this.text};return{options:this.options,items:[s],text:this.text}}}class wt extends xt{constructor(t,e){const i=function(t){const e=[],i=[];let s=0,r=0;for(let n=0,a="",o="";n<t.length;n++)if(a=t[n],r=t.charCodeAt(n),o=vt(a),r>127)u.warn(`---- [ECode39] invalidate char: '${a}'[${r}]`);else{if(s+o.length>85){u.warn(`---- discarded content[${n} --\x3e ${t.length}]: "${t.slice(n)}"`);break}i.push(a),e.push(o),s+=o.length}return{data:e.join(""),text:i.join("")}}(t);e.text=i.text,super(i.data,e)}valid(){return-1!==this.data.search(/^[0-9A-Z\-\.\ \$\/\+\%]+$/)}}class Dt extends I{static convertUPCE2UPCA(t){(t=Y.getDigitText(t)).length>7?t=t.substring(0,7):t.length<7&&(t=t.padStart(7,"0"));const e=t.substring(1),i=e[5],s=Array(11).fill("0");switch("1"==t[0]&&(s[0]="1"),s[1]=e[0],s[2]=e[1],i){case"0":case"1":case"2":s[3]=i,s[8]=e[2],s[9]=e[3],s[10]=e[4];break;case"3":s[3]=e[2],s[9]=e[3],s[10]=e[4],"0"!=e[2]&&"1"!=e[2]&&"2"!=e[2]||u.warn("271: Invalid UPC-E data, X3 shall not be equal to 0, 1 or ");break;case"4":s[3]=e[2],s[4]=e[3],s[10]=e[4],"0"==e[3]&&u.warn("272: Invalid UPC-E data, X4 shall not be equal to 0");break;case"5":case"6":case"7":case"8":case"9":s[3]=e[2],s[4]=e[3],s[5]=e[4],s[10]=i,"0"==e[4]&&u.warn("273: Invalid UPC-E data X5 shall not be equal to 0")}return s.join("")}static getUPCECheckCode(t){const e=Dt.convertUPCE2UPCA(t);return Y.checkDigit(e,11)}constructor(t,e){(t=Y.getDigitText(t)).length>7?t=t.substring(0,7):t.length<7&&(t=t.padStart(7,"0")),super(t,e)}encode(){let t=this.text;const e=Dt.convertUPCE2UPCA(this.text),i=Y.checkDigit(e,11);t+=i;let s="";s="1"==e[0]?tt[b.ctoi(i)]:Q[b.ctoi(i)];const r=[];for(let e=0;e<length;e++)switch(s[e]){case"A":r.push(rt[t.charCodeAt(e)-P.num_0]);break;case"B":r.push(nt[t.charCodeAt(e)-P.num_0])}const n=[],a=new Y;return n.push({text:"",data:a.expand("111")}),n.push({text:"",data:a.expand(r.join(""))}),n.push({text:"",data:a.expand("111111")}),{options:this.options,items:n,text:t}}}class St extends I{constructor(t,e){super(t=Y.getDigitText(t),e)}encode(){let t=this.text,e="";if(t.length<2&&(t=t.padStart(2,"0")),t.length<=2){const i=10*b.ctoi(t[0])+b.ctoi(t[1]);e=et[i%4]}else{t.length<5&&(t=t.padStart(5,"0"));const i=[];for(let e=0;e<5;e++)i[e]=b.ctoi(t[e]);let s=3*(i[0]+i[2]+i[4]);s+=9*(i[1]+i[3]),e=it[s%10]}const i=[];i.push("112");for(let s=0;s<t.length;s++){switch(e[s]){case"A":i.push(rt[t.charCodeAt(s)-P.num_0]);break;case"B":i.push(nt[t.charCodeAt(s)-P.num_0])}s!=length-1&&i.push("11")}const s=[{text:t,data:Y.encode(i.join(""))}];return{options:this.options,items:s,text:t}}}class _t extends St{constructor(t,e){super(t=Y.getDigitText(t),e)}encode(){let t=this.text;t.length>13?t=t.substring(0,13):t.length<12&&(t=t.padStart(12,"0"));const e=Y.checkDigit(t,12);12==t.length?t+=e:e!==t[12]&&(t=t.substring(0,12)+e);const i=st[t.charCodeAt(0)-P.num_0],s=[],r=[];for(let e=1;e<t.length;e++)e>1&&e<7&&"B"==i[e-2]?e<7?s.push(nt[t.charCodeAt(e)-P.num_0]):r.push(nt[t.charCodeAt(e)-P.num_0]):e<7?s.push(rt[t.charCodeAt(e)-P.num_0]):r.push(rt[t.charCodeAt(e)-P.num_0]);const n=this.options.quietZones||0,a=[],o=new Y,h=Array(n>2?n:2).fill("0").join("");return a.push({text:t[0],data:h}),a.push({text:"",data:o.expand("111")}),a.push({text:t.substring(1,7),data:o.expand(s.join(""))}),a.push({text:"",data:o.expand("11111")}),a.push({text:t.substring(7),data:o.expand(r.join(""))}),a.push({text:"",data:o.expand("111")}),a.push({text:this.options.guardWhitespace?" >":"",data:h}),{items:a,text:t,options:this.options}}}class Tt extends _t{static getCheckCode(t,e){let i=0,s=1;const r=e||t.length;for(let e=0;e<r;e++)i+=b.ctoi(t[e])*s,s++;const n=i%11;let a=b.itoc(n);return 10==n&&(a="X"),a}static filterText(t){const e=[];t=t.toUpperCase();for(let i=0;i<t.length&&!(e.length>13);i++)if(b.isDigit(t.charCodeAt(i)))e.push(t[i]);else if("X"===t[i]&&9===e.length&&i===t.length-1){e.push(t[i]);break}return e.join("")}constructor(t,e){if(super(t,e),(t=Tt.filterText(t)).length>13&&(t=t.substring(0,13)),13==t.length){const e=t.substring(0,3);"978"!==e&&"979"!==e&&(t="978"+t.substring(3));const i=Y.checkDigit(t,12);t[12]!=i&&(t=t.substring(0,12)+i)}else t.length>10&&(t=t.substring(0,10)),t.length<9&&(t=t.padStart(9,"0")),t=`978${t.substring(0,9)}`;this.data=this.text=t}}class Ot{static registerEncoder(t,e){this.registedEncoderMap.set(t,e)}static getEncoder(t){const e=t.type||A.AUTO,i=t.text||"",s=this.registedEncoderMap.get(e);if(s&&"function"==typeof s.encode)return s;switch(e){case A.UPC_A:return new z(i,t);case A.UPC_E:return new q(i,t);case A.EAN13:return new K(i,t);case A.EAN8:return new V(i,t);case A.CODE39:return new xt(i,t);case A.ITF25:return new gt(i,t);case A.CODABAR:return new Pt(i,t);case A.CODE93:return new Et(i,t);case A.ISBN:return new Tt(i,t);case A.ECODE39:return new wt(i,t);case A.ITF14:return new Ct(i,t);case A.ChinaPost:return new ft(i,t);case A.Matrix25:return new pt(i,t);case A.Industrial25:return new mt(i,t);case A.CODE128:default:return new W(i,t)}}static encode(t){"boolean"!=typeof t.displayValue&&(t.displayValue=!0);const e=this.getEncoder(t),i=e.encode(t.text||"",t);return i.data||(i.data=i.items.map((t=>t.data)).join("")),i.options||(i.options=e.options),i}}Ot.registedEncoderMap=new Map;class Bt{static registerBarcodeCreator(t,e){Ot.registerEncoder(t,e)}static IsProductType(t){switch(t){case A.EAN13:case A.EAN8:case A.UPC_A:case A.UPC_E:case A.ISBN:return!0;default:return!1}}static create1DBarcode(t){const e=t;let i=A.AUTO;"number"==typeof t.barcodeType?i=t.barcodeType:"number"==typeof e.type&&(i=e.type);let s="number"==typeof t.text?`${t.text}`:(t.text||"").trim();if(s||(s=void 0===t.content||null===t.content?"":String(t.content)),s){s=this.normalize(s,i);try{let e=0;switch(i){case A.EAN13:case A.UPC_A:case A.UPC_E:case A.ISBN:e=Bt.DockHorMargin}return Ot.encode({text:s,type:i,quietZones:e,showStartEndChar:t.showStartEnd})}catch(t){return void u.warn(t)}}}static normalize(t,e){if(e===A.CODE39||e===A.CODE93)t=this.normalizeLength(t,107,(t=>t>=0&&t<128),"?");else{if(e===A.CODABAR){const e="0123456789-$:/.+ABCD";let i,s;if(t.length>=2){const e="ABCD",r="TN*E";i=t.charAt(0).toUpperCase(),s=t.charAt(t.length-1).toUpperCase();const n=e.indexOf(i)>=0&&e.indexOf(s)>=0,a=r.indexOf(i)>=0&&r.indexOf(s)>=0;n||a?t=t.substring(1,t.length-1):i=s="A"}else i=s="A";return i+(t=this.normalizeLength(t,0,(t=>e.indexOf(String.fromCharCode(t))>=0),"0"))+s}if(e===A.EAN13)t=this.normalizeDigitLength(t,12),t+=this.getEAN13CheckCode(t);else if(e===A.EAN8)t=this.normalizeDigitLength(t,7),t+=this.getEAN8CheckCode(t);else if(e===A.UPC_A)t=this.normalizeDigitLength(t,11),t+=Y.checkDigit(t);else if(e===A.UPC_E){const e=(t=this.normalizeDigitLength(t,7)).charAt(0);"0"!==e&&"1"!==e&&(t="0"+t.substring(1)),t+=Dt.getUPCECheckCode(t)}else if(e===A.ITF14)t=this.normalizeDigitLength(t,13),t+=this.getITD14CheckCode(t);else{if(e===A.ITF25)return(t=this.normalizeLength(t,80,(t=>b.isDigit(t)),"0")).length%2!=0?"0"+t:t;if(e===A.GS1_128)if("("===t[0]){if(t.indexOf(")")<0){const e=t.indexOf("]");e>0&&(t=t.substring(0,e)+")"+t.substring(e+1))}}else if("["===t[0]){if(t.indexOf("]")<0){const e=t.indexOf(")");e>0&&(t=t.substring(0,e)+"]"+t.substring(e+1))}}else t.length<2&&(t="0"+t),t=t.length<=20?"(10)"+t:t.length<=30?"(90)"+t:"(91)"+t;else t=this.normalizeLength(t,80,(t=>t>=32&&t<=126),"?")}}return t}static normalizeDigitLength(t,e){return(t=this.normalizeLength(t,e,(t=>b.isDigit(t)),"0")).length<e&&(t=b.preFillChar(t,e,"0")),t}static normalizeLength(t,e,i,s){if(e>0&&t.length>e&&(t=t.substring(0,e)),i&&s){const e=[];for(let r=0;r<t.length;r++)i(t.charCodeAt(r))?e.push(t.charAt(r)):e.push(s);t=e.join("")}return t}static getEAN13CheckCode(t){let e=0,i=0;for(i=0;i<t.length;++i){const s=t.charCodeAt(i)-P.num_0;if(s<0||s>9)throw"检测到非发字符";e+=(1&i?3:1)*s}return e=10-e%10,10==e&&(e=0),String(e)}static getEAN8CheckCode(t){return Y.checkDigit(t)}static getITD14CheckCode(t){return Y.checkDigit(t)}}var Mt;Bt.DockHorMargin=7,function(t){t[t.Low=0]="Low",t[t.Middle=1]="Middle",t[t.Quality=2]="Quality",t[t.High=3]="High"}(Mt||(Mt={}));const Lt=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];class Nt{static getSymbolSize(t){if(!t)throw new Error('"version" cannot be null or undefined');if(t<1||t>40)throw new Error('"version" should be in range from 1 to 40');return 4*t+17}static getSymbolTotalCodewords(t){return Lt[t]}static getBCHDigit(t){let e=0;for(;0!==t;)e++,t>>>=1;return e}static calcWaterMarkSeed(t){if("number"==typeof t)return t;if("string"==typeof t){if(!t)return 1024;let e=4660;const i=y.getBytes_Utf8(t);for(let t=0;t<i.length;++t)e+=e>>>5,e+=(255&i[t])*(2&t?5:3),e+=1&t?13:11;return 1025+(1048575&e)}return 0}static hasWaterMarkSeed(t,e,i){if(!e||e.length<3)return!1;let s,r;if((255&e[0])>>>4==5)if(9==(15&e[0])){if(e.length<4)return!1;if((255&e[1])>>>4!=3)return!1;if(13!=(15&e[1]))return!1;s=(255&e[2])>>>6&3,r=(63&e[2])<<4|(255&e[3])>>>4}else{if(3!=(15&e[0]))return!1;if((255&e[1])>>>4!=13)return!1;s=(255&e[1])>>>2&3,r=(3&e[1])<<8|255&e[2]}else{if((255&e[0])>>>4!=3)return!1;if(13!=(15&e[0]))return!1;s=(255&e[1])>>>6&3,r=(63&e[1])<<4|(255&e[2])>>>4}return 1022==r||r==this.calcDtCheckSum(s,i,t)}static calcDtCheckSum(t,e,i){"string"==typeof e&&(e=this.calcWaterMarkSeed(e));let s=this.sDtWaterMarkCheckSums[3&t];if(s=s+e&1048575,i&&i.length>0){const t=y.getBytes_Utf8(i);for(let e=0;e<t.length;++e)s+=s>>>5,s+=(255&t[e])*(2&e?5:3),s+=1&e?13:11}return s%1019+3}}Nt.sDtWaterMarkCheckSums=[197,257,571,991];class Ft{static getRowColCoords(t){if(1===t)return[];const e=Math.floor(t/7)+2,i=Nt.getSymbolSize(t),s=145===i?26:2*Math.ceil((i-13)/(2*e-2)),r=[i-7];for(let t=1;t<e-1;t++)r[t]=r[t-1]-s;return r.push(6),r.reverse()}static getPositions(t){const e=[],i=Ft.getRowColCoords(t),s=i.length;for(let t=0;t<s;t++)for(let r=0;r<s;r++)0===t&&0===r||0===t&&r===s-1||t===s-1&&0===r||e.push([i[t],i[r]]);return e}}class jt{constructor(){this.mBuffer=[],this.mLength=0}get buffer(){return this.mBuffer}get length(){return this.mLength}get(t){const e=Math.floor(t/8);return 1==(this.buffer[e]>>>7-t%8&1)}put(t,e){for(let i=0;i<e;i++)this.putBit(1==(t>>>e-i-1&1))}getLengthInBits(){return this.length}putBit(t){const e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.mLength++}}const Ut=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],Wt=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];class $t{static getBlocksCount(t,e){switch(e){case Mt.Low:return Ut[4*(t-1)+0];case Mt.Middle:return Ut[4*(t-1)+1];case Mt.Quality:return Ut[4*(t-1)+2];case Mt.High:return Ut[4*(t-1)+3];default:return}}static getTotalCodewordsCount(t,e){switch(e){case Mt.Low:return Wt[4*(t-1)+0];case Mt.Middle:return Wt[4*(t-1)+1];case Mt.Quality:return Wt[4*(t-1)+2];case Mt.High:return Wt[4*(t-1)+3];default:return}}}const kt=Nt.getBCHDigit(1335);class Ht{static getEccBit(t){switch(t){case Mt.Low:return 1;default:case Mt.Middle:return 0;case Mt.Quality:return 3;case Mt.High:return 2}}static getEncodedBits(t,e){const i=Ht.getEccBit(t)<<3|e;let s=i<<10;for(;Nt.getBCHDigit(s)-kt>=0;)s^=1335<<Nt.getBCHDigit(s)-kt;return 21522^(i<<10|s)}}class Jt{static isValid(t){return t&&!isNaN(t)&&t>=0&&t<=7}static getPenaltyN1(t){const e=t.cols;let i=0,s=0,r=0,n=null,a=null;for(let o=0;o<e;o++){s=r=0,n=a=null;for(let h=0;h<e;h++){let e=t.get(o,h);e===n?s++:(s>=5&&(i+=s-5+3),n=e,s=1),e=t.get(h,o),e===a?r++:(r>=5&&(i+=r-5+3),a=e,r=1)}s>=5&&(i+=s-5+3),r>=5&&(i+=r-5+3)}return i}static getPenaltyN2(t){const e=t.cols;let i=0;for(let s=0;s<e-1;s++)for(let r=0;r<e-1;r++){const e=t.get(s,r)+t.get(s,r+1)+t.get(s+1,r)+t.get(s+1,r+1);4!==e&&0!==e||i++}return 3*i}static getPenaltyN3(t){const e=t.cols;let i=0,s=0,r=0;for(let n=0;n<e;n++){s=r=0;for(let a=0;a<e;a++)s=s<<1&2047|t.get(n,a),a>=10&&(1488===s||93===s)&&i++,r=r<<1&2047|t.get(a,n),a>=10&&(1488===r||93===r)&&i++}return 40*i}static getPenaltyN4(t){let e=0;const i=t.data.length;for(let s=0;s<i;s++)e+=t.data[s];return 10*Math.abs(Math.ceil(100*e/i/5)-10)}static getMaskAt(t,e,i){switch(t){case this.Patterns.PATTERN000:return(e+i)%2==0;case this.Patterns.PATTERN001:return e%2==0;case this.Patterns.PATTERN010:return i%3==0;case this.Patterns.PATTERN011:return(e+i)%3==0;case this.Patterns.PATTERN100:return(Math.floor(e/2)+Math.floor(i/3))%2==0;case this.Patterns.PATTERN101:return e*i%2+e*i%3==0;case this.Patterns.PATTERN110:return(e*i%2+e*i%3)%2==0;case this.Patterns.PATTERN111:return(e*i%3+(e+i)%2)%2==0;default:throw new Error("bad maskPattern:"+t)}}static applyMask(t,e){const i=e.cols;for(let s=0;s<i;s++)for(let r=0;r<i;r++)e.isReserved(r,s)||e.xor(r,s,this.getMaskAt(t,r,s))}static getBestMask(t,e){const i=Object.keys(this.Patterns).length;let s=0,r=1/0;for(let n=0;n<i;n++){e(n),this.applyMask(n,t);const i=this.getPenaltyN1(t)+this.getPenaltyN2(t)+this.getPenaltyN3(t)+this.getPenaltyN4(t);this.applyMask(n,t),i<r&&(r=i,s=n)}return s}}Jt.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const Gt="[0-9]+";let Vt="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";Vt=Vt.replace(/u/g,"\\u");const Kt="(?:(?![A-Z0-9 $%*+\\-./:]|"+Vt+")(?:.|[\r\n]))+",zt=new RegExp("^"+Vt+"$"),Xt=new RegExp("^"+Gt+"$"),Zt=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");class qt{static testKanji(t){return zt.test(t)}static testNumeric(t){return Xt.test(t)}static testAlphanumeric(t){return Zt.test(t)}}qt.KANJI=new RegExp(Vt,"g"),qt.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),qt.BYTE=new RegExp(Kt,"g"),qt.NUMERIC=new RegExp(Gt,"g"),qt.ALPHANUMERIC=new RegExp("[A-Z $%*+\\-./:]+","g");class Yt{static isValid(t){return!isNaN(t)&&t>=1&&t<=40}}class Qt{static getCharCountIndicator(t,e){if(!t.ccBits)throw new Error("Invalid mode: "+t);if(!Yt.isValid(e))throw new Error("Invalid version: "+e);return e>=1&&e<10?t.ccBits[0]:e<27?t.ccBits[1]:t.ccBits[2]}static getBestModeForData(t){return qt.testNumeric(t)?this.NUMERIC:qt.testAlphanumeric(t)?this.ALPHANUMERIC:qt.testKanji(t)?this.KANJI:this.BYTE}static toString(t){if(t&&t.id)return t.id;throw new Error("Invalid mode")}static isValid(t){return"string"!=typeof t&&t&&t.bit&&t.ccBits}static fromString(t){if("string"!=typeof t)throw new Error("Param is not a string");switch(t.toLowerCase()){case"numeric":return this.NUMERIC;case"alphanumeric":return this.ALPHANUMERIC;case"kanji":return this.KANJI;case"byte":return this.BYTE;default:throw new Error("Unknown mode: "+t)}}static from(t,e){if(this.isValid(t))return t;try{return this.fromString(t)}catch(t){return e}}}Qt.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},Qt.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},Qt.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},Qt.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},Qt.MIXED={id:"",bit:-1,ccBits:[]},Qt.STRUCTURED={id:"Structured",bit:3,ccBits:[0,0,0]};const te=new Uint8Array(512),ee=new Uint8Array(256);!function(){let t=1;for(let e=0;e<255;e++)te[e]=t,ee[t]=e,t<<=1,256&t&&(t^=285);for(let t=255;t<512;t++)te[t]=te[t-255]}();class ie{static log(t){if(t<1)throw new Error("log("+t+")");return ee[t]}static exp(t){return te[t]}static mul(t,e){return 0===t||0===e?0:te[ee[t]+ee[e]]}}class se{static mul(t,e){const i=new Uint8Array(t.length+e.length-1);for(let s=0;s<t.length;s++)for(let r=0;r<e.length;r++)i[s+r]^=ie.mul(t[s],e[r]);return i}static mod(t,e){let i=new Uint8Array(t);for(;i.length-e.length>=0;){const t=i[0];for(let s=0;s<e.length;s++)i[s]^=ie.mul(e[s],t);let s=0;for(;s<i.length&&0===i[s];)s++;i=i.slice(s)}return i}static generateECPolynomial(t){let e=new Uint8Array([1]);for(let i=0;i<t;i++)e=this.mul(e,new Uint8Array([1,ie.exp(i)]));return e}}class re{constructor(t){this.degree=t,this.degree&&this.initialize(this.degree)}initialize(t){this.degree=t,this.genPoly=se.generateECPolynomial(this.degree)}encode(t){if(!this.genPoly)throw new Error("Encoder not initialized");const e=new Uint8Array(t.length+this.degree);e.set(t);const i=se.mod(e,this.genPoly),s=this.degree-i.length;if(s>0){const t=new Uint8Array(this.degree);return t.set(i,s),t}return i}}const ne="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:".split("");class ae{get mode(){return this.mMode}get data(){return this.mData}constructor(t,e){this.mMode=t,this.mData=e||""}getLength(){return this.mData.length}getBitsLength(){return 0}write(t){}}class oe extends ae{constructor(t){super(Qt.ALPHANUMERIC,t)}static getBitsLength(t){return 11*Math.floor(t/2)+t%2*6}getBitsLength(){return oe.getBitsLength(this.mData.length)}write(t){let e;for(e=0;e+2<=this.mData.length;e+=2){let i=45*ne.indexOf(this.mData[e]);i+=ne.indexOf(this.mData[e+1]),t.put(i,11)}this.mData.length%2&&t.put(ne.indexOf(this.mData[e]),6)}}class he extends ae{constructor(t){super(Qt.NUMERIC,t)}static getBitsLength(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)}getBitsLength(){return he.getBitsLength(this.getLength())}write(t){let e,i,s;for(e=0;e+3<=this.mData.length;e+=3)i=this.mData.substr(e,3),s=parseInt(i,10),t.put(s,10);const r=this.mData.length-e;r>0&&(i=this.mData.substr(e),s=parseInt(i,10),t.put(s,3*r+1))}}class ce extends ae{constructor(t){super(Qt.BYTE,t),this.bytes=y.getBytes_Utf8(t)}static getBitsLength(t){return 8*t}getLength(){return this.bytes.length}getBitsLength(){return 8*this.bytes.length}write(t){for(let e=0,i=this.bytes.length;e<i;e++)t.put(this.bytes[e],8)}}class le extends ae{constructor(t){super(Qt.KANJI,t)}static getBitsLength(t){return 13*t}getBitsLength(){return le.getBitsLength(this.getLength())}write(t){let e;for(e=0;e<this.mData.length;e++){let i=Nt.toSJISFunction?Nt.toSJISFunction(this.mData[e]):this.mData.charCodeAt(e);if(i>=33088&&i<=40956)i-=33088;else{if(!(i>=57408&&i<=60351))throw new Error("Invalid SJIS character: "+this.mData[e]+"\nMake sure your charset is UTF-8");i-=49472}i=192*(i>>>8&255)+(255&i),t.put(i,13)}}}const de={queue:[],sorter:null,make:function(t){const e=de,i={};t=t||{};for(const t in e)e.hasOwnProperty(t)&&(i[t]=e[t]);return i.queue=[],i.sorter=t.sorter||e.default_sorter,i},default_sorter:(t,e)=>t.cost-e.cost,push(t,e){this.queue.push({value:t,cost:e}),this.queue.sort(this.sorter)},pop(){return this.queue.shift()},empty(){return 0===this.queue.length}},ue={single_source_shortest_paths:function(t,e,i){const s={},r={};r[e]=0;const n=ue.PriorityQueue.make();let a,o,h,c,l,d,u,g,p;for(n.push(e,0);!n.empty();)for(h in a=n.pop(),o=null==a?void 0:a.value,c=null==a?void 0:a.cost,l=t[o]||{},l)l.hasOwnProperty(h)&&(d=l[h],u=c+d,g=r[h],p=void 0===r[h],(p||g>u)&&(r[h]=u,n.push(h,u),s[h]=o));if(void 0!==i&&void 0===r[i]){const t=["Could not find a path from ",e," to ",i,"."].join("");throw new Error(t)}return s},extract_shortest_path_from_predecessor_list:function(t,e){const i=[];let s=e;for(;s;)i.push(s),t[s],s=t[s];return i.reverse(),i},find_path:function(t,e,i){const s=ue.single_source_shortest_paths(t,e,i);return ue.extract_shortest_path_from_predecessor_list(s,i)},PriorityQueue:de};class ge{static getStringByteLength(t){return unescape(encodeURIComponent(t)).length}static getSegments(t,e,i){const s=[];let r;for(;r=t.exec(i);)s.push({data:r[0],index:r.index,mode:e,length:r[0].length});return s}static getSegmentsFromString(t){const e=this.getSegments(qt.NUMERIC,Qt.NUMERIC,t),i=this.getSegments(qt.ALPHANUMERIC,Qt.ALPHANUMERIC,t);let s,r;return"function"==typeof Nt.toSJISFunction?(s=this.getSegments(qt.BYTE,Qt.BYTE,t),r=this.getSegments(qt.KANJI,Qt.KANJI,t)):(s=this.getSegments(qt.BYTE_KANJI,Qt.BYTE,t),r=[]),e.concat(i,s,r).sort((function(t,e){return t.index-e.index})).map((function(t){return{data:t.data,mode:t.mode,length:t.length,index:0}}))}static getSegmentBitsLength(t,e){switch(e){case Qt.NUMERIC:return he.getBitsLength(t);case Qt.ALPHANUMERIC:return oe.getBitsLength(t);case Qt.KANJI:return le.getBitsLength(t);case Qt.BYTE:default:return ce.getBitsLength(t)}}static mergeSegments(t){return t.reduce((function(t,e){const i=t.length-1>=0?t[t.length-1]:null;return i&&i.mode===e.mode?(t[t.length-1].data+=e.data,t):(t.push(e),t)}),[])}static buildNodes(t){const e=[];for(let i=0;i<t.length;i++){const s=t[i];switch(s.mode){case Qt.NUMERIC:e.push([s,{data:s.data,mode:Qt.ALPHANUMERIC,length:s.length},{data:s.data,mode:Qt.BYTE,length:s.length}]);break;case Qt.ALPHANUMERIC:e.push([s,{data:s.data,mode:Qt.BYTE,length:s.length}]);break;case Qt.KANJI:e.push([s,{data:s.data,mode:Qt.BYTE,length:this.getStringByteLength(s.data)}]);break;case Qt.BYTE:e.push([{data:s.data,mode:Qt.BYTE,length:this.getStringByteLength(s.data)}])}}return e}static buildGraph(t,e){const i={},s={start:{}};let r=["start"];for(let n=0;n<t.length;n++){const a=t[n],o=[];for(let t=0;t<a.length;t++){const h=a[t],c=""+n+t;o.push(c),i[c]={node:h,lastCount:0},s[c]={};for(let t=0;t<r.length;t++){const n=r[t];i[n]&&i[n].node.mode===h.mode?(s[n][c]=this.getSegmentBitsLength(i[n].lastCount+h.length,h.mode)-this.getSegmentBitsLength(i[n].lastCount,h.mode),i[n].lastCount+=h.length):(i[n]&&(i[n].lastCount=h.length),s[n][c]=this.getSegmentBitsLength(h.length,h.mode)+4+Qt.getCharCountIndicator(h.mode,e))}}r=o}for(let t=0;t<r.length;t++)s[r[t]].end=0;return{map:s,table:i}}static buildSingleSegment(t,e){let i;const s=Qt.getBestModeForData(t);if(i=Qt.from(e||"",s),!i||i!==Qt.BYTE&&(null==i?void 0:i.bit)<s.bit)throw new Error('"'+t+'" cannot be encoded with mode '+Qt.toString(i)+".\n Suggested mode is: "+Qt.toString(s));switch(i===Qt.KANJI&&"function"!=typeof Nt.toSJISFunction&&(i=Qt.BYTE),i){case Qt.NUMERIC:return new he(t);case Qt.ALPHANUMERIC:return new oe(t);case Qt.KANJI:return new le(t);case Qt.BYTE:return new ce(t)}}static fromArray(t){return t.reduce(((t,e)=>{if("string"==typeof e){const i=ge.buildSingleSegment(e);i&&t.push(i)}else if(e.data){const i=ge.buildSingleSegment(e.data,e.mode);i&&t.push(i)}return t}),[])}static fromString(t,e){const i=this.getSegmentsFromString(t),s=this.buildNodes(i),r=this.buildGraph(s,e),n=ue.find_path(r.map,"start","end"),a=[];for(let t=1;t<n.length-1;t++)a.push(r.table[n[t]].node);return this.fromArray(this.mergeSegments(a))}static rawSplit(t){return this.fromArray(this.getSegmentsFromString(t))}}const pe=Nt.getBCHDigit(7973);class me{static getBestVersionForDataLength(t,e,i){for(let s=1;s<=40;s++)if(e<=this.getCapacity(s,i,t))return s}static getReservedBitsCount(t,e){return Qt.getCharCountIndicator(t,e)+4}static getTotalBitsFromDataArray(t,e){let i=0;return t.forEach((function(t){const s=me.getReservedBitsCount(t.mode,e);i+=s+t.getBitsLength()})),i}static getBestVersionForMixedData(t,e,i){const s=t&&t.length>0?t.length:0;for(let t=1;t<=40;t++)if(this.getTotalBitsFromDataArray(e,t)+s<=this.getCapacity(t,i,Qt.MIXED))return t}static isValid(t){return!isNaN(t)&&t>=1&&t<=40}static getCapacity(t,e,i){if(!me.isValid(t))throw new Error("Invalid QR Code version");void 0===i&&(i=Qt.BYTE);const s=8*(Nt.getSymbolTotalCodewords(t)-($t.getTotalCodewordsCount(t,e)||0));if(i===Qt.MIXED)return s;const r=s-this.getReservedBitsCount(i,t);switch(i){case Qt.NUMERIC:return Math.floor(r/10*3);case Qt.ALPHANUMERIC:return Math.floor(r/11*2);case Qt.KANJI:return Math.floor(r/13);case Qt.BYTE:default:return Math.floor(r/8)}}static getBestVersionForData(t,e,i){let s;if(Array.isArray(e)){if(e.length>1)return this.getBestVersionForMixedData(t,e,i);if(0===e.length)return 1;s=e[0]}else s=e;const r=t?Math.ceil(t.length/8):0;return this.getBestVersionForDataLength(s.mode,s.getLength()+r,i)}static getEncodedBits(t){if(!me.isValid(t)||t<7)throw new Error("Invalid QR Code version");let e=t<<12;for(;Nt.getBCHDigit(e)-pe>=0;)e^=7973<<Nt.getBCHDigit(e)-pe;return t<<12|e}}class fe{static getPositions(t){const e=Nt.getSymbolSize(t);return[[0,0],[e-7,0],[0,e-7]]}}class Ce{static setupFinderPattern(t,e){const i=t.cols,s=fe.getPositions(e);for(let e=0;e<s.length;e++){const r=s[e][0],n=s[e][1];for(let e=-1;e<=7;e++)if(!(r+e<=-1||i<=r+e))for(let s=-1;s<=7;s++)n+s<=-1||i<=n+s||(e>=0&&e<=6&&(0===s||6===s)||s>=0&&s<=6&&(0===e||6===e)||e>=2&&e<=4&&s>=2&&s<=4?t.set(r+e,n+s,!0,!0):t.set(r+e,n+s,!1,!0))}}static setupTimingPattern(t){const e=t.cols;for(let i=8;i<e-8;i++){const e=i%2==0;t.set(i,6,e,!0),t.set(6,i,e,!0)}}static setupAlignmentPattern(t,e){const i=Ft.getPositions(e);for(let e=0;e<i.length;e++){const s=i[e][0],r=i[e][1];for(let e=-2;e<=2;e++)for(let i=-2;i<=2;i++)-2===e||2===e||-2===i||2===i||0===e&&0===i?t.set(s+e,r+i,!0,!0):t.set(s+e,r+i,!1,!0)}}static setupVersionInfo(t,e){const i=t.cols,s=me.getEncodedBits(e);let r,n,a;for(let e=0;e<18;e++)r=Math.floor(e/3),n=e%3+i-8-3,a=1==(s>>e&1),t.set(r,n,a,!0),t.set(n,r,a,!0)}static setupFormatInfo(t,e,i){const s=t.cols,r=Ht.getEncodedBits(e,i);let n=!1;for(let e=0;e<15;e++)n=1==(r>>e&1),e<6?t.set(e,8,n,!0):e<8?t.set(e+1,8,n,!0):t.set(s-15+e,8,n,!0),e<8?t.set(8,s-e-1,n,!0):e<9?t.set(8,15-e-1+1,n,!0):t.set(8,15-e-1,n,!0);t.set(s-8,8,1,!0)}static setupData(t,e){const i=t.cols;let s=-1,r=i-1,n=7,a=0;for(let o=i-1;o>0;o-=2)for(6===o&&o--;;){for(let i=0;i<2;i++)if(!t.isReserved(r,o-i)){let s=!1;a<e.length&&(s=1==(e[a]>>>n&1)),t.set(r,o-i,s),n--,-1===n&&(a++,n=7)}if(r+=s,r<0||i<=r){r-=s,s=-s;break}}}static createData(t,e,i,s){i||(i=new jt),s.forEach((function(e){i.put(e.mode.bit,4),i.put(e.getLength(),Qt.getCharCountIndicator(e.mode,t)),e.write(i)}));const r=8*(Nt.getSymbolTotalCodewords(t)-($t.getTotalCodewordsCount(t,e)||0));for(i.getLengthInBits()+4<=r&&i.put(0,4);i.getLengthInBits()%8!=0;)i.putBit(0);const n=(r-i.getLengthInBits())/8;for(let t=0;t<n;t++)i.put(t%2?17:236,8);return this.createCodewords(i,t,e)}static createCodewords(t,e,i){const s=Nt.getSymbolTotalCodewords(e),r=$t.getTotalCodewordsCount(e,i);if(!r)return;const n=s-r,a=$t.getBlocksCount(e,i);if(!a)return;const o=a-s%a,h=Math.floor(s/a),c=Math.floor(n/a),l=c+1,d=h-c,u=new re(d);let g=0;const p=new Array(a),m=new Array(a);let f=0;const C=new Uint8Array(t.buffer);for(let t=0;t<a;t++){const e=t<o?c:l;p[t]=C.slice(g,g+e),m[t]=u.encode(p[t]),g+=e,f=Math.max(f,e)}const P=new Uint8Array(s);let b,y,A=0;for(b=0;b<f;b++)for(y=0;y<a;y++)b<p[y].length&&(P[A++]=p[y][b]);for(b=0;b<d;b++)for(y=0;y<a;y++)P[A++]=m[y][b];return P}static create(t){let e=null===t.text||void 0===t.text?t.content:t.text;if(null==e||""===e)return;e=String(e);const i="number"==typeof t.eccLevel?t.eccLevel:Mt.Middle;let s="number"==typeof t.version?t.version:0,r="number"==typeof t.qrMask?t.qrMask:0;"function"==typeof t.toSJISFunc&&(Nt.toSJISFunction=t.toSJISFunc);const n=new jt;let a="number"==typeof t.waterMarkMode?t.waterMarkMode:-1;const o=t.waterMarkSeed||0;if(a>=0){a=a>3?3&Date.now():a;const t=this.getWaterSeedCheckSum(e,a,o);this.appendDtWaterMark(a,t,n)}let h=s;if(s<=0){const t=ge.rawSplit(e);h=me.getBestVersionForData(n,t,i)||0}const c=ge.fromString(e,h||40),l=me.getBestVersionForData(n,c,i);if(!l)throw new Error("The amount of data is too big to be stored in a QR Code");if(s<=0)s=l;else if(s<l)throw new Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+l+".\n");const d=this.createData(s,i,n,c);if(!d)return;const u=Nt.getSymbolSize(s),g=new C(u,u);return this.setupFinderPattern(g,s),this.setupTimingPattern(g),this.setupAlignmentPattern(g,s),this.setupFormatInfo(g,i,0),s>=7&&this.setupVersionInfo(g,s),this.setupData(g,d),r<=0&&(r=Jt.getBestMask(g,this.setupFormatInfo.bind(null,g,i))),Jt.applyMask(r,g),this.setupFormatInfo(g,i,r),g}static getWaterSeedCheckSum(t,e,i){const s=3&e;let r=this.sDtWaterMarkCheckSums[s];r=r+(i=Nt.calcWaterMarkSeed(i))&1048575;const n=y.getBytes_Utf8(t);for(let t=0;t<n.length;++t)r+=r>>>5,r+=(255&n[t])*(2&t?5:3),r+=1&t?13:11;return r=r%1019+3,r}static appendDtWaterMark(t,e,i){e=53248|(3&t)<<10|1023&e,this.appendStructuredAppend(e>>>8,255&e,i)}static appendStructuredAppend(t,e,i){this.appendModeInfo(Qt.STRUCTURED,i),i.put(t,8),i.put(e,8)}static appendModeInfo(t,e){e.put(t.bit,4)}}Ce.sDtWaterMarkCheckSums=[197,257,571,991];const Pe=Object.freeze({Auto:"auto",QRCode:"qrcode",PDF417:"pdf417",DMCode:"dataMatrix",GMCode:"gridMatrix"});class be{static getEncoder(t){if(!t)return;t=t.toUpperCase();const e=this.barcodeCreatorMap[t];return u.log(`---- getBarcode2DEncoder[${t}]: ${!!e}`),e}static setEncoder(t,e){return!(!t||!e||"function"!=typeof e.encode||(t=t.toUpperCase(),u.log(`---- setBarcode2DEncoder[${t}]:`),this.barcodeCreatorMap[t]=e,0))}static checkAndRegisterEncodeModule(t){if(!t||!window)return;const e=window;let i;if((t=t.toUpperCase())===Pe.PDF417.toUpperCase()?i=e.DzPdf417:t===Pe.DMCode.toUpperCase()?i=e.DzDataMatrix:t===Pe.GMCode.toUpperCase()&&(i=e.DzGridMatrix),i){if("function"==typeof i.getInstance){const e=i.getInstance();return this.setEncoder(t,e),e}return"function"==typeof i.register?(i.register(this.getInstance()),this.getEncoder(t)):void 0}}static getInstance(){return this._instance||(this._instance=new be)}static createQRCode(t){return Ce.create(t)}static create2DBarcode(t){const e=t.barcodeType||t.type;let i;return e&&(i=this.getEncoder(e),i||(i=this.checkAndRegisterEncodeModule(e))),null!==t.text&&void 0!==t.text||(t.text=t.content),i?i.encode(t):Ce.create(t)}static createPDF417(t){return t.barcodeType||(t.barcodeType=Pe.PDF417),this.create2DBarcode(t)}static createDataMatrix(t){return t.barcodeType||(t.barcodeType=Pe.DMCode),this.create2DBarcode(t)}static createGridMatrix(t){return t.barcodeType||(t.barcodeType=Pe.GMCode),this.create2DBarcode(t)}static drawQrcode(t,e){const i=Ce.create(Object.assign(Object.assign({},e),{eccLevel:Mt.Middle})),s=e.width||t.width;if(i){const e=new p({canvas:t});e.startJob({width:s}),e.drawQrcode({data:i,width:s})}}register(t){return!!t&&be.setEncoder(t.barcodeType,t)}}be.barcodeCreatorMap={};class ye{constructor(t,e,i){if(this.contentLeft="",this._currValue=0,this.currLength=0,this.contentRight="",this.degreeOffset=0,this.maxDegreeValue=0,!t)return;e=e||1,i=i||0;let s=-1,r=-1;for(r=t.length-1;r>=0&&!b.isDigit(t.charCodeAt(r));r--);if(!(r<0)){for(s=r-1;s>=0&&b.isDigit(t.charCodeAt(s))&&!(r-s>=ye.MaxDegreeLength);s--);s++,this._currValue=parseInt(t.substring(s,r+1)),this.maxDegreeValue=Number.MAX_VALUE,this.contentLeft=t.substring(0,s),this.contentRight=t.substring(r+1),this.currLength=i>0?i:r-s+1,this.degreeOffset=e||0}}get IsValid(){return 0!=this.degreeOffset}get CurrValue(){return this._currValue}set CurrValue(t){t>ye.MaxDegreeValue?this._currValue=ye.MaxDegreeValue:this._currValue=t<0?0:t}step(t){return this.CurrValue+=this.degreeOffset*t,this.toString()}get ShownDegree(){if(this.IsValid){let t=this.CurrValue||0;const e=t<0?"-":"";t=Math.abs(t);const i=t.toFixed();return`${e}${i.length<this.currLength?b.repeatChar("0",this.currLength-i.length):""}${i}`}return""}toString(){return this.IsValid?this.contentLeft+this.ShownDegree+this.contentRight:""}}ye.MaxDegreeLength=15,ye.MaxDegreeValue=Math.pow(10,ye.MaxDegreeLength)-1,ye.MaxDegreeOffset=Math.pow(10,ye.MaxDegreeLength-1);class Ae{get Text(){return this.text}constructor(){this.keyGroup=[],this.text=""}setKeyWordAction(t){this.keyWordAction=t}setKeys(t){if(!t)return;t=t.replace(/\\n/g,"\n"),this.text=t;const e=[];let i=0;for(let s=0,r=0,n=!1;s<t.length;s++){const a=t.charAt(s);"["===a?(n=!0,r=s):"]"===a&&n&&(n=!1,e.push({text:t.substring(i,r)}),e.push({text:t.substring(r+1,s),isKey:!0}),i=s+1)}i<t.length&&e.push({text:t.substr(i),isKey:e.length<1}),this.keyGroup=e}hasKeyWord(){return!!this.keyGroup.find((t=>t.isKey))}toString(t){const e=[];let i=!1;if(!this.keyWordAction)return t||this.text;for(const t of this.keyGroup)if(t.isKey){const s=this.keyWordAction(t.text);void 0!==s&&(e.push(s||""),i=!0)}else e.push(t.text);return!i&&t?t:e.join("")}}const Ie=Object.freeze({text:"text",barcode:"barcode",qrcode:"qrcode",pdf417:"pdf417",dataMatrix:"dataMatrix",data_matrix:"datamatrix",gridMatrix:"gridMatrix",grid_matrix:"gridmatrix",image:"image",rect:"rect",rectangle:"rectangle",ellipse:"ellipse",circle:"circle",line:"line",table:"table",arcText:"arcText",arc_text:"arctext",html:"html"});class Re{static Pix2MM(t,e){return 25.4*t/e}static MM2Pix(t,e){return t*e/25.4}static isPreviewJobName(t){return!!t&&((t=t.toLowerCase()).startsWith(this.JOB_NAME_PREV.substring(0,7))||t.startsWith(this.JOB_NAME_TRANS.substring(0,8)))}static isTransPrevJob(t){return!!t&&t.toLowerCase().startsWith(this.JOB_NAME_TRANS.substring(0,8))}static isWhitePrevJob(t){return!!t&&t.toLowerCase().startsWith(this.JOB_NAME_PREV.substring(0,7))}static calcPageHeight(t,e){return("number"!=typeof e||e<=0)&&(e=0),t.reduce(((t,e)=>{const i=(e.y||0)+(e.height||e.fontHeight||0);return i>t?i:t}),0)+(e>0?e:2)}static getJobStartOptions(t,e){const s=e||{};let r="number"==typeof s.printerDPI?s.printerDPI:t.printerDpi;"number"!=typeof r&&("number"==typeof t.dpi&&t.dpi>0?r=t.dpi:"number"==typeof t.printerDPI&&t.printerDPI>0&&(r=t.printerDPI));const n="number"==typeof s.printerWidth?s.printerWidth:t.printerWidth;return Object.assign(Object.assign({},t),{backgroundImage:t.background,borderImage:t.border,dpi:r,printerDpi:r,printerWidth:n,svgViewBox:"string"==typeof t.svgViewBox?i.parseRect(t.svgViewBox):t.svgViewBox,color:t.drawColor})}static loadImageSrc(t){u.log("#### 【DrawContext.loadImage】src:");const e="string"==typeof t?t:t.src;return u.log(e.length>100?e.substring(0,100)+"...":e),new Promise((t=>{try{if(e){const i=new Image;i.crossOrigin="anonymous",i.src=e,i.onload=()=>{t(i)},i.onerror=e=>{u.warn(e),t(null)}}else u.warn("【DrawContext.CreateImage: src不能为空!"),t(null)}catch(e){u.warn(e,"#### 【【DrawContext.loadImage.catch】 图片加载异常，error:"),t(null)}}))}static html2ImageSrc(t){return t?`data:image/svg+xml,\n<svg xmlns='http://www.w3.org/2000/svg'>\n<foreignObject width='100%' height='100%'>\n<div xmlns='http://www.w3.org/1999/xhtml' style='font-size:16px;font-family:Helvetica'>${t}</div>\n</foreignObject>\n</svg>`:t}static html2Image(t){const e=Re.html2ImageSrc(t);return Re.loadImageSrc(e)}static loadSvgContent(t){const e=new Blob([t],{type:"image/svg+xml"});return new Promise((i=>{const s=new FileReader;s.onload=t=>{var e;i(null===(e=t.target)||void 0===e?void 0:e.result)},s.onerror=e=>{u.warn(e,`---- failed to load svg: '${t.substring(0,50)}...'`),i(void 0)},s.readAsDataURL(e)}))}static getMargins(t){const e="number"==typeof t.margin&&t.margin>=0?t.margin:0,i="number"==typeof t.marginH&&t.marginH>=0?t.marginH:e,s="number"==typeof t.marginV&&t.marginV>=0?t.marginV:e,r=[];return r[0]="number"==typeof t.marginTop?t.marginTop:s,r[1]="number"==typeof t.marginRight?t.marginRight:i,r[2]="number"==typeof t.marginBottom?t.marginBottom:s,r[3]="number"==typeof t.marginLeft?t.marginLeft:i,r}get Context(){return this.cvs.Base.Context}get CanvasElement(){return this.cvs.Base.Canvas}get Canvas(){return this.cvs}get Dpi(){return this.cvs.Dpi}set Dpi(t){this.cvs.Dpi=t}get IsPreviewMode(){return this.previewMode}get JobInfo(){return this.jobOptions}get Width(){return this.jobWidth}get Height(){return this.jobHeight}get Orientation(){return this.jobOrientation||0}get JobName(){var t;return null===(t=this.jobOptions)||void 0===t?void 0:t.jobName}get ZoomRate(){return this.Canvas.DPM}set ZoomRate(t){t<this.mMinZoomRate&&(t=this.mMinZoomRate),t>this.mMaxZoomRate&&(t=this.mMaxZoomRate);const e=this.Canvas.DPM;t!==e&&(this.Canvas.DPM=t,this.CanvasElement.style&&(this.CanvasElement.style.borderRadius=.5*t+"px"),this.onZoomRateChanged(t,e))}get PixWidth(){return this.Canvas.DPM*this.Width}get PixHeight(){return this.Canvas.DPM*this.Height}get Foreground(){return this.Canvas.Foreground}set Foreground(t){(t||"").match(/^([A-Fa-f0-9]{3}|[A-Fa-f0-9]{6}|[A-Fa-f0-9]{8})$/)&&(t=`#${t}`),this.Canvas.Foreground=t}get Background(){return this.cvs.Base.Background}get IsApiMode(){return this.Canvas.Base.IsApiMode}set IsApiMode(t){this.Canvas.Base.IsApiMode=t}constructor(t){this.previewMode=!0,this.jobWidth=0,this.jobHeight=0,this.jobOrientation=0,this.mMinZoomRate=.5,this.mMaxZoomRate=40;const e="boolean"==typeof t?{previewMode:t}:t||{};e.creator||(e.creator=()=>this.createCanvas()),"function"!=typeof e.onCanvasClear&&(e.onCanvasClear=(t,e)=>{this.onCanvasClear(t,e)}),this.cvs=new m(e),this.mKeyWordParser=new Ae,"boolean"==typeof e.previewMode&&(this.previewMode=e.previewMode)}onZoomRateChanged(t,e){}createCanvas(){return u.log("---- DrawContext.createCanvas:"),null===document||void 0===document?void 0:document.createElement("canvas")}onCanvasClear(t,e){u.log("---- onCanvasClear:")}loadImage(t){return Re.loadImageSrc(t)}loadHtml(t){const e=Re.html2ImageSrc(t);return this.loadImage(e)}setMaxZoomRate(t){t>0&&(this.mMaxZoomRate=t)}setMinZoomRate(t){t>0&&(this.mMinZoomRate=t)}setOffset(t,e){switch(this.Orientation){case 1:case 90:this.Canvas.OffsetX=e,this.Canvas.OffsetY=-t;break;case 2:case 180:this.Canvas.OffsetX=-t,this.Canvas.OffsetY=-e;break;case 3:case 270:this.Canvas.OffsetX=-e,this.Canvas.OffsetY=t;break;default:this.Canvas.OffsetX=t,this.Canvas.OffsetY=e}}save(){this.Canvas.Base.Context.save()}restore(){this.Canvas.Base.Context.restore()}setItemOrientation(t){return this.Canvas.Base.ItemOrientation=t,!0}setItemHorizontalAlignment(t){return this.Canvas.Base.HorizontalAlign=t,!0}setItemVerticalAlignment(t){return this.Canvas.Base.VerticalAlign=t,!0}setFontName(t){this.Canvas.setFontName(t)}setFontHeight(t){this.Canvas.setFontHeight(t)}setLineWidth(t){this.Canvas.setLineWidth(t)}setLineSpace(t){return this.Canvas.setLineSpace(t),!0}setCharSpace(t){return this.Canvas.setCharSpace(t),!0}setAutoTextLine(t){this.Canvas.Base.AutoReturn=t}setRotation(t,e,i){return this.Canvas.setRotation(t>3?t:90*t,e,i),!0}setBorderAlign(t){this.Canvas.Base.BorderAlign=t}resetJobInfo(t){"number"!=typeof t.width&&(t.width=0),"number"!=typeof t.height&&(t.height=0),"number"!=typeof t.dpi&&(t.dpi=t.printerDpi),"number"!=typeof t.dpi&&(t.dpi=0),"number"!=typeof t.printerWidth&&(t.printerWidth=0),this.jobOptions=t,this.jobWidth=t.width>0?t.width:0,this.jobWidth<=0&&t.dpi>0&&t.printerWidth>0&&(this.jobWidth=t.printerWidth/t.dpi*25.4),this.jobHeight=t.height>0?t.height:t.width,this.jobOrientation=t.orientation||0,this.Canvas.Base.ItemOrientation=0,this.Canvas.Base.HorizontalAlign=0,this.Canvas.Base.VerticalAlign=0}poundToMm(t){return 25.4*t/72}mmToPound(t){return 72*t/25.4}isPrintJob(){return this.jobOptions&&this.jobOptions.printMode||!1}startJob(t){if((t=t||{}).width||(t.width=t.jobWidth||t.labelWidth),t.height||(t.height=t.jobHeight||t.labelHeight),"number"!=typeof t.width&&(t.width=0),"number"!=typeof t.height&&(t.height=0),"number"!=typeof t.printerWidth&&(t.printerWidth=0),t.width<=0&&t.printerWidth<=0)return;this.IsApiMode?("boolean"!=typeof t.printMode&&(t.printMode=!Re.isPreviewJobName(t.jobName)),t.printMode||(t.backgroundColor||(Re.isTransPrevJob(t.jobName)?t.backgroundColor=Re.JOB_COLOR_TRANS:t.backgroundColor=Re.JOB_COLOR_WHITE),t.color||(t.color=p.COLOR_FG_DEFAULT))):"boolean"!=typeof t.printMode&&(t.printMode=!this.previewMode),this.resetJobInfo(Object.assign({},t));const e=this.Canvas.startJob(t);return e?(t.borderImage&&("string"==typeof t.borderImage?this.drawImage({image:t.borderImage,width:t.width,height:t.height}):this.drawBorderImage({image:t.borderImage,svgElement:t.svgElement,borderScale:t.borderScale,tileMode:!t.svgElement,svgWidth:t.svgWidth,svgHeight:t.svgHeight,svgViewBox:t.svgViewBox})),Object.assign({},t,{canvas:e.Canvas,context:e.Context,isPreview:!t.printMode})):void 0}endJob(){const t=this.jobOptions;if(!t)return;const e=this.Canvas.commitJob();if(!e)return;const i=this.IsApiMode,s=Object.assign({},t,{canvas:e.Canvas,context:e.Context,isPreview:!t.printMode,apiMode:i});return i&&(t.printMode?(t.antiColor&&this.Canvas.inverseColors(),t.horizontalFlip&&this.Canvas.horizontalFlip(),s.imageData=e.getImageData(),s.dataUrl=t.withDataUrl?e.Canvas.toDataURL():""):s.dataUrl=e.Canvas.toDataURL()),s}commitJob(){return Promise.resolve(this.endJob())}drawLine(t){return"number"!=typeof t.lineWidth&&(t.lineWidth=Re.LINE_WIDTH_MM),this.Canvas.drawLine(t)}drawLines(t,e,i,s,r,n,a){n=n||[],a=a||[];let o=0;this.drawLine({x1:t,y1:e,x2:i,y2:s,lineWidth:r});for(let a=0;a<n.length;a++)o+=n[a],this.drawLine({x1:t,y1:e+o,x2:i,y2:s+o,lineWidth:r});o=0;for(let n=0;n<a.length;n++)o+=a[n],this.drawLine({x1:t+o,y1:e,x2:i+o,y2:s,lineWidth:r})}drawRect(t){return t.fill||"number"==typeof t.lineWidth||(t.lineWidth=Re.LINE_WIDTH_MM),this.Canvas.drawRect(t)}drawRectangle(t){return this.drawRect(t)}fillRect(t){return this.Canvas.drawRect(Object.assign(Object.assign({},t),{fill:!0}))}drawRoundRect(t){t.fill||"number"==typeof t.lineWidth||(t.lineWidth=Re.LINE_WIDTH_MM),this.Canvas.drawRoundRect(t)}drawEllipse(t){return t.fill||"number"==typeof t.lineWidth||(t.lineWidth=Re.LINE_WIDTH_MM),this.Canvas.drawEllipse(t)}drawCircle(t){return t.fill||"number"==typeof t.lineWidth||(t.lineWidth=Re.LINE_WIDTH_MM),this.Canvas.drawCircle(t)}drawText(t){return!t.fontHeight&&t.fontSize&&(t.fontHeight=this.poundToMm(t.fontSize)),t.fontHeight||(t.fontHeight=this.Canvas.getFontHeight()),t.fontHeight<2&&(t.fontHeight=2),this.Canvas.drawText(t)}drawArcText(t){return!t.fontHeight&&t.fontSize&&(t.fontHeight=this.poundToMm(t.fontSize)),this.Canvas.drawArcText(t)}splitText(t){return this.Canvas.splitText(t)}measureText(t){return this.Canvas.measureText(t)}measureFontSize(t){return this.Canvas.measureFontSize(t)}draw1DMatrix(t){return!!t.datas&&this.Canvas.draw1DBarcode(t)}drawBarcode(t){return"string"==typeof t.barcodeType&&t.barcodeType?this.draw2DBarcode(t):this.draw1DBarcode(t)}draw1DBarcode(t){const e=Bt.create1DBarcode(t);return t.barcodeType===A.ISBN&&(t.textFlag=2),!!e&&this.draw1DMatrix(Object.assign(t,{datas:e.items}))}draw2DBarcode(t){const e=be.create2DBarcode(t);return!!e&&this.draw2DMatrix(Object.assign(Object.assign({},t),{data:e}))}draw2DMatrix(t){return!!t.data&&this.Canvas.draw2DBarcode(t)}drawQRCode(t){const e=be.create2DBarcode(t);return!!e&&this.draw2DMatrix(Object.assign(Object.assign({},t),{data:e,margin:"number"==typeof t.margin?t.margin:void 0}))}draw2DQRCode(t){return this.drawQRCode(t)}drawPDF417(t){const e=be.createPDF417(t);return!!e&&this.draw2DMatrix(Object.assign(Object.assign({},t),{data:e,margin:"number"==typeof t.margin?t.margin:void 0}))}draw2DPdf417(t){return this.drawPDF417(t)}drawDataMatrix(t){const e=be.createDataMatrix(t);return!!e&&this.draw2DMatrix(Object.assign(Object.assign({},t),{data:e,margin:"number"==typeof t.margin?t.margin:void 0}))}draw2DDataMatrix(t){return this.drawDataMatrix(t)}drawGridMatrix(t){const e=be.createGridMatrix(t);return!!e&&this.draw2DMatrix(Object.assign(Object.assign({},t),{data:e,margin:"number"==typeof t.margin?t.margin:void 0}))}draw2DGridMatrix(t){return this.drawGridMatrix(t)}drawImage(t){if(t.image){const e=t.image;return e.dzSrc&&(t.image=e.dzSrc),this.Canvas.drawImage(t)}return!1}drawImageResizeLabel(t){return this.Canvas.drawImageResizeLabel(t)}drawBorderImage(t){const e=t.image;if(!e||"string"==typeof e)return;const i=t.svgElement,s="boolean"==typeof t.svgMode?t.svgMode:!!i;let r=e.width||t.imageWidth||0,n=e.height||t.imageHeight||0;if(i){u.log("---- SVG 图片适配:");const i=t.svgViewBox,s=t.svgWidth,a=t.svgHeight,o=this.Canvas.CanvasWidth,h=this.Canvas.CanvasHeight;if(s&&s>0||a&&a>0)r||(r=s||a||0),n||(n=a||s||0);else{let t=r,s=n;i&&(t=i.width,s=i.height),t&&s?t/s>o/h?(r=o,n=o*s/t):(n=h,r=t*h/s):r=n=Math.min(o,h),"string"!=typeof e&&(e.width=r,e.height=n)}}if(!r||!n)return void u.warn("---- drawBorderImage.fail: [无法获取图片大小]！");const a=r/3,o=n/3;this.Canvas.drawImageResizeLabel({img:e.dzSrc||e,left:a,top:o,right:e.width-a,bottom:e.height-o,tileMode:!s&&t.tileMode,relativeScale:s?t.borderScale||1:void 0,imageWidth:r,imageHeight:n})}drawItem(t,e,i){const s=t;if(t.color){const e=this.JobInfo;this.IsApiMode&&(null==e?void 0:e.printMode)?t.color=void 0:this.IsPreviewMode||(t.color=void 0)}const r=t.type&&"string"==typeof t.type?t.type.toLowerCase():Ie.text,n="number"==typeof s.contentType?s.contentType:e?2:0;if(2===n&&e){let t;!s.columnName&&s.dataColumnName&&(s.columnName=s.dataColumnName),s.columnName?(this.mKeyWordParser.setKeys(s.columnName),t=this.mKeyWordParser.hasKeyWord()?this.mKeyWordParser.toString(s.text):e[s.columnName]):s.text&&s.text.indexOf("[")>=0&&(this.mKeyWordParser.setKeys(s.text),this.mKeyWordParser.hasKeyWord()&&(t=this.mKeyWordParser.toString(s.text))),void 0!==t&&(s.text=t)}else if(1===n&&i&&i>0){const e=t;e.rawText||(e.rawText=e.text);const s=e.rawText||"",r=new ye(s,e.degreeOffset,e.degreeLength);r.IsValid&&(e.text=r.step(i))}switch("number"==typeof t.horAlignment&&"number"!=typeof t.horizontalAlignment&&(t.horizontalAlignment=t.horAlignment),"number"==typeof t.verAlignment&&"number"!=typeof t.verticalAlignment&&(t.verticalAlignment=t.verAlignment),"boolean"==typeof t.filled&&"boolean"!=typeof t.fill&&(t.fill=t.filled),r){case Ie.text:return this.drawText(t);case Ie.barcode:return this.draw1DBarcode(t);case Ie.qrcode:return this.drawQRCode(t);case Ie.pdf417:return this.drawPDF417(t);case Ie.data_matrix:return this.drawDataMatrix(t);case Ie.grid_matrix:return this.drawGridMatrix(t);case Ie.image:return this.drawImage(t);case Ie.rect:case Ie.rectangle:return this.drawRect(t);case Ie.ellipse:return this.drawEllipse(t);case Ie.circle:return this.drawCircle(t);case Ie.arc_text:return this.drawArcText(t);case Ie.line:return this.drawLine(t);case Ie.table:return this.drawTable(t,e,i);default:return u.warn(`---- 不支持的 DrawType: ${t.type}`),!1}}static getUnionRectOfRelateRect(t,e,s){for(const r of e)r!==t?i.hasIntersection(t,r)&&(s&&s.push(r),t=i.getUnionRect(t,r)):s&&s.push(r);return t}static shrinkCellContent(t,e){return{x:t.x+e,y:t.y+e,width:t.width-2*e,height:t.height-2*e}}drawTableWithoutRotation(t,e,s){!t.tableRows&&Array.isArray(t.rows)&&(t.tableRows=t.rows);const r=t.width||0,n=t.height||0,a=t.tableRows||[];if(r<=0||n<=0||a.length<=0)return!1;"number"!=typeof t.lineWidth&&(t.lineWidth=Re.LINE_WIDTH_MM);const o=t.lineWidth>0?t.lineWidth:0,h=t.cellPadding||0,c=.5*o,l="number"==typeof t.horizontalAlignment?t.horizontalAlignment:1,d="number"==typeof t.verticalAlignment?t.verticalAlignment:1,u=t.x||0,g=t.y||0;let p="number"==typeof t.rowCount&&t.rowCount>0?t.rowCount:0,m="number"==typeof t.columnCount&&t.columnCount>0?t.columnCount:0;p<=0&&(p="number"==typeof t.rows&&t.rows>0?t.rows:a.length),m<=0&&(m="number"==typeof t.columns&&t.columns>0?t.columns:a.reduce(((t,e)=>e&&e.length>t?e.length:t),0));let f=Array(m).fill(.5);t.columnWidths&&t.columnWidths.length>1?f=t.columnWidths:("string"==typeof t.colWidth&&t.colWidth&&(t.colWidth=t.colWidth.split(",")),Array.isArray(t.colWidth)&&t.colWidth.length>1&&(f=t.colWidth.map((t=>t?Number(t):0)))),f.length>m?f=f.slice(0,m):f.length<m&&(f=f.concat(Array(m-f.length).fill(f[f.length-1]))),f=f.map((t=>t>0?t:.5));const C=f.filter((t=>t>1)).reduce(((t,e)=>t+e),0),P=f.filter((t=>t<=1)).reduce(((t,e)=>t+e),0);P>0?f=f.map((t=>t>1?t:(r-C)/P*t)):C!==r&&(f=f.map((t=>t/C*r)));let b=Array(p).fill(.5);t.rowHeights&&t.rowHeights.length>1?b=t.rowHeights:("string"==typeof t.rowHeight&&t.rowHeight&&(t.rowHeight=t.rowHeight.split(",")),Array.isArray(t.rowHeight)&&t.rowHeight.length>1&&(b=t.rowHeight.map((t=>t?Number(t):0)))),b.length>p?b=b.slice(0,p):b.length<p&&(b=b.concat(Array(p-b.length).fill(b[b.length-1]))),b=b.map((t=>t>0?t:.5));const y=b.filter((t=>t>1)).reduce(((t,e)=>t+e),0),A=b.filter((t=>t<=1)).reduce(((t,e)=>t+e),0);A>0?b=b.map((t=>t>1?t:(n-y)/A*t)):y!==n&&(b=b.map((t=>t/y*n)));let I=t.groups||[];if("string"==typeof t.group&&(I=t.group.split(";").map((t=>{const e=t.split(",").map((t=>t[0]>="0"&&t[0]<="9"?Number(t):0));return e.length>3?{x:e.length>1?e[1]-1:0,y:e.length>0?e[0]-1:0,width:e.length>3?e[3]-e[1]+1:0,height:e.length>2?e[2]-e[0]+1:0}:void 0})).filter((t=>!!t))),a.forEach(((t,e)=>{for(let i=0;i<m;i++){const s=t[i];if("object"==typeof s){const t=s.rowSpan||0,r=s.columnSpan||s.colSpan||0;(t>1||r>1)&&I.push({x:i,y:e,width:r>1?r:1,height:t>1?t:1})}}})),I.length>1){const t=I.slice(0);I.splice(0);const e=[];for(const i of t){if(e.indexOf(i)>=0)continue;const s=Re.getUnionRectOfRelateRect(i,t,e);I.push(s)}}o>0&&this.drawRect({x:t.x,y:t.y,width:t.width,height:t.height,lineWidth:o});for(let r=0,n=0;r<p;n+=b[r],r++){const C=a[r]||[];for(let a=0,P=0;a<m;P+=f[a],a++){const y={x:a,y:r,width:1,height:1},A=I.filter((t=>i.isInnerRect(y,t))).shift();if(A){if(y.x!==A.x||y.y!==A.y)continue;y.width=A.width,y.height=A.height}const R=(t,e)=>(t||0)+e,E={x:u+P,y:g+n,width:y.width>0?f.slice(y.x,y.x+y.width).reduce(R,0):f[y.x],height:y.height>0?b.slice(y.y,y.y+y.height).reduce(R,0):b[y.y]};y.x>0&&o>0&&this.drawLine({x1:E.x,x2:E.x,y1:E.y+(y.y<=0?c:0),y2:E.y+E.height-(y.y+y.height>=p?c:0),lineWidth:o}),y.y>0&&o>0&&this.drawLine({x1:E.x+(y.x<1?c:0),x2:E.x+E.width-(y.x+y.width>=m?c:0),y1:E.y,y2:E.y,lineWidth:o});const v=C[a];if(void 0!==v){let i=v;"object"!=typeof v&&(i={type:Ie.text,text:v}),i.type||(i.type=Ie.text);const r=i;"number"!=typeof r.horizontalAlignment&&(r.horizontalAlignment="number"==typeof r.horAlignment?r.horAlignment:l),"number"!=typeof r.verticalAlignment&&(r.verticalAlignment="number"==typeof r.verAlignment?r.verAlignment:d);let n=c+h;if(i.type===Ie.text){const e=i;e.fontHeight||(e.fontHeight=t.fontHeight||.3*E.height),!e.fontName&&t.fontName&&(e.fontName=t.fontName),"number"!=typeof e.fontStyle&&"number"==typeof t.fontStyle&&(e.fontStyle=t.fontStyle),h<=0&&(n=c+1)}else i.type===Ie.qrcode&&h<=0&&(n=c+1);this.drawItem(Object.assign(i,Re.shrinkCellContent(E,n)),e,s)}}}return!0}drawTable(t,e,i){let s=t.x||0,r=t.y||0,n=t.width||0,a=t.height||0;if(!this.jobOptions)return!1;let o=Array.isArray(t.margin)?t.margin[0]:t.margin,h=Array.isArray(t.margin)?t.margin[1]:o;if(n<=0){const e=this.jobWidth;o="number"==typeof o&&o>=0?o:.05*e,s=t.x="number"==typeof t.x?t.x:o,n=t.width=e-s-o}if(a<=0){const e=this.jobHeight;h="number"==typeof h&&h>=0?h:.05*e,r=t.y="number"==typeof t.y?t.y:h,a=t.height=e-r-h}const c=t.columnCount||t.columns||t.cols||0;if(t.Cells&&Array.isArray(t.Cells)&&!t.cells&&(t.cells=t.Cells,delete t.Cells),!t.tableRows)if(Array.isArray(t.rows))t.tableRows=t.rows;else if(t.cells&&c>0){t.tableRows=[];for(let e=0;e<t.cells.length;e+=c)t.tableRows.push(t.cells.slice(e,e+c))}if(!t.tableRows||t.tableRows.length<=0)return!1;t.fontSize&&!t.fontHeight&&(t.fontHeight=this.poundToMm(t.fontSize)),this.save(),this.setRotation(t.orientation||0,{x:s,y:r},{width:n,height:a});const l=this.drawTableWithoutRotation(t,e,i);return this.restore(),l}printPageGroup(t,e){const i=t.jobPages||[],s=t.jobArguments||[],r={printCopies:1,copyIndex:0,printPages:1,pageIndex:0},n=t.jobInfo||{},a=t.printerInfo||{},o=Re.getJobStartOptions(n,a),h=n.jobHeight||0,c=Re.getMargins(n);let l=0;const d=n.startPage&&n.startPage>0?n.startPage:0,u=n.printPages||0;if(s.length>0||u>1&&i.length<=1){const t=s.length>0?s.length:d+u;let n=t;u>0&&(n=d+u>t?t:d+u),h<=0&&(o.height=Re.calcPageHeight(i[0],c[2]));for(let t=d;t<n;t++){const a=s[t];if(r.printPages=n,r.pageIndex=t,!this.startJob(Object.assign({},o)))break;this.mKeyWordParser.setKeyWordAction((t=>a[t]));for(const e of i[0])this.drawItem(e,a,t);const h=this.endJob();if(!h)break;const c=e(Object.assign(h,r));if("number"==typeof c&&(l=c),l>0)break;l<0&&t--}}else{let t=i.length;u>0&&(t=d+u>i.length?i.length:d+u);for(let s=d;s<t;s++){r.printPages=t,r.pageIndex=s,h<=0&&(o.height=Re.calcPageHeight(i[s],c[2]));const n=i[s];if(!this.startJob(Object.assign({},o)))break;for(const t of n)this.drawItem(t);const a=this.endJob();if(!a)break;const d=e(Object.assign(a,r));if("number"==typeof d&&(l=d),l>0)break;l<0&&s--}}return l}printAsyncPageGroup(t,e){return f(this,void 0,void 0,(function*(){const i=t.jobPages||[],s=t.jobArguments||[],r={printCopies:1,copyIndex:0,printPages:1,pageIndex:0},n=t.jobInfo||{},a=t.printerInfo||{},o=Re.getJobStartOptions(n,a),h=n.jobHeight||0,c=Re.getMargins(n);let l=0,d=n.startPage&&n.startPage>0?n.startPage:0;const u=n.printPages||0;if(s.length>0||u>1&&i.length<=1){const t=s.length>0?s.length:d+u;let n=t;u>0&&(n=d+u>t?t:d+u),d>=n&&(d=n-1),h<=0&&(o.height=Re.calcPageHeight(i[0],c[2]));for(let t=d;t<n;t++){const a=s[t];if(r.printPages=n,r.pageIndex=t,r.isEndPage=t+1>=n,!this.startJob(Object.assign({},o)))break;this.mKeyWordParser.setKeyWordAction((t=>a[t]));for(const e of i[0])this.drawItem(e,a,t);const h=yield this.commitJob();if(!h)break;const c=yield e(Object.assign(h,r));if("number"==typeof c&&(l=c),l>0)break;l<0&&t--}}else{let t=i.length;u>0?t=d+u>i.length?i.length:d+u:i.length<=1&&d>0&&(d=0);for(let s=d;s<t;s++){r.printPages=i.length,r.pageIndex=s,r.isEndPage=s+1>=i.length,h<=0&&(o.height=Re.calcPageHeight(i[s],c[2]));const t=i[s];if(!this.startJob(Object.assign({},o)))break;for(const e of t)this.drawItem(e);const n=yield this.commitJob();if(!n)break;const a=yield e(Object.assign(n,r));if("number"==typeof a&&(l=a),l>0)break;l<0&&s--}}return l}))}drawJob(t){u.log("#### drawJob:"),t.jobPages||(t.jobPages=[]);const e=t.jobInfo||{},i=e.startCopy||0,s=e.remainCopies||0;let r=0;if(t.jobPages.length<=0&&t.jobPage&&t.jobPages.push(t.jobPage),t.jobPages.length<=0)return r=1,r;const n=e.printCopies&&e.printCopies>0?e.printCopies:1;if(n>1&&e.autoPage){for(let e=0;e<n&&(r=this.printPageGroup(t,(r=>t.onPageComplete?(r.printCopies=n+i+s,r.copyIndex=e+i,r.isEndCopy=e+1>=n,t.onPageComplete(r)||0):0)),0===r);e++);return r}return this.printPageGroup(t,(e=>(t.onPageComplete&&(r=t.onPageComplete(Object.assign(Object.assign({},e),{printCopies:n+i+s,copyIndex:i,isEndCopy:!0}))||0),r)))}drawAsyncJob(t){return f(this,void 0,void 0,(function*(){u.info("#### drawAsyncJob:"),t.jobPages||(t.jobPages=[]);const e=t.jobInfo||{},i=e.startCopy||0,s=e.remainCopies||0;let r=0;if(t.jobPages.length<=0&&t.jobPage&&t.jobPages.push(t.jobPage),t.jobPages.length<=0)return r=1,r;const n=e.printCopies&&e.printCopies>0?e.printCopies:1;if(n>1&&e.autoPage){for(let e=0;e<n&&(r=yield this.printAsyncPageGroup(t,(r=>{var a;return t.onPageComplete?(r.printCopies=n+i+s,r.copyIndex=e+i,r.isEndCopy=e+1>=n,Promise.resolve(null!==(a=t.onPageComplete(r))&&void 0!==a?a:0)):Promise.resolve(0)})),0===r);e++);return r}return yield this.printAsyncPageGroup(t,(e=>t.onPageComplete?Promise.resolve(t.onPageComplete(Object.assign(Object.assign({},e),{printCopies:n+i+s,copyIndex:i,isEndCopy:!0}))||0):Promise.resolve(0)))}))}autoLoadDrawItemImage(t){return f(this,void 0,void 0,(function*(){if(t.type===Ie.image){const e=t;e&&"string"==typeof e.image&&(e.image=yield this.loadImage(e.image))}else if(t.type===Ie.html){const e=t;"string"!=typeof e.html&&e.html&&(e.html=e.html.innerHTML),e.image=yield this.loadHtml(e.html)}else if(t.type===Ie.table){const e=t;if(e.tableRows)for(const t of e.tableRows)for(const e of t||[])e&&"string"!=typeof e&&e.type&&(yield this.autoLoadDrawItemImage(e));else if(e.cells)for(const t of e.cells)t&&"string"!=typeof t&&t.type&&(yield this.autoLoadDrawItemImage(t))}return!0}))}autoLoadImage(t){return f(this,void 0,void 0,(function*(){const e=t.jobInfo;e&&((this.IsApiMode?Re.isPreviewJobName(e.jobName):this.IsPreviewMode)?e.background&&"string"==typeof e.background&&(e.background=yield this.loadImage(e.background)):e.background=void 0,e.border&&"string"==typeof e.border&&(e.border=yield this.loadImage({src:e.border,withSize:!0}))),t.jobPage&&(!t.jobPages||t.jobPages.length<=0)&&(t.jobPages=[t.jobPage]);const i=t.jobPages||[];for(const t of i)for(const e of t)yield this.autoLoadDrawItemImage(e);return t}))}}Re.JOB_NAME_TRANS="#!#transparent#!#",Re.JOB_NAME_PREV="#!#preview#!#",Re.JOB_COLOR_WHITE="#fff",Re.JOB_COLOR_TRANS="transparent",Re.LINE_WIDTH_MM=.35;class Ee{constructor(t){this._margin=[],this._radius=0,this.data=t.data,this.init(t)}get MarginTop(){return this._margin.length>0?this._margin[0]:0}get MarginRight(){return this._margin.length>1?this._margin[1]:0}get MarginLeft(){return this._margin.length>3?this._margin[3]:this.MarginRight}get MarginBottom(){return this._margin.length>2?this._margin[2]:this.MarginTop}get CornerRadius(){return this._radius}init(t){this._radius=t.radius||0,this._margin=t.margin||[],this._jobStartAction=t.onJobStart,1===this._margin.length&&this._margin.push(this._margin[0]),t.element&&this.attachTo({element:t.element})}createDrawContext(t){return new Re(Object.assign(Object.assign({},t),{apiMode:!1,previewMode:!0,position:"relative"}))}attachTo(t){return f(this,void 0,void 0,(function*(){if(!t.element&&!t.canvas)return!1;if(!this.drawContext){const e=this.createDrawContext(t);if(!e)return!1;this.drawContext=e}const e=t.element,i=t.canvas;if(e)return this.drawContext.Canvas.Base.appendTo(e),this.raiseViewChanged(e.offsetWidth,e.offsetHeight);{const e=t.viewWidth||i.width||0,s=t.viewHeight||i.height||0;return this.raiseViewChanged(e,s)}}))}raiseViewChanged(t,e){return f(this,void 0,void 0,(function*(){const i=this.data?this.data.jobInfo:void 0,s=(null==i?void 0:i.jobWidth)||0,r=(null==i?void 0:i.jobHeight)||0,n=this.drawContext;if(!i)return u.warn("---- 未检测到 jobInfo 相关信息！"),!1;if(s<=0||r<=0)return u.warn(`---- 纸张大小错误：{jobWidth: ${s}, jobHeight: ${r}}`),!1;if(!n)return u.warn("---- 未检测到绘制上下文！"),!1;if(t<=0||e<=0)return u.warn(`---- 参数错误：viewWidth = ${t}, viewHeight = ${e}`),!1;const a=e-2,o=t-2-this.MarginLeft-this.MarginRight,h=a-this.MarginTop-this.MarginBottom,c=o>0?o/s:0,l=h>0?h/r:0;return n.ZoomRate=Math.min(c,l),this.updateCanvasPosition(n),this.refreshView()}))}updateCanvasPosition(t){var e;if("absolute"!==(null===(e=t.CanvasElement.style)||void 0===e?void 0:e.position))return;const i=t.CanvasElement.parentElement;if(i){if(i.parentElement){const e=i.parentElement,s=e.offsetWidth-2,r=e.offsetHeight-2,n=this.MarginLeft+this.MarginRight+t.PixWidth,a=this.MarginTop+this.MarginBottom+t.PixHeight;i.style.width=`${n>s?n:s}px`,i.style.height=`${a>r?a:r}px`}const e=i.offsetWidth,s=i.offsetHeight,r=.5*(e-t.PixWidth),n=this.MarginTop+.5*(s-this.MarginTop-this.MarginBottom-t.PixHeight);t.CanvasElement.style&&(t.CanvasElement.style.left=`${r}px`,t.CanvasElement.style.top=`${n}px`,t.CanvasElement.style.borderRadius=t.ZoomRate*this._radius+"px")}}refreshView(){return f(this,void 0,void 0,(function*(){const t=this.drawContext,e=this.data.jobInfo;if(!t||!e)return!1;const i=Re.getJobStartOptions(e,this.data.printerInfo),s=t.startJob(i);return!!s&&(this._jobStartAction&&(yield Promise.resolve(this._jobStartAction(s))),yield t.autoLoadImage(this.data),t.drawAsyncJob(this.data).then((t=>0===t)))}))}}class ve{static innerDecode(t){const e=new Uint16Array(32),i=t.length,s=i-32;let r="",n="",a=0,o=0,h=0,c=-1;for(let l=0,d=0,u=0,g=0;g<i;){for(a=g<=s?32:i-g|0;l<a;g=g+1|0,l=l+1|0){switch(d=255&t[g],d>>4){case 15:if(u=255&t[g=g+1|0],u>>6!=2||247<d){g=g-1|0;break}o=(7&d)<<6|63&u,h=5,d=256;case 14:u=255&t[g=g+1|0],o<<=6,o|=(15&d)<<6|63&u,h=u>>6==2?h+4|0:24,d=d+256&768;case 13:case 12:u=255&t[g=g+1|0],o<<=6,o|=(31&d)<<6|63&u,h=h+7|0,g<i&&u>>6==2&&o>>h&&o<1114112?(d=o,o=o-65536|0,0<=o?(c=55296+(o>>10)|0,d=56320+(1023&o)|0,l<31?(e[l]=c,l=l+1|0,c=-1):(u=c,c=d,d=u)):a=a+1|0):(d>>=8,g=g-d-1|0,d=65533),h=0,o=0,a=g<=s?32:i-g|0;default:e[l]=d;continue;case 11:case 10:case 9:case 8:}e[l]=65533}if(n+=String.fromCharCode(e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15],e[16],e[17],e[18],e[19],e[20],e[21],e[22],e[23],e[24],e[25],e[26],e[27],e[28],e[29],e[30],e[31]),l<32&&(n=n.slice(0,l-32|0)),g<i){if(e[0]=c,l=~c>>>31,c=-1,n.length<r.length)continue}else-1!==c&&(n+=String.fromCharCode(c));r+=n,n=""}return r}static decode(t,e){try{return new TextDecoder(e).decode(Uint8Array.from(t))}catch(e){return u.log("DzTextDecoder.decode: TextDecoder is not defined"),this.innerDecode(t)}}}class xe{static decode(t){try{let e="";return e="function"==typeof xe.decodeAction?xe.decodeAction(t):new TextDecoder("gbk").decode(t),e}catch(t){return console.warn(t),""}}static initEncodeTable(){if(!this._table){u.log("---- start to initEncodeTable:");const t=[[161,169,161,254],[176,247,161,254],[129,160,64,254],[170,254,64,160],[168,169,64,160],[170,175,161,254],[248,254,161,254],[161,167,64,160]],e=new Uint16Array(23940);let i=0;for(const[s,r,n,a]of t)for(let t=n;t<=a;t++)if(127!==t)for(let n=s;n<=r;n++)e[i++]=t<<8|n;this._table=new Uint16Array(65536),this._table.fill(65535);const s=xe.decode(e);if(!s)return this._table=new Uint16Array(0),this._table;for(let t=0;t<s.length;t++)this._table[s.charCodeAt(t)]=e[t];u.log("---- end to initEncodeTable:")}return this._table}static encode(t,e){t=t||"";const i=this.initEncodeTable();if(i.length<=0)return void console.warn("---- 当前系统不支持该操作。");const s=new Uint16Array(t.length);for(let r=0;r<t.length;r++){const n=t.charCodeAt(r);if(n<128){s[r]=n;continue}const a=i[n];if(65535!==a)s[r]=a;else if(8364===n)s[r]=128;else{const t=e?e(r):63;if(-1===t)return s.subarray(0,r);s[r]=t}}return s}static encodeToUint8Array(t,e){t=t||"";const i=this.initEncodeTable();if(i.length<=0)return void console.warn("---- 当前系统不支持该操作。");const s=new Uint8Array(2*t.length);let r=0;for(let n=0;n<t.length;n++){const a=t.charCodeAt(n);if(a<128){s[r++]=a;continue}const o=i[a];if(65535!==o)s[r++]=o,s[r++]=o>>8;else if(8364===a)s[r++]=128;else{const t=e?e(n):63;if(-1===t)return s.subarray(0,r);t>255?(s[r++]=t,s[r++]=t>>8):s[r++]=t}}return s.subarray(0,r)}static uint8ArrayToUint16Array(t,e){const i=[];for(let s=0,r=0,n=0;s<t.length;s++)t[s]<128?i.push(t[s]):(r=255&t[s++],n=s<t.length?255&t[s]:0,e?i.push(r<<8|n):i.push(n<<8|r));return Uint16Array.from(i)}}var we,De,Se,_e;!function(t){t[t.ASYNC_WAIT=-1]="ASYNC_WAIT",t[t.OK=0]="OK",t[t.ERROR_PARAM=1]="ERROR_PARAM",t[t.ERROR_NO_PRINTER=2]="ERROR_NO_PRINTER",t[t.ERROR_DISCONNECTED=3]="ERROR_DISCONNECTED",t[t.ERROR_CONNECT_FAILED=4]="ERROR_CONNECT_FAILED",t[t.ERROR_START_NOTIFICATION=5]="ERROR_START_NOTIFICATION",t[t.ERROR_DATA_SEND_ERROR=6]="ERROR_DATA_SEND_ERROR",t[t.ERROR_DATA_RECEIVE_ERROR=7]="ERROR_DATA_RECEIVE_ERROR",t[t.ERROR_IS_PRINTING=8]="ERROR_IS_PRINTING",t[t.ERROR_RESPONSE_TIMEOUT=9]="ERROR_RESPONSE_TIMEOUT",t[t.ERROR_PRINTER_CANCELED=10]="ERROR_PRINTER_CANCELED",t[t.ERROR_JOB_CREATE=16]="ERROR_JOB_CREATE",t[t.ERROR_JOB_CANCELED=17]="ERROR_JOB_CANCELED",t[t.ERROR_GET_IMAGE_DATA=18]="ERROR_GET_IMAGE_DATA",t[t.ERROR_PRINTER_NOT_AVAILABLE=19]="ERROR_PRINTER_NOT_AVAILABLE",t[t.ERROR_DATA_PARSE=20]="ERROR_DATA_PARSE",t[t.ERROR_OTHER=32]="ERROR_OTHER"}(we||(we={})),function(t){t[t.Unset=255]="Unset",t[t.None=0]="None",t[t.Hole=1]="Hole",t[t.Gap=2]="Gap",t[t.Black=3]="Black",t[t.Trans=4]="Trans"}(De||(De={})),function(t){t[t.Unset=255]="Unset",t[t.Min=1]="Min",t[t.Low=2]="Low",t[t.Normal=3]="Normal",t[t.High=4]="High",t[t.Max=5]="Max"}(Se||(Se={})),function(t){t[t.Unset=255]="Unset",t[t.Min=1]="Min",t[t.Low=4]="Low",t[t.Normal=6]="Normal",t[t.High=10]="High",t[t.Max=15]="Max"}(_e||(_e={}));class Te{static decodeBase64(t){if(t){if(Te.decodeBase64)return Te.decodeBase64(t);if("function"==typeof atob){const e=Te.atob(t);let i=e.length;const s=new Uint8Array(i);for(;i--;)s[i]=e.charCodeAt(i);return s.buffer}}}static arrayBuffer2Base64(t){let e=0,i=0,s=0;const r=t.length;let n="";for(let a=0;a<r;){if(e=t[a++],a==r){n+=Te.base64EncodeChars.charAt(e>>2),n+=Te.base64EncodeChars.charAt((3&e)<<4),n+="==";break}if(i=t[a++],a==r){n+=Te.base64EncodeChars.charAt(e>>2),n+=Te.base64EncodeChars.charAt((3&e)<<4|(240&i)>>4),n+=Te.base64EncodeChars.charAt((15&i)<<2),n+="=";break}s=t[a++],n+=Te.base64EncodeChars.charAt(e>>2),n+=Te.base64EncodeChars.charAt((3&e)<<4|(240&i)>>4),n+=Te.base64EncodeChars.charAt((15&i)<<2|(192&s)>>6),n+=Te.base64EncodeChars.charAt(63&s)}return n}static base64ToArrayBuffer(t){const e=t.length,i=[],s=new Uint8Array(4);let r=0;for(let n=0;n<e;){for(r=0;r<s.length&&n<e;n++)s[r]=Te.base64DecodeChars[255&t.charCodeAt(n)],s[r]>=0&&s[r]<=64?r++:10!==s[r]||u.warn(`---- 检测到无效的字符：data[${n}] = '${t.charAt(n)}', charCode = 0x${t.charCodeAt(n).toString(16)}`);if(r>0&&r<s.length)return u.warn(`---- base64字符串解析失败，pos = ${r}, i = ${n}, data.length = ${e}`),Uint8Array.from([]);if(i.push(s[0]<<2|(48&s[1])>>4),s[2]>=64)break;s[3]>=64?i.push((15&s[1])<<4|(60&s[2])>>2):(i.push((15&s[1])<<4|(60&s[2])>>2),i.push((3&s[2])<<6|s[3]))}return Uint8Array.from(i)}static atob(t){try{if(window&&"function"==typeof window.atob)return atob(t);{const e=Te.base64ToArrayBuffer(t);return ve.decode(new Uint8Array(e),"utf-8")}}catch(t){return u.warn(t),""}}static sleep(t,e){return new Promise((i=>{setTimeout((()=>{i(e)}),t)}))}static execAsync(t,e,i){return new Promise((s=>{setTimeout((()=>{t(e).then((t=>{s(t)}))}),i||0)}))}static processSuccessResult(t,e,i){return e&&(e.success&&e.success(t),e.complete&&e.complete(t)),i&&i(t),t}static processFailResult(t,e,i){return e&&(e.fail&&e.fail(t),e.complete&&e.complete(t)),i&&i(t),t}static getBytes(t){const e=[];let i="";for(let s=0;s<t.length;s+=2)i=t.substring(s,s+2),e.push(parseInt(i,16));return Uint8Array.from(e)}static toHexByteString(t,e){return(e||"")+`00${t.toString(16)}`.slice(-2)}static toHexShortString(t,e){return(e||"")+`0000${t.toString(16)}`.slice(-4)}static toHexString(t,e){return(e||"")+`00000000${t.toString(16)}`.slice(-8)}static getHexStringOfBytes(t,e,i){return Array.from(t).map((t=>Te.toHexByteString(t,i))).join(e)}static isAndroid(){return!!navigator&&/android/i.test(navigator.userAgent||"")}static isIOS(){return!!navigator&&/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent||"")}static getRequestData(t,e){if(null==e?e=[]:"object"!=typeof e&&(e=[e]),e.length&&"object"==typeof e[0])return e[0];const i={};if("string"==typeof t&&(t=[t]),t.length<1||!t[0])return i;for(let s=0;s<t.length;s++)i[t[s]]=e[s];return i}static filter(t,e){return Object.keys(t).filter((i=>!!e&&e(i,t[i]))).reduce(((e,i)=>(e[i]=t[i],e)),{})}static filter_NoFunc(t){return Te.filter(t,((t,e)=>"function"!=typeof e))}static filter_NoNone(t){return Te.filter(t,((t,e)=>null!=e))}static filter_NoFuncAndNone(t){return Te.filter(t,((t,e)=>null!=e&&"function"!=typeof e))}static filterAndAssign(t,e,i){const s=i||((t,e)=>null!=e&&"function"!=typeof e),r=Te.filter(t,s);return Object.assign(r,e)}static combineUint8Array(t){const e=t.reduce(((t,e)=>t+e.length),0),i=new Uint8Array(e);let s=0;return t.forEach((t=>{i.set(t,s),s+=t.length})),i}static lowByte(t){return 255&t}static highByte(t){return t>>>8&255}}Te.base64EncodeChars="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Te.base64DecodeChars=Uint8Array.from([255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,62,255,255,255,63,52,53,54,55,56,57,58,59,60,61,255,255,255,64,255,255,255,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,255,255,255,255,255,255,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,255,255,255,255,255]);class Oe{static createEmitter(t){return new Oe(t||"")}constructor(t){this.listeners=new Map,this.tagName=t}on(t,e){const i=e||Oe.EVENT_NAME;let s=this.listeners.get(i);return s?s.push(t):(s=[t],this.listeners.set(i,s)),t}off(t,e){const i=e||Oe.EVENT_NAME,s=this.listeners.get(i);if(s){const e=s.indexOf(t);if(e>=0)return s.splice(e,1),t}}clear(t){const e=this.listeners.get(t||Oe.EVENT_NAME);return e?e.splice(0):[]}emit(t,e,i){var s;const r=e||Oe.EVENT_NAME,n=(null===(s=this.listeners.get(r))||void 0===s?void 0:s.slice(0))||[];i?n.forEach((e=>e(t))):setTimeout((()=>{n.forEach((e=>e(t)))}))}invoke(t,e){this.emit(t,e,!0)}beginInvoke(t,e){this.emit(t,e,!1)}}Oe.EVENT_NAME="_";class Be{static getPackBytes(t){return t+(t>=192?5:4)}static toShort(t,e){return this.toNumber(t,e)}static toEBV(t,e){return e&&e>=192?this.toNumber(t,-193&e):255&t}static fromEBV(t){const e=[];return t>=192?(e.push(t>>>8|192),e.push(255&t)):e.push(t),e}static toNumber(t,e,i,s){let r=0;return t&&(r|=255&t),e&&(r|=(255&e)<<8),i&&(r|=(255&i)<<16),s&&(r|=(255&s)<<24),r}static getBytesFromNumber(t,e){const i=new Uint8Array(e);for(let s=0;s<e;s++)i[s]=t>>>8*(e-1-s)&255;return i}static getBytesFromShort(t,e){let i=[t>>>8&255,255&t];return e&&(i=t>=192?[t>>>8|192,255&t]:[255&t]),new Uint8Array(i)}static getBytesFromInt32(t){return this.getBytesFromNumber(t,4)}static calcCRC(t,e,i){let s=0;for(;e<i;++e)s+=255&t[e];return 255&~s}static parse(t){if(t.length<4||t[0]!=Be.DEVICE_TO_HOST_DATA_START)return;const e=t[1];let i=0,s=0;if(t[2]>=192){if(t.length<5)return;i=Be.toShort(t[3],t[2]),s=t[i+4];const r=Be.calcCRC(t,1,i+4);return s!==Be.FIXED_PACKAGE_CRC_RESULT&&s!==r&&u.warn(`---- CRC检验失败：receiveCRC = ${s}, calcCRC = ${r}`),i>0?new Be(e,t.slice(4,i+4)):new Be(e)}{if(i=t[2],t.length<i+4)return;s=t[i+3];const r=Be.calcCRC(t,1,i+3);if(s===Be.FIXED_PACKAGE_CRC_RESULT||s===r)return i>0?new Be(e,t.slice(3,i+3)):new Be(e);u.warn(`---- CRC校验失败：receiveCrc = ${s}, calcCrc = ${r}`)}}constructor(t,e){this.mOffset=0,this.mCmd=t,this.mData=e||[]}get Cmd(){return this.mCmd}get Data(){return this.mData}get Remains(){return this.mData.length-this.mOffset}pushByte(t){this.mData.push(255&t)}popByte(t){let e=t||0;return this.mOffset<this.mData.length&&(e=this.mData[this.mOffset++]),e}pushShort(t,e){e?t>=192?(this.mData.push(t>>>8|192),this.mData.push(255&t)):this.mData.push(255&t):(this.mData.push(t>>>8&255),this.mData.push(t>>>0&255))}popShort(t){let e=t||0;const i=this.mOffset;return i<=this.mData.length-2?(e=Be.toShort(this.mData[i+1],this.mData[i]),this.mOffset+=2):e=Be.toShort(this.mData[this.mOffset++]),e}pushEBV(t){return t>=192?(this.mData.push(t>>>8|192),this.mData.push(255&t),this.mOffset+=2,this.mOffset):(this.mData.push(t),this.mOffset++,this.mOffset)}popEBV(t){const e=this.mOffset;let i=t||0;return e<this.mData.length&&(this.mData[e]>=192&&this.mData.length>=e+2?(i=Be.toEBV(this.mData[e+1],this.mData[e]),this.mOffset+=2):i=this.mData[this.mOffset++]),i}pushInt(t){this.mData.push(t>>>24&255),this.mData.push(t>>>16&255),this.mData.push(t>>>8&255),this.mData.push(t>>>0&255)}popInt(t){let e=t||0;const i=this.mOffset;return i+4<=this.mData.length&&(e=Be.toNumber(this.mData[i+3],this.mData[i+2],this.mData[i+1],this.mData[i]),this.mOffset+=4),e}popInteger(t){return this.popInt(t)}popString(){const t=this.mOffset;if(t>=this.mData.length)return"";let e=t;for(;e<this.mData.length&&0!==this.mData[e];e++);this.mOffset=e;const i=this.mData.slice(t,e);return ve.decode(Uint8Array.from(i),"gbk")}popByteArray(){const t=this.mOffset;return t>=this.mData.length?new Uint8Array(0):(this.mOffset=this.mData.length,Uint8Array.from(this.Data.slice(t)))}getBufferLength(){return Be.getPackBytes(this.mData.length)}getBytes(){const t=this.mData||[],e=t.length,i=new Uint8Array(e+(e>=192?5:4));if(i[0]=Be.DEVICE_TO_HOST_DATA_START,i[1]=this.mCmd,e>=192){i[2]=e>>>8|192,i[3]=255&e;for(let s=0;s<e;s++)i[s+4]=t[s];i[e+4]=Be.FIXED_PACKAGE_CRC_RESULT}else{i[2]=e;for(let s=0;s<e;s++)i[s+3]=t[s];i[e+3]=Be.FIXED_PACKAGE_CRC_RESULT}return i}}Be.HOST_TO_DEVICE_DATA_START=31,Be.DEVICE_TO_HOST_DATA_START=31,Be.FIXED_PACKAGE_CRC_RESULT=136;class Me{get FreeSpace(){return this.mBuffer.length-this.mBufLen}get Length(){return this.mBufLen}constructor(t){this.mBufLen=0;const e=t&&t>Me.BUFFER_LENGTH_DEFAULT?t:Me.BUFFER_LENGTH_DEFAULT;this.mBuffer=new Uint8Array(e)}static getBytes(t,e){return new Be(t,e).getBytes()}pushPackage(t,e){const i=Me.getBytes(t,e);return this.push(i)}pushByte(t,e){const i=Me.getBytes(t,[255&e]);return this.push(i)}pushShort(t,e,i){const s=Be.getBytesFromShort(e,i);return this.push(Me.getBytes(t,Array.from(s)))}pushInt(t,e){const i=Be.getBytesFromInt32(e);return this.push(Me.getBytes(t,Array.from(i)))}push(t,e,i){const s=e||0,r=i||t.length;if(r-s>this.mBuffer.length-this.mBufLen)return u.warn("---- PackageBuffer缓存不够！"),!1;for(let e=s;e<r;e++)this.mBuffer[this.mBufLen++]=t[e];return!0}getAllBytes(){return this.mBuffer.slice(0,this.mBufLen)}clearBuffer(){this.mBufLen=0,this.mBuffer.fill(0)}toString(){return`{size: ${this.mBuffer.length}, mBufLen: ${this.mBufLen}, buffer: [${Te.getHexStringOfBytes(this.mBuffer)}]}`}}Me.BUFFER_LENGTH_DEFAULT=1e3;class Le{constructor(){this.mBufferList=[]}get BufferList(){return this.mBufferList}get Length(){return this.mBufferList.length}reset(){this.mBufferList.splice(0)}toList(){return this.mBufferList.slice(0)}push(t,e,i){const s=e||0,r=i||t.length,n=r-s;let a=this.mBufferList[this.mBufferList.length-1];(!a||a.FreeSpace<n)&&(a=new Me(n),this.mBufferList.push(a)),a.push(t,s,r)}push2(t,e,i,s,r,n){const a=new Me;a.push(t,e,i),a.push(s,r,n),this.push(a.getAllBytes())}pushPackage(t,e){const i=Me.getBytes(t,e);return this.push(i)}}const Ne=[1,2,3,4,5,6,7,8,9,10,11,12,24,36,48,120],Fe=[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,41,62,83,104,125,146,167,188,209,230,461,923];class je{static appendRLEC(t,e,i,s,r){for(;s>=63;s-=63){if(e.value+2>r)return!1;t[e.value]=255,++e.value,t[e.value]=i,++e.value}switch(s){case 1:if(i>192){if(e.value+2>r)return!1;t[e.value]=193,++e.value,t[e.value]=i,++e.value}else{if(e.value+1>r)return!1;t[e.value]=i,++e.value}break;case 2:if(e.value+2>r)return!1;i>192?(t[e.value]=194,++e.value,t[e.value]=i,++e.value):(t[e.value]=i,++e.value,t[e.value]=i,++e.value);break;default:if(s>0){if(e.value+2>r)return!1;t[e.value]=192|s,++e.value,t[e.value]=i,++e.value}}return!0}static calcRLEC(t,e,i,s){if(e<=0)return 0;const r={value:0};let n=t[0],a=1;for(let o=1;o<e;++o)if(t[o]===n)++a;else{if(!this.appendRLEC(i,r,n,a,s))return 0;n=t[o],a=1}return this.appendRLEC(i,r,n,a,s)?r.value:0}static appendRLE5(t,e,i,s,r){if(s<=0)return!0;let n=Math.floor(5*e.value/8),a=15;for(;s>0;)if(s>=Ne[a]){s-=Ne[a];const o=a|(i?16:0);if(e.value=e.value+1,5*e.value>8*r)return!1;switch(7&e.value){case 0:t[n]=t[n]|o,++n;break;case 1:t[n]=t[n]|o<<3;break;case 2:t[n]=t[n]|o>>>2,++n,t[n]=t[n]|(3&o)<<6;break;case 3:t[n]=t[n]|o<<1;break;case 4:t[n]=t[n]|o>>>4,++n,t[n]=t[n]|(15&o)<<4;break;case 5:t[n]=t[n]|o>>>1,++n,t[n]=t[n]|(1&o)<<7;break;case 6:t[n]=t[n]|o<<2;break;case 7:t[n]=t[n]|o>>>3,++n,t[n]=t[n]|(7&o)<<5}}else s<=12?a=s-1:--a;return!0}static calcRLE5X(t,e,i,s){if(e<=0)return 0;let r=0,n=0,a=!1,o=128;const h={value:0};for(;;){if(t[n]&o)if(a)++r;else{if(!this.appendRLE5(i,h,!1,r,s))return 0;a=!0,r=1}else if(a){if(!this.appendRLE5(i,h,!0,r,s))return 0;a=!1,r=1}else++r;if(1===o){if(++n,n>=e)break;o=128}else o>>>=1}return a&&!this.appendRLE5(i,h,!0,r,s)?0:h.value}static calcRLE5D(t,e,i,s,r,n){let a=0,o=0,h=!1,c=128;const l={value:0},d=Math.min(e,s);if(d>0)for(;;){if((i[o]&c)!=(t[o]&c))if(h)++a;else{if(!this.appendRLE5(r,l,!1,a,n))return 0;h=!0,a=1}else if(h){if(!this.appendRLE5(r,l,!0,a,n))return 0;h=!1,a=1}else++a;if(1===c){if(++o,o>=d)break;c=128}else c>>>=1}if(e!==s)for(e<s&&(t=i,e=s),c=128;;){if(t[o]&c)if(h)++a;else{if(!this.appendRLE5(r,l,!1,a,n))return 0;h=!0,a=1}else if(h){if(!this.appendRLE5(r,l,!0,a,n))return 0;h=!1,a=1}else++a;if(1===c){if(++o,o>=e)break;c=128}else c>>>=1}return h&&!this.appendRLE5(r,l,!0,a,n)?0:l.value}static appendRLE6(t,e,i,s,r){if(s<=0)return!0;let n=Math.floor(6*e.value/8),a=31;for(;s>0;)if(s>=Fe[a]){s-=Fe[a];const o=a|(i?32:0);if(e.value=e.value+1,6*e.value>8*r)return!1;switch(3&e.value){case 0:t[n]=t[n]|o,++n;break;case 1:t[n]=t[n]|o<<2;break;case 2:t[n]=t[n]|o>>>4,++n,t[n]=t[n]|(15&o)<<4;break;case 3:t[n]=t[n]|o>>>2,++n,t[n]=t[n]|(3&o)<<6}}else s<=20?a=s-1:--a;return!0}static calcRLE6X(t,e,i,s){if(e<=0)return 0;let r=0,n=0,a=!1,o=128;const h={value:0};for(;;){if(t[n]&o)if(a)++r;else{if(!this.appendRLE6(i,h,!1,r,s))return 0;a=!0,r=1}else if(a){if(!this.appendRLE6(i,h,!0,r,s))return 0;a=!1,r=1}else++r;if(1===o){if(++n,n>=e)break;o=128}else o>>>=1}return a&&!this.appendRLE6(i,h,!0,r,s)?0:h.value}static calcRLE6D(t,e,i,s,r,n){let a=0,o=0,h=!1,c=128;const l={value:0},d=Math.min(e,s);if(d>0)for(;;){if((i[o]&c)!=(t[o]&c))if(h)++a;else{if(!this.appendRLE6(r,l,!1,a,n))return 0;h=!0,a=1}else if(h){if(!this.appendRLE6(r,l,!0,a,n))return 0;h=!1,a=1}else++a;if(1===c){if(++o,o>=d)break;c=128}else c>>>=1}if(e!==s)for(e<s&&(t=i,e=s),c=128;;){if(t[o]&c)if(h)++a;else{if(!this.appendRLE6(r,l,!1,a,n))return 0;h=!0,a=1}else if(h){if(!this.appendRLE6(r,l,!0,a,n))return 0;h=!1,a=1}else++a;if(1===c){if(++o,o>=e)break;c=128}else c>>>=1}return h&&!this.appendRLE6(r,l,!0,a,n)?0:l.value}}const Ue=1536,We=[12];class $e{}$e.CMD_NULL=0,$e.CMD_IS_PRINTABLE=112,$e.DZIP_PRINTABLE=0,$e.DZIP_ISPRINTING=1,$e.DZIP_ISROTATING=2,$e.DZIP_NOJOB=10,$e.DZIP_PAGENOTREADY=11,$e.DZIP_JOBCANCELED=12,$e.DZIP_ENVNOTREADY=20,$e.DZIP_VOLTOOLOW=30,$e.DZIP_VOLTOOHIGH=31,$e.DZIP_TPHNOTFOUND=32,$e.DZIP_TPHTOOHOT=33,$e.DZIP_COVEROPENED=34,$e.DZIP_NO_PAPER=35,$e.DZIP_RIBBONCANOPENED=36,$e.DZIP_NO_RIBBON=37,$e.DZIP_UNMATCHED_RIBBON=38,$e.DZIP_TPHTOOCOLD=39,$e.DZIP_USEDUP_RIBBON=40,$e.DZIP_USEDUP_RIBBON2=41,$e.DZIP_NO_LABEL=42,$e.DZIP_UNMATCHED_LABEL=43,$e.DZIP_USEDUP_LABEL=44,$e.DZIP_NO_RIBBON2=45,$e.DZIP_UNMATCHED_RIBBON2=46,$e.DZIP_LABELCANOPENED=50,$e.DZCT_ANDROID_APP=0,$e.DZCT_ANDROID_BLE=6,$e.DZCT_ANDIOS_DJGW=12,$e.DZCT_ANDROID_USB=16,$e.CMD_ENABLE_SETTING=128,$e.CMD_DEVICE_TYPE=120,$e.CMD_DEVICE_NAME=121,$e.CMD_DEVICE_VERSION=122,$e.CMD_SOFTWARE_VERSION=124,$e.CMD_PRINTER_DPI=113,$e.CMD_PRINTER_WIDTH=114,$e.CMD_MANUFACTURER=117,$e.CMD_BUFFER_STATE=118,$e.CMD_BUFFER_SIZE=119,$e.CMD_PAGE_START=32,$e.CMD_PAGE_PRINT=33,$e.CMD_PAGE_LINE=34,$e.CMD_PAGE_PARAM=37,$e.CMD_PAGE_HEIGHT=38,$e.CMD_PAGE_WIDTH=39,$e.CMD_PAGE_END=40,$e.CMD_PAGE_CONTROL=35,$e.CMD_DARKNESS=67,$e.CMD_SPEED=68,$e.CMD_GAP_TYPE=66,$e.CMD_GAP_LEN=69,$e.CMD_MOTORMODE=71,$e.CMD_AUTOPOWEROFF=72,$e.CMD_LANGUAGE=73,$e.CMD_CAP_GAPTYPE=82,$e.CMD_CAP_MOTORMODE=87,$e.CMD_CAP_LANGUAGE=89,$e.CMD_DEVICE_DMINFO=125,$e.CMD_SET_GENFLAGS=77,$e.CMD_COMMIT_PARAM=79,$e.CMD_DEV_DISCOVERY=90,$e.CMD_ADDRESS_READ=97,$e.CMD_PERIPHERALFLAGS=131,$e.CMD_PERIPHERALTYPE_FLAGS=1,$e.CMD_PERIPHERALTYPE_SPISPEED=2,$e.CMD_HARDWARE_FLAGS=132,$e.CMD_REQ_ADCVALUE=136,$e.CMD_DEV_HANDSHAKE=158,$e.CMD_MANU_TOOLKIT=159,$e.CMD_MCU_GETID=10,$e.CMD_DEBUG_BUFFER=127,$e.CMD_PRINT_COUNTER=115,$e.CMD_MANUSHIPTIME=17,$e.CMD_SUB_ROM_UPGRADE=41,$e.CMD_BITMAP_P_RLEC=41,$e.CMD_BITMAP_PRINT=43,$e.CMD_BITMAP_P_RLEX=44,$e.CMD_BITMAP_P_RLED=45,$e.CMD_BITMAP_P_RLE6X=60,$e.CMD_BITMAP_P_RLE6D=61,$e.CMD_BITMAP_REPEAT=46,$e.PCPDHF_UHFRFID_WRITOR=2,$e.PCPDHF_HFRFID_WRITOR=4,$e.PCPDHF_NFCRFID_WRITOR=6,$e.PCPDHF_MSKRFID_WRITOR=6,$e.PCPDHF_HAS_BEEP=16384,$e.PCPDHF_SUPER_BITMAP=16,$e.PCPDHF_GRAY_BITMAP=32,$e.PCPDHF_BLUETOOTH_2=131072,$e.PCPDSF_NO_AUTO_OUT=268435456,$e.PCPDSF_MOTOR_ANTIDIR=1,$e.PCPDSF_RLE5_BITMAP=16,$e.PCPDSF_RLE6_BITMAP=32,$e.PCPDSF_RLEC_BITMAP=128,$e.PCPDSF_MASK_BITMAP=240,$e.PCPDSF_BT_HARD_FC=256,$e.PCPDSF_PRTA_RIGHT=0,$e.PCPDSF_PRTA_CENTER=512,$e.PCPDSF_PRTA_LEFT=1024,$e.PCPDSF_PRTA_MASK=1536,$e.PCPDSF_ROTATE_180=16384,$e.PCPDAF_BT_HARD_FC=1,$e.PCPDAF_STRING_GBK=2,$e.PCPDAF_HAS_WIFI=4096,$e.PCPDAF_RLEC_BITMAP=128;class ke{constructor(){this.mThreshold=ke.THRESHOLD_DEFAULT,this.mOrientation=0,this.mLineAction=0,this.mSoftwareFlags=16,this.mHardwareFlags=0,this.mSupportSuperBitmap=!0,this.mPrinterWidth=0,this.mLineCount=0,this.mLineBytes=0,this.mBufferList=new Le,this.mByteWidth=0,this.mPrevBytes=0,this.mSumLines=0,this.mSumPrints=0,this.mSumRLE5Xs=0,this.mSumRLE5Ds=0,this.mSumRLE6Xs=0,this.mSumRLE6Ds=0,this.mSumRLE_Cs=0,this.mSumRepeats=0,this.mRLE5XSaved=0,this.mRLE5DSaved=0,this.mRLE6XSaved=0,this.mRLE6DSaved=0,this.mRLECSaved=0}static encodeImageData(t,e,i){this.instance||(this.instance=new ke);const s=i||{},r=Object.assign(Object.assign(Object.assign({},s),t),{gapType:"number"==typeof t.gapType?t.gapType:s.gapType,gapLength:t.gapLength||0,printerDPI:s.printerDPI||t.printerDPI,printerWidth:s.printerWidth||t.printerWidth,softwareFlags:s.softwareFlags||t.softwareFlags,hardwareFlags:s.hardwareFlags||t.hardwareFlags}),n=this.instance.print(r,e);return n?n.map((t=>t.getAllBytes())):[]}static updatePageKey(t,e){const i=t[0];if(i&&!(i.length<=0)&&31===i[0]&&i[1]===$e.CMD_PAGE_START&&2===i[2]){const t=Be.toShort(i[4],i[3]);if(t!==e){const s=Be.getBytesFromShort(e||0,!1);i[3]=s[0],i[4]=s[1],u.log(`---- updatePageKey: from -> to [0x${t.toString(16)}] => [0x${e.toString(16)}]`)}}}get IsLandscape(){return 0===this.mOrientation||2===this.mOrientation}print(t,e){return this.start(t,e)?(u.info("---- start to image encode:"),this.encode(t),u.info("---- stop to image encode:"),this.end(t)):null}reset(t){const e=t||{},i=e;"number"!=typeof e.printSpeed&&"number"==typeof i.speed&&(e.printSpeed=i.speed),e.orientation="number"==typeof e.orientation?e.orientation:0,e.orientation>=360&&(e.orientation=e.orientation%360),e.orientation>3&&(e.orientation=Math.floor(e.orientation/90)),this.mOrientation=e.orientation,this.mPrinterWidth=e.printerWidth&&e.printerWidth>0?e.printerWidth:ke.PRINTER_WIDTH_DEFAULT,"number"==typeof e.darkness&&(e.printDarkness=e.darkness),"number"==typeof e.speed&&(e.printSpeed=e.speed),e.threshold&&e.threshold>0&&e.threshold<255?this.mThreshold=e.threshold:this.mThreshold=ke.THRESHOLD_DEFAULT,this.mBufferList.reset(),this.mSoftwareFlags="number"==typeof e.softwareFlags?e.softwareFlags:16,this.mHardwareFlags=e.hardwareFlags||0,this.mSupportSuperBitmap="boolean"!=typeof e.enableSuperBitmap||e.enableSuperBitmap,this.mLineAction=1,this.mByteWidth=Math.floor((this.mPrinterWidth+7)/8),this.mLineCount=e.marginTop&&e.marginTop>0?e.marginTop:0,this.mLineBytes=0,this.mLineData=new Uint8Array(0),this.mPrevBytes=0,this.mPrevData=new Uint8Array(0),this.mSumLines=0,this.mSumPrints=0,this.mSumRLE5Xs=0,this.mSumRLE5Ds=0,this.mSumRLE6Xs=0,this.mSumRLE6Ds=0,this.mSumRLE_Cs=0,this.mSumRepeats=0,this.mRLE5XSaved=0,this.mRLE5DSaved=0,this.mRLE6XSaved=0,this.mRLE6DSaved=0,this.mRLECSaved=0}start(t,e){const i=new Me,s=t.imageData;if(!(s&&s.width&&s.height&&s.data))return!1;this.reset(t),u.info(`========== startPage pageKey: ${e}[0x${null==e?void 0:e.toString(16)}] ==========`),u.info(`---- width             : ${s.width}`),u.info(`---- height            : ${s.height}`),u.info(`---- orientation       : ${this.mOrientation}`),u.info(`---- printerDPI        : ${t.printerDPI}`),u.info(`---- printerWidth      : ${t.printerWidth}`),u.info(`---- gapType           : ${t.gapType}`),u.info(`---- printDarkness     : ${t.printDarkness}`),u.info(`---- printSpeed        : ${t.printSpeed}`),u.info(`---- gapLength         : ${t.gapLength}`),u.info(`---- printAlignment    : ${t.printAlignment}`),u.info(`---- threshold         : ${t.threshold}`),u.info(`---- mSoftwareFlags    : ${Te.toHexString(this.mSoftwareFlags,"0x")}`),u.info(`---- mHardwareFlags    : ${Te.toHexString(this.mHardwareFlags,"0x")}`),u.info(`---- supportSuperBitmap: ${this.mSupportSuperBitmap}`),u.info("=================================================");const r=this.IsLandscape?s.width:s.height,n=Be.getBytesFromShort(e||0,!1);i.pushPackage($e.CMD_PAGE_START,Array.from(n));const a=Be.fromEBV(Math.floor((r+7)/8));i.pushPackage($e.CMD_PAGE_WIDTH,a);const o="number"==typeof t.gapType?t.gapType:-1;o>=De.None&&o<=De.Trans&&i.pushPackage($e.CMD_GAP_TYPE,[o]);const h="number"==typeof t.gapLength?t.gapLength:0;if(h>0&&o>0&&o<=4){let t=Math.floor(100*h);t>ke.MAX_EBV_VALUE&&(t=ke.MAX_EBV_VALUE),i.pushPackage($e.CMD_GAP_LEN,Be.fromEBV(t))}const c="number"==typeof t.printDarkness?t.printDarkness:-1;c>=_e.Min&&c<=_e.Max&&i.pushPackage($e.CMD_DARKNESS,[c-1]);const l="number"==typeof t.printSpeed?t.printSpeed:-1;return l>=Se.Min&&l<=Se.Max&&i.pushPackage($e.CMD_SPEED,[l-1]),this.mBufferList.push(i.getAllBytes()),!0}static getImageGrayValue(t,e){return e+3<t.length&&t[e+3]>0?.3*t[e]+.59*t[e+1]+.11*t[e+2]:255}getPrintAlignment(){return this.mSoftwareFlags&$e.PCPDSF_PRTA_MASK}encode(t){const e=t.imageData,i=e.width,s=e.height,r=this.IsLandscape?Math.min(i,this.mPrinterWidth):Math.min(s,this.mPrinterWidth);this.mByteWidth=Math.floor((r+7)/8);const n=this.mThreshold;let a=0,o=0;const h=t.printAlignment||this.getPrintAlignment();let c=0;if(1===this.mOrientation||3===this.mOrientation?s>r&&(c=512==(h&Ue)?Math.floor(.5*(s-r)):1024==(h&Ue)?0:s-r):i>r&&(c=512==(h&Ue)?Math.floor(.5*(i-r)):1024==(h&Ue)?0:i-r),1===this.mOrientation)for(let t=0;t<i;++t){const h=new Uint8Array(this.mByteWidth);let l=128,d=0;for(let u=0;u<r;++u)a=4*((s-u-c-1)*i+t),o=ke.getImageGrayValue(e.data,a),o<=n&&(h[d]=h[d]|l),1===l?(l=128,++d):l>>>=1;this.printRow(h)}else if(2===this.mOrientation)for(let t=0;t<s;++t){const h=new Uint8Array(this.mByteWidth);let l=128,d=0;a=4*((s-t)*i-c-1);for(let t=0;t<r;++t,a-=4)o=ke.getImageGrayValue(e.data,a),o<=n&&(h[d]=h[d]|l),1===l?(l=128,++d):l>>>=1;this.printRow(h)}else if(3===this.mOrientation)for(let t=0;t<i;++t){const s=new Uint8Array(this.mByteWidth);let h=128,l=0;for(let d=0;d<r;++d)a=4*((d+c)*i+(i-t-1)),o=ke.getImageGrayValue(e.data,a),o<=n&&(s[l]=s[l]|h),1===h?(h=128,++l):h>>>=1;this.printRow(s)}else for(let t=0;t<s;++t){const s=new Uint8Array(this.mByteWidth);a=4*(i*t+c);let h=128,l=0;for(let t=0;t<r;++t,a+=4)o=ke.getImageGrayValue(e.data,a),o<=n&&(s[l]=s[l]|h),1===h?(h=128,++l):h>>>=1;this.printRow(s)}}end(t){const e=t.marginBottom||0;switch(this.mLineAction){case 1:this.pushLine(this.mLineCount+e);break;case 2:this.pushPrint(),this.pushLine(e);break;default:return null}return this.mLineAction=0,this.mBufferList.push(We),this.mBufferList.toList()}printRow(t){let e=t.length-1;for(;e>=0&&0===t[e];--e);if(e<0)return this.printLine(1);switch(++e,this.mLineAction){case 1:this.pushLine(this.mLineCount);break;case 2:if(this.mLineBytes===e&&this.checkArrayEquals(this.mLineData,t,e))return this.mLineCount+=1,!0;this.pushPrint();break;default:return!1}return this.mLineData=t,this.mLineBytes=e,this.mLineCount=1,this.mLineAction=2,!0}printLine(t){switch(this.mLineAction){case 1:return this.mLineCount+=t,!0;case 2:this.pushPrint();break;default:return!1}return this.mLineData=new Uint8Array,this.mLineBytes=0,this.mLineCount=t,this.mLineAction=1,!0}pushLine(t){if(t<=0)return;this.mSumLines+=t,this.mPrevData=Uint8Array.from([]),this.mPrevBytes=0;const e=[27,74,255];for(;t>=255;t-=255)this.mBufferList.push(e);t>0&&this.mBufferList.push([27,74,t])}pushPrint(){if(this.mLineCount<=0)return;let t=0;for(;t<this.mLineBytes&&0===this.mLineData[t];++t);const e=this.mLineBytes-t;let i=new Uint8Array(0),s=new Uint8Array(0),r=new Uint8Array(0),n=new Uint8Array(0),a=new Uint8Array(0),o=0,h=0,c=0,l=0,d=0;this.mSupportSuperBitmap&&(128&this.mSoftwareFlags&&(i=new Uint8Array(this.mByteWidth+4),o=je.calcRLEC(this.mLineData,this.mLineBytes,i,this.mByteWidth)),16&this.mSoftwareFlags&&(s=new Uint8Array(this.mByteWidth+4),h=je.calcRLE5X(this.mLineData,this.mLineBytes,s,this.mByteWidth)),16&this.mSoftwareFlags&&this.mPrevData.length>0&&(r=new Uint8Array(this.mByteWidth+4),c=je.calcRLE5D(this.mPrevData,this.mPrevBytes,this.mLineData,this.mLineBytes,r,this.mByteWidth)),32&this.mSoftwareFlags&&(n=new Uint8Array(this.mByteWidth+4),l=je.calcRLE6X(this.mLineData,this.mLineBytes,n,this.mByteWidth)),32&this.mSoftwareFlags&&this.mPrevData.length>0&&(a=new Uint8Array(this.mByteWidth+4),d=je.calcRLE6D(this.mPrevData,this.mPrevBytes,this.mLineData,this.mLineBytes,a,this.mByteWidth)));const u=(t>=192?4:3)+(e>=192?2:1)+e,g=o<=0?this.mByteWidth+100:o+(o>=192?4:3),p=h<=0?this.mByteWidth+100:Math.ceil(5*h/8)+(h>=192?4:3),m=c<=0?this.mByteWidth+100:Math.ceil(5*c/8)+(c>=192?4:3),f=l<=0?this.mByteWidth+100:Math.ceil(6*l/8)+(l>=192?4:3),C=d<=0?this.mByteWidth+100:Math.ceil(6*d/8)+(d>=192?4:3);if(m<u&&m<g&&m<p&&m<f&&m<=C)this.mSumRLE5Ds+=1,this.mRLE5DSaved+=u-m,this.pushRLE5(45,r,c);else if(C<u&&C<g&&C<p&&C<f)this.mSumRLE6Ds+=1,this.mRLE6DSaved+=u-C,this.pushRLE6(61,a,d);else if(p<u&&p<g&&p<=f)this.mSumRLE5Xs+=1,this.mRLE5XSaved+=u-p,this.pushRLE5(44,s,h);else if(f<u&&f<g)this.mSumRLE6Xs+=1,this.mRLE6XSaved+=u-f,this.pushRLE6(60,n,l);else if(g<u)this.mSumRLE_Cs+=1,this.mRLECSaved+=u-g,this.pushRLEC(41,i,o);else{this.mSumPrints+=1;const i=[Be.HOST_TO_DEVICE_DATA_START,$e.CMD_BITMAP_PRINT,0,0];let s=this.pushEBV(i,2,t);s=this.pushEBV(i,s,e),this.mBufferList.push2(i,0,s,this.mLineData,t,this.mLineBytes)}this.mLineCount>1&&this.pushRepeat(this.mLineCount-1),this.mPrevData=this.mLineData,this.mPrevBytes=this.mLineBytes}pushRepeat(t){if(t<=0)return;this.mSumRepeats+=t;let e=[Be.HOST_TO_DEVICE_DATA_START,46,0];for(this.pushEBV(e,2,16383);t>16383;t-=16384)this.mBufferList.push(e);t>0&&(e=[Be.HOST_TO_DEVICE_DATA_START,46,0],this.pushEBV(e,2,t-1),this.mBufferList.push(e))}pushEBV(t,e,i){return i>=192?(t[e+0]=i>>>8|192,t[e+1]=255&i,e+2):(t[e+0]=i,e+1)}pushRLEC(t,e,i){if(i<=0)return;const s=[Be.HOST_TO_DEVICE_DATA_START,t,0],r=this.pushEBV(s,2,i);this.mBufferList.push2(s,0,r,e,0,i)}pushRLE5(t,e,i){if(i<=0)return;const s=[Be.HOST_TO_DEVICE_DATA_START,t,0],r=this.pushEBV(s,2,i),n=Math.ceil(5*i/8);this.mBufferList.push2(s,0,r,e,0,n)}pushRLE6(t,e,i){if(i<=0)return;const s=[Be.HOST_TO_DEVICE_DATA_START,t,0],r=this.pushEBV(s,2,i),n=Math.ceil(6*i/8);this.mBufferList.push2(s,0,r,e,0,n)}checkArrayEquals(t,e,i){for(let s=0;s<i;++s)if(t[s]!==e[s])return!1;return!0}}ke.MAX_EBV_VALUE=16383,ke.THRESHOLD_DEFAULT=128,ke.PRINTER_DPI_DEFAULT=203,ke.PRINTER_WIDTH_DEFAULT=384;class He{static createInstance(t){const e=t?new Uint8Array(t):void 0;if(e&&!(e.length<14))return 53===e[0]&&51===e[1]?new He(e):void 0}static uuidToString(t,e){if(!t)return"";const i=[];e&&i.push("0x");for(let e=t.length-1;e>=0;--e)i.push(Te.toHexByteString(t[e]));return i.join("")}get deviceMainType(){return this.data[2]}get suggestedMtuI(){return 20*this.data[3]}get suggestedWaitI(){return this.data[4]}get suggestedMtuA(){return 20*this.data[5]}get suggestedWaitA(){return this.data[6]}get suggestedUUID(){return He.uuidToString(this.data.slice(7,11))}get locateArea(){const t=this.data[11];return t>=P.A&&t<=P.Z?String.fromCharCode(t):""}get deviceFlags(){return Be.toShort(this.data[12],this.data[13])}constructor(t){this.data=new Uint8Array(t)}toString(){return`[${Te.getHexStringOfBytes(this.data,",")}]`}}class Je{constructor(t,e){this.mCmd=t,this.mTimeout=e||2e3}stop(){this.mTimer&&(clearTimeout(this.mTimer),this.mTimer=void 0)}start(t){this.mResponseAction=t,this.mTimer=setTimeout((()=>{this.invoke(void 0)}),this.mTimeout),u.log(`start request command, [TM: ${this.mTimer}, cmd: 0x${Te.toHexByteString(this.mCmd)}]`)}invoke(t){u.log(`invoke request command, [TM: ${this.mTimer}, cmd: 0x${Te.toHexByteString(this.mCmd)}], value = ${t?t.Data.join(","):"timeout"}`),this.stop(),this.mResponseAction&&this.mResponseAction(t)}}var Ge,Ve,Ke;!function(t){t[t.None=0]="None",t[t.Connecting=1]="Connecting",t[t.Connected=2]="Connected",t[t.Checking=3]="Checking",t[t.ReadyPrint=4]="ReadyPrint",t[t.Sending=5]="Sending",t[t.Printing=6]="Printing",t[t.Paused=7]="Paused",t[t.Stopped=8]="Stopped",t[t.Abort=9]="Abort"}(Ge||(Ge={}));class ze{static isSupportedService(t){if(!t)return!1;if(0===(t=this.getFullUUID(t)).indexOf("F000FFC0"))return!1;const e=/^0000([0-9A-F]{4})-0000-1000-8000-00805F9B34FB$/i.exec(t);if(e){const t=e[1];if(t>="0000"&&t<="2AFF")return!1;if("FF10"===t)return!1;if("FFC0"===t)return!1}else if(this.isMatchedUUID(t,"E7810A71-73AE-499D-8C15-FAA9AEF0C3F2"))return!1;return!0}static getFullUUID(t){return 4===t.length?`0000${t}-0000-1000-8000-00805F9B34FB`.toUpperCase():8===t.length?`${t}-0000-1000-8000-00805F9B34FB`.toUpperCase():t.toUpperCase()}static isMatchedUUID(t,e){return!(!t||!e)&&(t.length===e.length?t.toUpperCase()===e.toUpperCase():this.getFullUUID(t)===this.getFullUUID(e))}static isDeviceInfoService(t){return 0===this.getFullUUID(t).indexOf("0000180A")}static isDeviceInfoCharacteristic(t){return 0===this.getFullUUID(t).indexOf("00002A24")}static nextPageKey(t){return++t>=65535&&(t=1),t}static test(){}constructor(t){this.mRequestQueue=new Map,this.mPrinterInfo={printerDPI:0,printerWidth:0,hardwareFlags:0,softwareFlags:$e.PCPDSF_RLE5_BITMAP,softwareVersion:"",deviceName:"",deviceVersion:""},this.mRequestMTUs=0,this.mWriteWaitMSs=0,this.mExtraWriteMSs=0,this.mDFType="",this.mInitMTUs=0,this.mInitWriteWaits=0,this.mReadBuffer=[],this.mPackageStartRecords=[],this.mPrintJobList=[],this.mPausedPageList=[],this.mPrintingMap=new Map,this.mTempPageMap=new Map,this.mPrintPageKeyCheckTimer=0,this.mJobId=0,this.mIsPrPageKey=-1,this.mPageKey=Math.floor(255*Math.random()),this.mBufferSize=0,this.mPrintable=0,this.mIsCheckingPrintable=!1,this.mMcuId="",this.mClientType=0,this.mEnableMcuId=!1,this.mPageKeyEnable=!1,this.mDeviceNameStage=0,this.mPrintStatus=Ge.None,this.mPrintStatusChanged=Oe.createEmitter("PrintStatusChanged"),this.checkIndex=0,this.Adapter=t}get Adapter(){return this.mBleAdapter}set Adapter(t){t&&t!==this.mBleAdapter&&(this.mBleAdapter=t)}get PrinterDPI(){return this.mPrinterInfo.printerDPI&&this.mPrinterInfo.printerDPI>0?this.mPrinterInfo.printerDPI:ke.PRINTER_DPI_DEFAULT}get PrinterWidth(){return this.mPrinterInfo.printerWidth&&this.mPrinterInfo.printerWidth>0?this.mPrinterInfo.printerWidth:ke.PRINTER_WIDTH_DEFAULT}get Printable(){return this.mPrintable}get ConnectStatus(){return this.mPrintStatus}get IsConnected(){return this.mPrintStatus>Ge.Checking}get ClientType(){return this.mClientType}set ClientType(t){this.mClientType=t}get InitMTUs(){return this.mInitMTUs}set InitMTUs(t){this.mInitMTUs=t}get InitWriteWaits(){return this.mInitWriteWaits}set InitWriteWaits(t){this.mInitWriteWaits=t}get ExtraWriteTimes(){return this.mExtraWriteMSs}set ExtraWriteTimes(t){t>=-30&&t<=30&&(this.mExtraWriteMSs=t)}get CurrentDevice(){return this.mDevice}get isPrinterAvailable(){return this.mPrintable>=0&&this.mPrintable<=$e.DZIP_PAGENOTREADY}get EnableMcuId(){return this.mEnableMcuId}set EnableMcuId(t){this.mEnableMcuId=t}get isPageKeyEnable(){return this.mPageKeyEnable}set isPageKeyEnable(t){this.mPageKeyEnable=t}get isPageKeyValid(){return this.mPageKeyEnable&&this.mIsPrPageKey>=0}quit(){return this.Adapter.closeBleAdapter()}parseMTUInfo(t,e){const i=Te.isIOS();if(this.mRequestMTUs=123,this.mWriteWaitMSs=i?20:10,this.mDFType="",e){const t=new Uint8Array(e),s=new Be($e.CMD_NULL,Array.from(t)).popString().toUpperCase();u.info(`---- prepareRequestMTU: ${s}, isIOS = ${i}`);const r=s.lastIndexOf("-");this.mDFType=r>=0?s.substring(r+1):""}if(t)return u.info("---- get mtu info from factory info!"),Te.isIOS()?(this.mRequestMTUs=t.suggestedMtuI+3,this.mWriteWaitMSs=t.suggestedWaitI):(this.mRequestMTUs=t.suggestedMtuA+3,this.mWriteWaitMSs=t.suggestedWaitA),!0;if(this.mDFType){if("MT_DF1"===this.mDFType)this.mRequestMTUs=123,this.mWriteWaitMSs=i?18:15;else{const t=/^DF(\d+)$/.exec(this.mDFType);if(t){const e=parseInt(t[1]);5&~e?4&e?(this.mRequestMTUs=183,this.mWriteWaitMSs=i?10:3):2&e?(this.mRequestMTUs=123,this.mWriteWaitMSs=i?20:10):1&e&&(this.mRequestMTUs=183,this.mWriteWaitMSs=i?18:15):(this.mRequestMTUs=503,this.mWriteWaitMSs=i?5:3)}}return!0}return this.mInitMTUs>0&&(this.mRequestMTUs=this.mInitMTUs),this.mInitWriteWaits>0&&(this.mWriteWaitMSs=this.mInitWriteWaits),u.info("---- prepareRequestMTU: 未读取到DF信息。"),!1}dispatcherRequest(t){const e=t.Cmd,i=this.mRequestQueue.get(e);i&&(this.mRequestQueue.delete(e),i.invoke(t))}enqueueRequest(t,e,i){const s=new Je(t,i);this.mRequestQueue.set(t,s),s.start((t=>{e&&e(t)}))}raisePrintStatusChanged(t){u.info(`◆◆◆ PrintStatusChanged: ${Ge[this.mPrintStatus]} --\x3e ${Ge[t]} ◆◆◆`),this.mPrintStatus=t,this.mPrintStatusChanged.emit(t)}parseManuToolkit(t,e){switch(e){case $e.CMD_SUB_ROM_UPGRADE:break;case $e.CMD_MANUSHIPTIME:{const e=t.popByteArray(),i=ve.decode(e);u.info(`★★★★★★ CMD_MANU_TOOLKIT.manuShipTime: ${i} ★★★★★★`);break}case $e.CMD_MCU_GETID:{const e=t.popByteArray();this.mMcuId=Te.getHexStringOfBytes(e,"").toUpperCase(),u.info(`★★★★★★ CMD_MANU_TOOLKIT.McuId: ${this.mMcuId} ★★★★★★`);break}case $e.CMD_ADDRESS_READ:{const e=t.popByteArray();u.info(`★★★★★★ CMD_MANU_TOOLKIT.addressRead: [${Te.getHexStringOfBytes(e)}] ★★★★★★`)}}}onReadPackage(t){switch(u.info(`========== DataPackage.onReadPackage: cmd = 【0x${Te.toHexByteString(t.Cmd)}】`),u.info(`data: [${Te.getHexStringOfBytes(t.Data)}]`),t.Cmd){case $e.CMD_DEVICE_TYPE:break;case $e.CMD_BUFFER_SIZE:this.mBufferSize=500*t.popEBV(),u.info(`★★★ bufferSize: ${this.mBufferSize}[0x${this.mBufferSize.toString(16)}]`);break;case $e.CMD_PRINTER_DPI:this.mPrinterInfo.printerDPI=t.popShort(),u.info(`★★★ printerDPI: ${this.mPrinterInfo.printerDPI}`);break;case $e.CMD_PRINTER_WIDTH:if(t.Remains>=5){const e=t.popByteArray(),i=255&e[0],s=255&e[1];this.mPrinterInfo.printerWidth=(i<<8)+s;const r=255&e[2],n=255&e[3];this.mPrinterInfo.paperWidth=Math.round(.1*((r<<8)+n)),this.mPrinterInfo.printerLocateArea=255&e[4],u.info(`---- pagerWidth: ${this.mPrinterInfo.paperWidth}, printerLocateArea: ${this.mPrinterInfo.printerLocateArea}`)}else this.mPrinterInfo.printerWidth=t.popShort();u.info(`★★★ printerWidth: ${this.mPrinterInfo.printerWidth}`);break;case $e.CMD_MANUFACTURER:this.mPrinterInfo.manufacturer=t.popString(),u.info(`★★★ manufacturer: ${this.mPrinterInfo.manufacturer}`);break;case $e.CMD_DARKNESS:this.mPrinterInfo.printDarkness=t.popByte(),u.info(`★★★ printDarkness: ${this.mPrinterInfo.printDarkness}`);break;case $e.CMD_SPEED:this.mPrinterInfo.printSpeed=t.popByte(),u.info(`★★★ printSpeed: ${this.mPrinterInfo.printSpeed}`);break;case $e.CMD_GAP_TYPE:this.mPrinterInfo.gapType=t.popByte(),u.info(`★★★ gapType: ${this.mPrinterInfo.gapType}`);break;case $e.CMD_GAP_LEN:this.mPrinterInfo.gapLength=t.popEBV(),u.info(`★★★ gapLength: ${this.mPrinterInfo.gapLength}`);break;case $e.CMD_HARDWARE_FLAGS:{const e=t.Data;e.length>20?this.mPrinterInfo.batteryCount=255&e[20]:this.mPrinterInfo.batteryCount=2;const i=t.popInteger();this.mPrinterInfo.hardwareFlags=i,u.info(`mHardwareFlags: [${Te.toHexString(this.mPrinterInfo.hardwareFlags,"0x")}]`),t.Remains>=4?this.mPrinterInfo.softwareFlags=t.popInteger():this.mPrinterInfo.softwareFlags=$e.PCPDSF_MOTOR_ANTIDIR|$e.PCPDSF_PRTA_RIGHT|i&$e.PCPDSF_RLE5_BITMAP,u.info(`★★★ mSoftwareFlags: [${Te.toHexString(this.mPrinterInfo.softwareFlags,"0x")}]`);break}case $e.CMD_IS_PRINTABLE:this.mPrintable=t.popByte(),t.Remains>=5?t.popByte()==t.popByte()?this.mIsPrPageKey=t.popShort():this.mIsPrPageKey=0:this.mIsPrPageKey=-1,u.info(`★★★ printable  : 0x${this.mPrintable}, isPrPageKey: 0x${this.mIsPrPageKey.toString(16)}`);break;case $e.CMD_DEVICE_NAME:{const e=t.popString();1===this.mDeviceNameStage?(this.mPrinterInfo.deviceName=e,u.info(`★★★ nameStage[${this.mDeviceNameStage}]: deviceName: ${e}`)):2===this.mDeviceNameStage?(this.mPrinterInfo.seriesName=e,u.info(`★★★ nameStage[${this.mDeviceNameStage}]: seriesName: ${e}`)):3===this.mDeviceNameStage?(this.mPrinterInfo.devIntName=e,u.info(`★★★ nameStage[${this.mDeviceNameStage}]: devIntName: ${e}`)):u.warn(`----  nameStage[${this.mDeviceNameStage}]: 未处理的设备信息 deviceName: ${e}`),this.mDeviceNameStage++;break}case $e.CMD_DEVICE_VERSION:{const e=`${Te.toHexByteString(t.popByte())}`;this.mPrinterInfo.deviceVersion=e.substring(0,1)+"."+e.substring(1),u.info(`★★★ deviceVersion: ${this.mPrinterInfo.deviceVersion}`);break}case $e.CMD_SOFTWARE_VERSION:this.mPrinterInfo.softwareVersion=t.popString(),u.info(`★★★ softwareVersion: ${this.mPrinterInfo.softwareVersion}`);break;case $e.CMD_MOTORMODE:this.mPrinterInfo.motorMode=t.popByte();break;case $e.CMD_AUTOPOWEROFF:this.mPrinterInfo.autoPowerOffMins=t.popEBV();break;case $e.CMD_REQ_ADCVALUE:case $e.CMD_LANGUAGE:case $e.CMD_CAP_GAPTYPE:case $e.CMD_CAP_MOTORMODE:case $e.CMD_CAP_LANGUAGE:break;case $e.CMD_ENABLE_SETTING:129===t.popByte()&&u.info(`---- This printer supported command package start char is ${Te.toHexByteString(Be.HOST_TO_DEVICE_DATA_START,"0x")}`);break;case $e.CMD_PERIPHERALFLAGS:1==t.Remains?this.mPrinterInfo.peripheralFlags=t.popByte():t.popByte()===$e.CMD_PERIPHERALTYPE_FLAGS&&(this.mPrinterInfo.peripheralFlags=t.popShort());break;case $e.CMD_DEV_HANDSHAKE:break;case $e.CMD_MANU_TOOLKIT:{const e=t.popByte();u.info(`★★★ CMD_MANU_TOOLKIT.childCmd: 【0x${Te.toHexByteString(e)}】`),this.parseManuToolkit(t,e);break}case $e.CMD_DEBUG_BUFFER:{const e=t.popByteArray();u.info(`★★★ CMD_MANU_TOOLKIT.debugBuffer: [${Te.getHexStringOfBytes(e)}]`)}break;case $e.CMD_PRINT_COUNTER:{const e=t.popByteArray();u.info(`★★★ CMD_MANU_TOOLKIT.printCounter: [${Te.getHexStringOfBytes(e)}]`);for(let t=0;t<4;++t){const i=((255&e[4*t])<<24)+((255&e[4*t+1])<<16)+((255&e[4*t+2])<<8)+(255&e[4*t+3]);u.info(`---- printCounter.line${t} = ${i}`)}}}this.dispatcherRequest(t)}onDataReceived(t){const e=new Uint8Array(t),i=[];u.log(`#### 【onDataReceived】 [${Te.getHexStringOfBytes(e)}]`);for(const t of e)if(this.mReadBuffer.length<=0)t===Be.DEVICE_TO_HOST_DATA_START?this.mReadBuffer.push(t):i.push(t);else if(t===Be.DEVICE_TO_HOST_DATA_START&&this.mPackageStartRecords.push(this.mReadBuffer.length),this.mReadBuffer.push(t),this.mReadBuffer.length>=4){let t=Be.parse(this.mReadBuffer);if(!t&&this.mPackageStartRecords.length>0)for(let e=0;e<this.mPackageStartRecords.length;e++){const s=this.mPackageStartRecords[e];if(this.mReadBuffer.length-s>=4&&(t=Be.parse(this.mReadBuffer.slice(s)),t)){i.push(...this.mReadBuffer.slice(0,s));break}}t&&(this.onReadPackage(t),this.mReadBuffer.splice(0),this.mPackageStartRecords.splice(0))}i.length>0&&u.warn(`#### 【丢弃无效的指令】：${Te.getHexStringOfBytes(i)}`),this.mReadBuffer.length>0&&u.info(`---- 待处理数据：[${Te.getHexStringOfBytes(this.mReadBuffer)}]`)}setBleMtu(t){const e="MT_DF1"===this.mDFType||"DF1"===this.mDFType||"DF2"===this.mDFType;return this.Adapter.setBleMtu({deviceId:t,mtu:this.mRequestMTUs,waits:this.mWriteWaitMSs,chipType:this.mDFType,required:e}).then((t=>("number"==typeof t.waits&&t.waits>0&&(this.mWriteWaitMSs=t.waits),t.status>0?this.mDFType?e&&(this.mRequestMTUs=23):(this.mRequestMTUs=this.mInitMTUs>=23?this.mInitMTUs:23,this.mInitWriteWaits>0&&(this.mWriteWaitMSs=this.mInitWriteWaits)):"number"==typeof t.mtu&&(this.mRequestMTUs=t.mtu>23?t.mtu:23),u.info(`---- setBleMtu.resp.status    : ${t.status}`),u.info(`---- requestMTU.mDFType       : ${this.mDFType}`),u.info(`---- requestMTU.mRequestMTUs  : ${this.mRequestMTUs}`),u.info(`---- requestMTU.mWriteWaitMSs : ${this.mWriteWaitMSs}`),u.info(`---- requestMTU.mExtraWriteMSs: ${this.mExtraWriteMSs}`),0!==t.status||Te.sleep(100,!0))))}readMTUInfo(t,e){const i=e.find((t=>ze.isMatchedUUID(t,"180A")));return i?this.Adapter.getGATTCharacteristics({deviceId:t,serviceId:i}).then((e=>{var s;const r=null===(s=e.find((t=>ze.isMatchedUUID(t.uuid,"2A24"))))||void 0===s?void 0:s.uuid;if(r)return Te.sleep(20).then((()=>this.Adapter.readCharacteristic({deviceId:t,serviceId:i,characteristicId:r}).then((t=>("string"==typeof t&&(t=Te.getBytes(t)),t)))))})):Promise.resolve(void 0)}getValidateCharacteristic(t,i){return e(this,void 0,void 0,(function*(){const e=yield this.Adapter.getGATTCharacteristics({deviceId:t,serviceId:i}),s=[];for(const t of e)if(16&t.properties&&!(32&t.properties)&&(s[0]||ze.isMatchedUUID(t.uuid,"FF03")||(s[0]=t,u.log(`---- 检测到 notify 通道： {uuid: ${t.uuid}, properties:${t.properties}}`))),(8&t.properties||4&t.properties)&&(s[1]||(ze.isMatchedUUID(i,"FFE0")?ze.isMatchedUUID(t.uuid,"FFE1")&&(s[1]=t):s[1]=t,s[1]&&u.log(`---- 检测到 write 通道： {uuid: ${t.uuid}, properties:${t.properties}}`))),s[0]&&s[1])break;return s[0]&&!s[0].serviceId&&(s[0].serviceId=i),s[1]&&!s[1].serviceId&&(s[1].serviceId=i),s}))}getPrintCharacteristics(t,i,s){return e(this,void 0,void 0,(function*(){let e,r;if(u.log(`---- suggestedService = ${s}`),s&&i.length>0&&(s=s.toUpperCase(),s=i.find((t=>ze.getFullUUID(t).startsWith(s)))||""),s){const e=yield this.getValidateCharacteristic(t,s);if(e[0]&&e[1])return u.info(`---- 通过推荐的服务[${s}] 检测到了有效的读写通道！`),{notifyId:e[0],writeId:e[1]};u.warn(`---- 通过推荐的服务[${s}] 未检测到有效的读写通道！`)}for(const s of i)if(ze.isSupportedService(s)){u.log(`---- 检测到可用 service: ${s}`);const i=yield this.getValidateCharacteristic(t,s);if(!e&&i[0]&&(e=i[0]),!r&&i[1]&&(r=i[1]),e&&r)return{notifyId:e,writeId:r};u.log(`---- service ${s} 读写特征值不全！`)}else u.log(`---- 不可用 serviceId: ${s}`);return u.warn("---- 未检测到有效的打印通道！"),{}}))}getGATTServices(t){return e(this,void 0,void 0,(function*(){const e=[];if(!this.mConnected)return u.warn("---- 打印机链接已断开！"),e;for(let i=0;i<5;i++){const s=yield this.Adapter.getGATTServices({deviceId:t});if(s&&s.length>0){u.info(`---- 【getGATTServices】: 第 [${i+1} / 5] 获取成功！`),e.push(...s);break}u.warn(`---- 【getGATTServices】: 第 [${i+1} / 5] 获取失败！`),yield Te.sleep(300)}return e}))}startNotification(t,e,i){return this.getGATTServices(t).then((t=>{const e=t.map((t=>"string"==typeof t?t:(null==t?void 0:t.uuid)||""));return Te.sleep(20,e)})).then((i=>(u.info(`---- serviceList.length: ${null==i?void 0:i.length}`),this.getPrintCharacteristics(t,i||[],e).then((e=>{const s=e.notifyId,r=e.writeId;return s&&r?(u.info(`---- notifyId [${s.serviceId}]: ${s.uuid}`),u.info(`---- writeId  [${r.serviceId}]: ${r.uuid}`),this.readMTUInfo(t,i||[]).then((e=>(this.parseMTUInfo(this.mFactoryInfo,e),u.info(`---- ---- requestMtu: ${this.mRequestMTUs} (byte)`),u.info(`---- ---- writeWaits: ${this.mWriteWaitMSs} (ms)`),this.setBleMtu(t).then((()=>(this.mWriteCharacteristic=r,this.mNotifyCharacteristic=s,this.mReadBuffer.splice(0),this.Adapter.notifyCharacteristic({deviceId:t,serviceId:s.serviceId,characteristicId:s.uuid,state:!0,dataReceived:t=>{"string"==typeof t&&(t=Te.getBytes(t)),this.onDataReceived(t)}}).then((t=>Te.sleep(50,t)))))))))):(u.warn("---- 未检测到有效的服务！"),!1)})))))}getPrinterInfo(){var t,e;return this.ConnectStatus>Ge.Checking?Object.assign({},this.mPrinterInfo,{name:this.mDeviceName||"",deviceId:this.mDeviceId||"",writeCharacteristicId:null===(t=this.mWriteCharacteristic)||void 0===t?void 0:t.uuid,notifyCharacteristicId:null===(e=this.mNotifyCharacteristic)||void 0===e?void 0:e.uuid}):Object.assign({},this.mPrinterInfo)}createBleConnection(t){return e(this,void 0,void 0,(function*(){const e=t.device||{};this.mDeviceId=e.deviceId,this.mDeviceName=e.name,this.raisePrintStatusChanged(Ge.Connecting);const i="number"==typeof t.tryTimes&&t.tryTimes>0?t.tryTimes:5;let s=0;const r={value:!1},n=t.timeout||5e3;setTimeout((()=>{r.value=!0}),n);for(let a=0;a<i;a++){if(a>0&&(yield Te.sleep(100)),s=yield this.Adapter.createBleConnection({deviceId:e.deviceId,timeout:n,connectionStateChange:e=>{e.deviceId!==this.mDeviceId||e.connected||(this.mConnected=!1,this.disconnect(),t.connectionStateChange&&t.connectionStateChange(e))}}),s<=0){u.info(`---- 【createBleConnection】: 第 [${a+1} / ${i}] 次链接成功！`);break}{const t=r.value?"超时":"失败";if(u.warn(`---- 【createBleConnection】: 第 [${a+1} / ${i}] 次链接${t}！`),r.value)break}}if(s>0)return this.raisePrintStatusChanged(Ge.None),Te.processFailResult({statusCode:s>we.ERROR_OTHER?s:we.ERROR_CONNECT_FAILED,resultInfo:"打印机链接失败！"});this.mConnected=!0,this.raisePrintStatusChanged(Ge.Connected),this.mDevice=e,this.mDeviceId=e.deviceId,this.mDeviceName=e.name,this.mFactoryInfo=He.createInstance(e.advertisData),u.info(`---- factoryInfo: ${this.mFactoryInfo?this.mFactoryInfo.toString():"[-]"}`),yield Te.sleep(100);const a=this.mFactoryInfo?this.mFactoryInfo.suggestedUUID:"";if(!(yield this.startNotification(e.deviceId,a,t.delayTime)))return u.warn("---- 数据检测启动失败！"),yield this.disconnect(e),Te.processFailResult({statusCode:we.ERROR_START_NOTIFICATION,resultInfo:"notify 特征值启动失败！"});this.raisePrintStatusChanged(Ge.Checking),yield Te.sleep(30);const o=yield this.checkPrinter();return o.statusCode!==we.OK?(u.error(`---- 打印机相关参数获取失败(statusCode: ${o.statusCode})！`),this.raisePrintStatusChanged(Ge.None),yield this.disconnect(e),Te.processFailResult(o)):(this.mDeviceId=e.deviceId,this.raisePrintStatusChanged(Ge.ReadyPrint),Te.processSuccessResult({statusCode:we.OK,resultInfo:this.getPrinterInfo()}))}))}isDeviceConnected(t){return this.mPrintStatus>=Ge.ReadyPrint&&this.mDeviceId===t}connect(t){return e(this,void 0,void 0,(function*(){const e=(t.device||{}).deviceId;if(!e)return{statusCode:we.ERROR_PARAM,resultInfo:"未指定设备ID！"};if(this.mPrintStatus>Ge.None){if(e===this.mDeviceId)return this.IsConnected?(u.info(`---- 打印机已连接：${e}`),{statusCode:we.OK,resultInfo:"设备已连接！"}):(u.info("---- 正在链接，请稍后..."),new Promise((t=>{const e=this.mPrintStatusChanged.on((i=>{i===Ge.ReadyPrint?(this.mPrintStatusChanged.off(e),t({statusCode:we.OK})):i===Ge.None&&(this.mPrintStatusChanged.off(e),t({statusCode:we.ERROR_CONNECT_FAILED}))}))})));yield this.disconnect()}return yield this.createBleConnection(t)}))}disconnect(t,i){return e(this,void 0,void 0,(function*(){const e=t?t.name:this.mDeviceName,s=t?t.deviceId:this.mDeviceId||"";return u.info(`---- disconnect(${e}[${s}])`),u.info(`---- ---- mPrintStatus: ${this.mPrintStatus}`),!(s&&this.mDeviceId!==s||(this.mConnected&&this.mPrintStatus===Ge.Printing&&!i&&(yield new Promise((t=>{const e=this.mPrintStatusChanged.on((i=>{(i===Ge.ReadyPrint||i>Ge.Printing)&&(this.mPrintStatusChanged.off(e),t(e))}))}))),this.mPrintStatus>Ge.Connected&&this.mDeviceId&&this.mNotifyCharacteristic&&(yield this.Adapter.notifyCharacteristic({deviceId:this.mDeviceId,serviceId:this.mNotifyCharacteristic.serviceId,characteristicId:this.mNotifyCharacteristic.uuid,state:!1})),this.mConnected=!1,this.mWriteCharacteristic=void 0,this.mNotifyCharacteristic=void 0,yield this.Adapter.closeBleConnection({name:e,deviceId:s||""}),this.raisePrintStatusChanged(Ge.None),u.info("---- 打印机断开成功！"),0))}))}setPrinterParam(t){this.mPrintStatus<Ge.ReadyPrint||this.mPrintStatus===Ge.Sending||this.checkAndSendPrintParams(t)}pushPrintJob(t){const e=t.jobOptions;if(e.onlyGetRawData){const i=e.imageDatas||[];for(let s=0;s<i.length;s++){const r=ke.encodeImageData(Object.assign(Object.assign({},e),{imageData:i[s]})),n={statusCode:we.OK,pageKey:0,printCopies:1,copyIndex:0,printPages:i.length,pageIndex:s,printData:r.length>0?Te.combineUint8Array(r):void 0};r.length<=0&&(n.statusCode=we.ERROR_PARAM),t.onPageSendComplete&&t.onPageSendComplete(n)}t.onJobSendComplete&&t.onJobSendComplete(we.OK)}else this.mPrintJobList.push(t),t.jobPushed&&t.jobPushed(),(this.mPrintStatus===Ge.ReadyPrint||this.mPrintStatus>Ge.Sending)&&(this.mPausedPageList.splice(0),this.popAndSendPrintJob())}onPrinterNotAvailable(t){if(this.mPrintStatus===Ge.Sending||this.mPrintStatus===Ge.Printing){this.mPausedPageList.splice(0),this.mPausedPageList.push(...Array.from(this.mPrintingMap.values())),this.mPrintingMap.clear(),this.raisePrintStatusChanged(Ge.Paused);const e=this.mPausedPageList.map((t=>Te.toHexShortString(t.pageKey,"0x"))).join(",");if(u.warn(`#### #### 检测到打印机异常: printable = ${this.mPrintable}`),u.info(`---- 打印任务已暂停，并已暂存未完成的打印任务：[${e}]`),this.mPausedPageList.length>0&&(t=this.mPausedPageList[0]),t){const e=t.job,i=Object.assign(Object.assign({},t),{printable:this.mPrintable,statusCode:we.ERROR_PRINTER_NOT_AVAILABLE});e.onPagePrintComplete&&e.onPagePrintComplete(i,e)}}}stopCheckPrintPageKeyTimer(){this.mPrintPageKeyCheckTimer>0&&(clearInterval(this.mPrintPageKeyCheckTimer),this.mPrintPageKeyCheckTimer=0)}checkPrintPageKey(){this.isPageKeyValid&&(this.mPrintPageKeyCheckTimer>0||(this.checkIndex=0,this.mPrintPageKeyCheckTimer=setInterval((()=>{if(this.mPrintable===$e.DZIP_PRINTABLE&&(this.checkIndex++,this.checkIndex>10)){const t=Array.from(this.mPrintingMap.keys()).map((t=>`0x${t.toString(16)}`)).join(";"),e=Array.from(this.mTempPageMap.keys()).map((t=>`0x${t.toString(16)}`)).join(";");u.warn(`---- 检测到未处理的打印任务：pageKey1 = [${t}}], pageKey2 = [${e}]`),this.mTempPageMap.clear(),this.stopCheckPrintPageKeyTimer()}this.mIsCheckingPrintable||this.mPrintStatus!==Ge.Printing||(u.log("---- checkPrintPageKey：检查打印页面的 pageKey!"),this.tryCheckPrintable(2,2e3))}),300)))}onPrintProgress(t,e){if(u.log(`---- onPrintProgress: printStatus = ${this.mPrintStatus}`),this.mPrintingMap.size<=0&&this.mTempPageMap.size<=0)this.stopCheckPrintPageKeyTimer();else if(this.mPrintStatus===Ge.Sending||this.mPrintStatus===Ge.Printing)if(t>=$e.DZIP_PAGENOTREADY&&t<255)this.onPrinterNotAvailable();else if(t>=0){let i;if(e<=0){u.warn(`---- 检测到无效的 pageKey[${e}]，首张未处理的打印信息已转存到临时缓存！`);const t=Array.from(this.mPrintingMap.keys());i=this.mPrintingMap.get(t[0]),i&&(this.mTempPageMap.set(i.pageKey,i),this.mPrintingMap.delete(i.pageKey))}else this.mPrintingMap.has(e)?(i=this.mPrintingMap.get(e),this.mPrintingMap.delete(e)):this.mTempPageMap.has(e)&&(i=this.mTempPageMap.get(e),this.mTempPageMap.delete(e));if(i){const s=i.job;s.onPagePrintComplete&&s.onPagePrintComplete({statusCode:we.OK,printable:t,pageKey:i.pageKey,pageKey2:e,pageIndex:i.pageIndex,printPages:i.printPages,copyIndex:i.copyIndex,printCopies:i.printCopies},s);const r=Array.from(this.mPrintingMap.keys()).map((t=>`0x${t.toString(16)}`)),n=Array.from(this.mTempPageMap.keys()).map((t=>`0x${t.toString(16)}`));u.info(`---- 剩余打印页面：[${r.join(",")}]`),u.info(`---- 无效打印页面：[${n.join(",")}]`)}this.mPrintingMap.size<=0&&this.mTempPageMap.size<=0&&this.mPrintJobList.length<=0&&this.mPausedPageList.length<=0&&(this.raisePrintStatusChanged(Ge.ReadyPrint),this.stopCheckPrintPageKeyTimer())}}sendPausedPages(){return e(this,void 0,void 0,(function*(){if(this.mPausedPageList.length<=0)return we.OK;let t=we.OK;for(u.info(`#### #### sendPausedPages: 继续打印暂存的打印任务: [${this.mPausedPageList.map((t=>Te.toHexShortString(t.pageKey,"0x"))).join(",")}]`),this.stopCheckPrintPageKeyTimer();this.mPausedPageList.length>0;){const e=this.mPausedPageList.shift();if(e){if(this.mPrintStatus>=Ge.Stopped){u.log("---- 取消打印正在进行的暂存任务！"),t=we.ERROR_JOB_CANCELED;break}const i=`J${e.job.jobId}-P${e.pageIndex}-C${e.copyIndex}`;if(this.isPageKeyValid&&ke.updatePageKey(e.dataList,e.pageKey),t=yield this.sendPrintPage(e.dataList,e.pageKey,e.job,!0,i),t===we.ERROR_PRINTER_NOT_AVAILABLE){this.mPausedPageList.unshift(e),e.job.onPageSendComplete&&e.job.onPageSendComplete(Object.assign(Object.assign({},e),{statusCode:t,printable:this.mPrintable}));break}if(t!==we.OK){const i=e.job;this.raisePrintStatusChanged(Ge.Stopped),i.onPageSendComplete&&i.onPageSendComplete(Object.assign(Object.assign({},e),{statusCode:t})),i.onPagePrintComplete&&i.onPagePrintComplete(Object.assign(Object.assign({},e),{statusCode:t}),i);break}e.job.onPageSendComplete&&e.job.onPageSendComplete(Object.assign(Object.assign({},e),{statusCode:t})),e.copyIndex+1>=e.printCopies&&e.job.onJobSendComplete&&e.job.onJobSendComplete(t),this.raisePrintStatusChanged(Ge.Printing),this.mPrintingMap.set(e.pageKey,e)}}if(t!==we.OK&&t!==we.ERROR_PRINTER_NOT_AVAILABLE){let e=-1;for(const i of this.mPausedPageList)i.job.jobId!==e&&(i.job.onPagePrintComplete&&i.job.onPagePrintComplete(Object.assign(Object.assign({},i),{statusCode:t}),i.job),e=i.job.jobId);this.mPausedPageList.splice(0)}return this.checkPrintPageKey(),t}))}popAndSendPrintJob(){return e(this,void 0,void 0,(function*(){let t=we.OK;const e=this.mPrintJobList.shift();if(!e)return this.isPageKeyValid?this.raisePrintStatusChanged(Ge.Printing):this.raisePrintStatusChanged(Ge.ReadyPrint),this.checkPrintPageKey(),we.OK;const i=e.jobOptions.imageDatas||[];let s=e.jobOptions.printCopies||e.jobOptions.copies;s="number"==typeof s&&s>1?s:1,this.stopCheckPrintPageKeyTimer(),this.mPrintStatus>=Ge.Paused&&this.raisePrintStatusChanged(Ge.ReadyPrint);for(let r=0;r<i.length;r++){const n={pageKey:this.mPageKey,statusCode:t,printable:this.mPrintable,pageIndex:r,printPages:i.length,copyIndex:0,printCopies:s};(yield this.tryCheckPrintable(3,2e3))?this.isPrinterAvailable||(t=this.mPrintable===$e.DZIP_JOBCANCELED?we.ERROR_PRINTER_CANCELED:we.ERROR_PRINTER_NOT_AVAILABLE):t=we.ERROR_DATA_SEND_ERROR,u.info(`【${e.jobId}】 - 开始生成打印数据 page: [${r+1} - ${i.length}]!`);const a=ke.encodeImageData(Object.assign(Object.assign({},e.jobOptions),{imageData:i[r]}),this.isPageKeyValid?this.mPageKey:0,this.mPrinterInfo);if(a.length<=0){t=we.ERROR_PARAM,e.onPageSendComplete&&e.onPageSendComplete(n);break}for(let o=0;o<s;o++){n.copyIndex=o,n.pageKey=this.mPageKey,n.printable=this.mPrintable,this.mPrintStatus>=Ge.Stopped&&(t=we.ERROR_JOB_CANCELED);const h=`J${e.jobId}-P${r}-C${o}`;if(t===we.OK&&(this.isPageKeyValid&&o>0&&ke.updatePageKey(a,this.mPageKey),t=yield this.sendPrintPage(a,this.mPageKey,e,o>0,h)),t===we.OK)this.raisePrintStatusChanged(Ge.Printing);else if(t!==we.ERROR_PRINTER_NOT_AVAILABLE){e.onPageSendComplete&&e.onPageSendComplete(Object.assign(Object.assign({},n),{printable:this.mPrintable,statusCode:t})),this.raisePrintStatusChanged(Ge.Stopped);break}n.printable=this.mPrintable;const c=Object.assign(Object.assign({},n),{pageKey:this.mPageKey,job:e,dataList:a});if(t===we.ERROR_PRINTER_NOT_AVAILABLE?(this.mPrintStatus!==Ge.Paused&&this.onPrinterNotAvailable(c),this.mPausedPageList.push(c),u.info(`---------- 暂存打印数据：${h}, pageKey: 0x${this.mPageKey.toString(16)}`)):t===we.OK&&this.isPageKeyValid&&this.mPrintingMap.set(this.mPageKey,c),e.onPageSendComplete&&e.onPageSendComplete(Object.assign(Object.assign({},n),{printable:this.mPrintable,statusCode:t})),o+1>=s&&r+1>=i.length&&(e.endKey=this.mPageKey),this.mPageKey=ze.nextPageKey(this.mPageKey),t!==we.OK&&t!==we.ERROR_PRINTER_NOT_AVAILABLE)break}if(t!==we.OK)break}return u.info(`【${e.jobId}】 - 当前打印任务处理完毕, statusCode = ${t}！`),e.onJobSendComplete&&e.onJobSendComplete(t),t!==we.OK?(this.mPrintJobList.splice(0),this.raisePrintStatusChanged(Ge.ReadyPrint),t):this.popAndSendPrintJob()}))}sendPrintPage(t,i,s,r,n){return e(this,void 0,void 0,(function*(){const e=t;let a=this.mBufferSize,o=0;const h=`0x${i.toString(16)}`;if(r){if(!(yield this.tryCheckPrintable(3,2e3)))return we.ERROR_DATA_SEND_ERROR;if(!this.isPrinterAvailable)return this.mPrintable===$e.DZIP_JOBCANCELED?we.ERROR_PRINTER_CANCELED:we.ERROR_PRINTER_NOT_AVAILABLE}for(u.info(`=====>【${n||s.jobId} - ${i}(${h})】 - 开始发送打印任务!`),this.raisePrintStatusChanged(Ge.Sending);o<e.length;){const t=e[o];if(this.mPrintStatus>=Ge.Stopped)return u.warn(`    [${h}] - 打印任务已取消，当前打印进度：[${o+1} / ${e.length}]！`),we.ERROR_JOB_CANCELED;if(a<3e3){if(u.log(`    [${h}] - 检查缓存大小`),!(yield this.tryCheckPrintable(5)))return u.warn(`    [${h}] - 缓存检查失败，bufferSize: ${a}`),u.warn(`    [${h}] - 数据接收异常，停止发送！`),we.ERROR_DATA_SEND_ERROR;if(this.isPrinterAvailable)a=this.mBufferSize,u.log(`    [${h}] - bufferSize: ${a}`);else{if(this.mPrintable!==$e.DZIP_TPHTOOHOT)return u.warn(`    [${h}] - 打印机异常，printable: ${this.mPrintable}`),we.ERROR_PRINTER_NOT_AVAILABLE;u.warn(`    [${h}] - 打印头温度过高！`),yield Te.sleep(1e3)}}else u.log(`    **** [${h}] - 数据发送进度: [${o+1} / ${e.length}]`),u.log(`    ---- [${h}] - dataLen[mtu,waits,extraWaits]: ${t.length} [${this.mRequestMTUs-3}, ${this.mWriteWaitMSs}, ${this.mExtraWriteMSs}]`),yield this.sendPackage(t,o),o++,a-=t.length}return yield Te.sleep(50),we.OK}))}sendPackage(t,i){return e(this,void 0,void 0,(function*(){return yield this.write(t,i)}))}trySendPackage(t,i,s){return e(this,void 0,void 0,(function*(){if(s&&s>0){let e={statusCode:we.OK};for(let r=0;r<s;r++){if(e=yield this.sendPackage(t,i),e.statusCode===we.OK)return e;u.warn(`第 [${r+1} - ${s}] 次发送数据包失败！`)}return e}return this.sendPackage(t,i)}))}write(t,i){return e(this,void 0,void 0,(function*(){var e,s;const r=this.mRequestMTUs-3,n=t.length,a=this.mWriteWaitMSs+this.mExtraWriteMSs;let o,h={statusCode:we.OK};for(let c=0,l=0,d=0;c<n;)if(h={statusCode:we.ASYNC_WAIT},o=c+r<t.length?t.slice(c,c+r):t.slice(c),this.Adapter.writeCharacteristic({deviceId:this.mDeviceId||"",serviceId:(null===(e=this.mWriteCharacteristic)||void 0===e?void 0:e.serviceId)||"",characteristicId:(null===(s=this.mWriteCharacteristic)||void 0===s?void 0:s.uuid)||"",data:o,pkgIndex:i,subPkgIndex:d,complete:t=>{h=t}}),yield Te.sleep(a>0?a:1),h.statusCode>we.OK){if(l++,u.warn(`---- 打印数据发送失败，正在重新发送打印数据, tryTimes = [${l} / 5], waitTimes = ${a}`),l>=5)break}else h.statusCode,we.OK,l=0,c+=r,d++;return h}))}stopPrint(){if(u.info(`---- stopPrint: ${this.mPrintPageKeyCheckTimer}`),this.mPrintPageKeyCheckTimer>0&&this.stopCheckPrintPageKeyTimer(),this.mPrintStatus===Ge.Paused){if(this.mPausedPageList.length>0){const t=this.mPausedPageList.map((t=>Te.toHexShortString(t.pageKey,"0x"))).join(",");u.log(`---- 清空暂存打印任务：${t}`),this.mPausedPageList.splice(0)}this.mPrintingMap.size>0&&this.mPrintingMap.clear(),this.mTempPageMap.size>0&&this.mTempPageMap.clear(),this.raisePrintStatusChanged(Ge.ReadyPrint)}else this.mPrintStatus>Ge.ReadyPrint&&(this.raisePrintStatusChanged(Ge.Stopped),this.mPrintingMap.size>0&&this.mPrintingMap.clear())}continuePrint(){return this.mPausedPageList.length<=0?Promise.resolve(we.OK):this.sendPausedPages()}pushImageData(t){const e={statusCode:we.OK};this.mPrintStatus===Ge.Paused&&this.stopPrint(),t.data&&t.width&&t.height&&("string"==typeof t.data&&(t.data=Te.getBytes(t.data)),t.imageData={width:t.width,height:t.height,data:t.data,colorSpace:"srgb"});const i=t.imageDatas||[];if(i.length<=0&&t.imageData&&i.push(t.imageData),!i||i.length<=0)return e.statusCode=we.ERROR_PARAM,e.status=e.statusCode,t.onJobComplete&&t.onJobComplete(e),e;if(!t.onlyGetRawData&&this.mPrintStatus<Ge.ReadyPrint)return e.statusCode=we.ERROR_DISCONNECTED,e.status=e.statusCode,t.onJobComplete&&t.onJobComplete(e),e;{const s=this.mJobId++;t.imageDatas=i;const r=[];return t.onlyGetRawData&&(e.printData=r),this.pushPrintJob({jobId:s,startKey:this.mPageKey,jobOptions:t,jobPushed:()=>{u.info(`======== 打印任务 [${s}] 添加成功!`),t.jobPushed&&t.jobPushed(s)},onPageSendComplete:e=>{const i=e.pageKey||0;u.log(`---- 打印页面 [${s} - ${i}(0x${i.toString(16)})] 数据发送结束: { result: ${e.statusCode}, page: [${e.pageIndex+1} / ${e.printPages}], copy: [${e.copyIndex+1} / ${e.printCopies}]}`),e.printData&&e.printData.length>0&&r.push(e.printData),e.status=e.statusCode,t.onPageComplete&&t.onPageComplete(e)},onPagePrintComplete:(i,r)=>{const n=i.pageKey||0;u.log(`---- 打印页面 [${s} - ${n}(0x${n.toString(16)})] 打印结束: { result: ${i.statusCode}, page: [${i.pageIndex+1} / ${i.printPages}], copy: [${i.copyIndex+1} / ${i.printCopies}]}`),t.onPagePrintComplete&&t.onPagePrintComplete(i),e.statusCode=i.statusCode,e.printable=i.printable,i.statusCode!==we.OK?t.onJobPrintComplete&&t.onJobPrintComplete(e):i.pageIndex+1===i.printPages&&i.copyIndex+1===i.printCopies&&(u.info(`★★★★★ 打印任务【${r.jobId}】 打印完毕全部打印完毕！ ★★★★★`),t.onJobPrintComplete&&t.onJobPrintComplete(e))},onJobSendComplete:i=>{u.info(`======== 打印任务 [${s}] 处理完毕!`),Object.assign(e,{statusCode:i,status:i,printable:this.mPrintable}),t.onJobComplete&&t.onJobComplete(e)}}),e}}printImageData(t){return new Promise((e=>{this.pushImageData(Object.assign(Object.assign({},t),{onJobComplete:i=>{t.onJobComplete&&t.onJobComplete(i),e(i)}}))}))}checkPrinter(){return e(this,void 0,void 0,(function*(){let t=yield this.writeCommandAsync($e.CMD_PRINTER_DPI);if(t.statusCode!==we.OK)return t;const e=new Me;if(e.pushPackage($e.CMD_PRINTER_WIDTH),e.pushPackage($e.CMD_IS_PRINTABLE),t=yield this.write(e.getAllBytes()),t.statusCode>we.OK)return t;if(this.mDeviceNameStage=1,e.clearBuffer(),e.pushPackage($e.CMD_SOFTWARE_VERSION),e.pushPackage($e.CMD_DEVICE_NAME),e.pushPackage($e.CMD_DEVICE_VERSION),e.pushPackage($e.CMD_MANUFACTURER),yield this.write(e.getAllBytes()),e.clearBuffer(),e.pushPackage($e.CMD_ENABLE_SETTING,[~$e.CMD_ENABLE_SETTING]),e.pushPackage($e.CMD_HARDWARE_FLAGS,[1]),this.mEnableMcuId&&(e.pushPackage($e.CMD_MANU_TOOLKIT,[$e.CMD_MCU_GETID]),e.pushPackage($e.CMD_PERIPHERALFLAGS,[$e.CMD_PERIPHERALTYPE_FLAGS,0]),e.pushPackage($e.CMD_MANU_TOOLKIT,[$e.CMD_MANUSHIPTIME])),e.pushPackage($e.CMD_ENABLE_SETTING,[128]),t=yield this.write(e.getAllBytes()),t.statusCode>we.OK)return t;e.clearBuffer(),e.pushPackage($e.CMD_DEVICE_NAME,["S".charCodeAt(0)]),e.pushPackage($e.CMD_DEVICE_NAME,["D".charCodeAt(0)]),yield this.write(e.getAllBytes()),e.clearBuffer(),e.pushPackage($e.CMD_GAP_TYPE),e.pushPackage($e.CMD_GAP_LEN),yield this.write(e.getAllBytes());let i=[13];return this.mClientType>0&&(i=this.mClientType>255?Be.getBytesFromShort(this.mClientType):[this.mClientType]),this.writeCommandAsync($e.CMD_DEVICE_TYPE,Array.from(i))}))}waitPrintable(t){const e=t||3e3;return new Promise((t=>{let i=0;const s=setInterval((()=>{i++,(!this.mIsCheckingPrintable||10*i>=e)&&(clearInterval(s),t(!0))}),10)}))}checkPrintable(t){return e(this,void 0,void 0,(function*(){this.mIsCheckingPrintable&&(yield this.waitPrintable()),this.mIsCheckingPrintable=!0,this.mPrintable=255,u.info("▶▶▶▶▶ checkPrintable: 0xFF ◀◀◀◀◀");const e=new Me;return e.pushPackage($e.CMD_ENABLE_SETTING,[~$e.CMD_ENABLE_SETTING]),e.pushPackage($e.CMD_HARDWARE_FLAGS,[1]),e.pushPackage($e.CMD_ENABLE_SETTING,[0]),yield this.write(e.getAllBytes()),new Promise((i=>{e.clearBuffer(),e.pushPackage($e.CMD_IS_PRINTABLE),e.pushPackage($e.CMD_BUFFER_SIZE),this.write(e.getAllBytes()),this.enqueueRequest($e.CMD_BUFFER_SIZE,(t=>{t||(this.mPrintable=-1,u.warn("---- checkPrintable timeout!"));const e=this.mPrintable,s=this.mIsPrPageKey.toString(16);u.info(`▶▶▶▶▶ printable: 0x${e}, pageKey: ${this.mIsPrPageKey}[0x${s}] ◀◀◀◀◀`),this.onPrintProgress(this.mPrintable,this.mIsPrPageKey),this.mIsCheckingPrintable=!1,i(t)}),t||2e3)}))}))}tryCheckPrintable(t,i){return e(this,void 0,void 0,(function*(){if(!(t&&t>0))return this.checkPrintable(i);for(let e=0;e<t;e++){const s=yield this.checkPrintable(i);if(s)return s;u.warn(`---- tryCheckPrintable failed of tryTimes: [${e+1} - ${t}]`)}}))}checkAndSendPrintParams(t,i){return e(this,void 0,void 0,(function*(){if(!this.IsConnected)return;this.mPrintStatus,Ge.Sending,this.mIsCheckingPrintable&&(yield this.waitPrintable()),this.mIsCheckingPrintable=!0,this.mPrintable=255,u.info("▶▶▶▶▶ checkAndSendPrintParams: printable = 0xFF ◀◀◀◀◀");const e=new Me;let s=t.printDarkness;return"number"==typeof s&&s>=_e.Min&&s<=_e.High&&e.pushPackage($e.CMD_DARKNESS,[s-1]),s=t.printSpeed,"number"==typeof s&&s>=Se.Min&&s<=Se.Max&&e.pushPackage($e.CMD_SPEED,[s-1]),s=t.gapType,"number"==typeof s&&s>=De.None&&s<=De.Trans&&e.pushPackage($e.CMD_GAP_TYPE,[s]),s=t.gapLength,"number"==typeof s&&s>0&&s<=ke.MAX_EBV_VALUE&&e.pushPackage($e.CMD_GAP_LEN,Be.fromEBV(s)),s=t.motorMode,"number"==typeof s&&s>=0&&e.pushPackage($e.CMD_MOTORMODE,[s]),s=t.autoPowerOffMins,"number"==typeof s&&s>0&&e.pushPackage($e.CMD_AUTOPOWEROFF,Be.fromEBV(s)),e.pushPackage($e.CMD_BUFFER_SIZE),yield this.write(e.getAllBytes()),new Promise((t=>{e.clearBuffer(),e.pushPackage($e.CMD_IS_PRINTABLE),e.pushPackage($e.CMD_BUFFER_SIZE),this.write(e.getAllBytes()),this.enqueueRequest($e.CMD_BUFFER_SIZE,(e=>{e||(this.mPrintable=-1,u.warn("---- checkAndSendPrintParams timeout!")),this.mIsCheckingPrintable=!1,t(e)}),i||2e3)}))}))}writeCommandAsync(t,i){return e(this,void 0,void 0,(function*(){return new Promise((e=>{u.log(`---- writeCommand: cmd = ${Te.toHexByteString(t)}`),this.write(Me.getBytes(t,i)).then((t=>{t.statusCode>we.OK&&e(t)})),this.enqueueRequest(t,(t=>{e(t?{statusCode:we.OK,resultInfo:t}:{statusCode:we.ERROR_RESPONSE_TIMEOUT,resultInfo:"指令发送响应超时！"})}))}))}))}}!function(t){t[t.BROADCAST=1]="BROADCAST",t[t.READ=2]="READ",t[t.WRITE_NO_RESPONSE=4]="WRITE_NO_RESPONSE",t[t.WRITE=8]="WRITE",t[t.NOTIFY=16]="NOTIFY",t[t.INDICATE=32]="INDICATE"}(Ve||(Ve={}));class Xe{static getInstance(){return this._instance||(this._instance=new Xe),this._instance}static isSupported(){return navigator&&"bluetooth"in navigator}constructor(){this.mShowWriteLog=!1,navigator&&"bluetooth"in navigator?this.mBluetooth=navigator.bluetooth:u.warn("---- 当前环境不支持 WebBluetooth API 接口！")}closeBleAdapter(){return Promise.resolve(!0)}startBleDiscovery(t){const e=t||{};return new Promise((t=>{if(this.mBluetooth){const i=(e.models||"").split(/[;]/).filter((t=>!!t));i.find((t=>t.startsWith("DT")))||i.push("DT"),i.find((t=>t.startsWith("DP")))||i.push("DP"),i.find((t=>t.startsWith("P")))||i.push("P"),this.mBluetooth.requestDevice({optionalServices:["49535343-fe7d-4ae5-8fa9-9fafd205e455","0000180a-0000-1000-8000-00805f9b34fb","0000ff00-0000-1000-8000-00805f9b34fb","0000ff10-0000-1000-8000-00805f9b34fb"],filters:i.map((t=>({namePrefix:t})))}).then((i=>{u.info(i),this.mDevice=i;const s=[{name:i.name,deviceId:i.id}];e.deviceFound&&e.deviceFound(s);const r={statusCode:0,resultInfo:i};e.complete&&e.complete(r),t(r)})).catch((i=>{u.warn(i);const s={statusCode:1,resultInfo:i};e.complete&&e.complete(s),t(s)}))}else{const i="当前环境不支持 WebBluetooth 功能！";u.warn(`---- ${i}`),Te.processFailResult({statusCode:-1,resultInfo:i},e,t)}}))}stopBleDiscovery(){return Promise.resolve(!0)}createBleConnection(t){var e,i;const s="string"==typeof t?{deviceId:t}:t||{};if(!s.deviceId)return new Promise((t=>{var e;(null===(e=this.mGattServer)||void 0===e?void 0:e.connected)?Te.processSuccessResult(we.OK,s,t):Te.processFailResult(we.ERROR_PARAM,s,t)}));if(null===(e=this.mGattServer)||void 0===e?void 0:e.connected)return this.mGattServer.device.id===s.deviceId?(u.info("---- 打印机已连接 ----"),Promise.resolve(Te.processSuccessResult(0,s))):this.closeBleConnection(s).then((()=>we.OK));const r=this.mDevice||(null===(i=this.mGattServer)||void 0===i?void 0:i.device);if(u.info(r),!r||s.deviceId!==r.id)return Promise.resolve(Te.processFailResult(we.ERROR_CONNECT_FAILED,s));try{return u.info("### 【Request】 device.gatt.connect()"),r.gatt.connect().then((t=>(u.info(t),(null==t?void 0:t.connected)?(this.mGattServer=t,this.mConnectionStateChange=s.connectionStateChange,r.addEventListener("gattserverdisconnected",(t=>{u.info("---- gattDevice.gattserverdisconnected: 打印机断开成功！"),u.info(t),this.mConnectionStateChange&&this.mConnectionStateChange({deviceId:r.id,connected:!1})})),Te.sleep(100).then((()=>Te.processSuccessResult(0,s)))):Te.processFailResult(we.ERROR_CONNECT_FAILED,s)))).catch((t=>(u.warn("---- 【Response】 device.gatt.connect.catch:"),u.warn(t),Te.processFailResult(we.ERROR_CONNECT_FAILED,s))))}catch(t){return u.warn("----- try catch: device.gatt.connect:"),u.warn(t),Promise.resolve(Te.processFailResult(we.ERROR_CONNECT_FAILED,s))}}closeBleConnection(t){return e(this,void 0,void 0,(function*(){var e;const i="string"==typeof t?{deviceId:t}:t||{};return this.mGattServer?i.deviceId&&i.deviceId!==(null===(e=this.mGattServer.device)||void 0===e?void 0:e.id)?(u.info(`#### WebBleAdapter.disconnect Failed! {current: ${this.mGattServer.device.id}, target: ${i.deviceId}}`),Te.processFailResult(we.ERROR_PARAM,i)):this.mGattServer.connected?(this.mNotifyCharacter&&(u.info("---- 关闭通知功能"),this.mNotifyCharacter.stopNotifications(),this.mNotifyCharacter=void 0,this.mWriteCharacter=void 0),this.mGattServer.connected&&(this.mGattServer.disconnect(),this.mGattServer=void 0),u.info("#### WebBleAdapter.disconnect success!"),Te.processSuccessResult(0,i)):Te.processSuccessResult(0,i):Te.processSuccessResult(we.ERROR_PARAM,i)}))}getConnectedBleDevices(){return Promise.resolve([])}setBleMtu(t){return e(this,void 0,void 0,(function*(){return t.mtu,Te.isIOS(),{status:-1}}))}isDeviceConnected(t){return!(!this.mGattServer||!this.mGattServer.connected)&&this.mGattServer.device.id===t}getGATTServices(t){return e(this,void 0,void 0,(function*(){const e=[];return u.info(`---- 【Request】getPrimaryServices(server: ${this.mGattServer?this.mGattServer.device.name:"undefined"})`),new Promise((i=>{this.mGattServer&&this.mGattServer.connected?this.mGattServer.getPrimaryServices().then((s=>{for(const t of s)u.log(`---- service uuid: ${t.uuid}`),e.push(t);Te.processSuccessResult(e,t,i)})).catch((e=>{u.warn("---- 【Response】 gattServer.getPrimaryServices.catch:"),u.warn(e),Te.processFailResult([],t,i)})):(u.warn("---- 打印机未链接！"),u.warn(this.mGattServer),Te.processFailResult([],t,i))}))}))}getGATTCharacteristics(t){return e(this,void 0,void 0,(function*(){const e=t||{},i=[];return u.info("---- 【Request】getBleGATTCharacteristics"),new Promise((s=>this.mGattServer?e.serviceId?void this.mGattServer.getPrimaryService(e.serviceId).then((r=>{r?r.getCharacteristics().then((t=>{if(!t||t.length<=0)u.warn("---- 未检测到任何特征值！"),e.complete&&e.complete(i),s(i);else{for(const s of t){let t=0;u.log(`---- characteristic uuid: ${s.uuid}`),s.properties.read&&(t|=Ve.READ),s.properties.writeWithoutResponse&&(t|=Ve.WRITE_NO_RESPONSE),s.properties.write&&(t|=Ve.WRITE),s.properties.notify&&(t|=Ve.NOTIFY),i.push({serviceId:e.serviceId,uuid:s.uuid,properties:t})}e.complete&&e.complete(i),s(i)}})):(u.warn(`---- 服务实例[${e.serviceId}] 获取失败！`),t.complete&&t.complete(i),s(i))})):(u.warn("---- 参数错误，未指定 serviceId 或者 characteristicId！"),t.complete&&t.complete(i),s(i),i):(u.warn("---- 打印机未链接！"),t.complete&&t.complete(i),void s(i))))}))}readCharacteristic(t){return e(this,void 0,void 0,(function*(){if(!this.mGattServer||!t.serviceId||!t.characteristicId)return Te.processFailResult("",t);const e=ze.getFullUUID(t.serviceId),i=ze.getFullUUID(t.characteristicId),s=yield this.mGattServer.getPrimaryService(e.toLowerCase());if(!s)return Te.processFailResult("",t);const r=yield s.getCharacteristic(i.toLowerCase());if(!r)return Te.processFailResult("",t);u.info("---- readCharacteristicValue:");const n=yield r.readValue();return u.info(n),Te.processSuccessResult(null==n?void 0:n.buffer,t)}))}notifyCharacteristic(t){return e(this,void 0,void 0,(function*(){if(u.info("---- 【Request】startNotifications"),!this.mGattServer)return u.warn("---- 打印机未链接！"),t.complete&&t.complete(!1),!1;if(!t.serviceId||!t.characteristicId)return u.warn("---- 参数错误！"),t.complete&&t.complete(!1),!1;const e=yield this.mGattServer.getPrimaryService(t.serviceId);if(!e)return u.warn(`---- 服务实例[${t.serviceId}] 获取失败！`),t.complete&&t.complete(!1),!1;const i=yield e.getCharacteristic(t.characteristicId);return i?(i.oncharacteristicvaluechanged=e=>{var i;const s=e.target;s.value&&t.dataReceived&&t.dataReceived(null===(i=s.value)||void 0===i?void 0:i.buffer)},(yield i.startNotifications())?(this.mNotifyCharacter=i,t.complete&&t.complete(!0),!0):(u.warn("---- #### startNotifications 失败！"),t.complete&&t.complete(!1),!1)):(u.warn(`---- notify特征值实例 [${t.characteristicId}] 获取失败！`),t.complete&&t.complete(!1),!1)}))}writeCharacteristic(t){return e(this,void 0,void 0,(function*(){if(!this.mGattServer)return Te.processFailResult({statusCode:1,resultInfo:"打印机未连接"},t);if(!this.mWriteCharacter){const e=yield this.mGattServer.getPrimaryService(t.serviceId);if(!e)return u.warn(`---- 服务实例[${t.serviceId}] 获取失败！`),Te.processFailResult({statusCode:2,resultInfo:"获取服务失败！"},t);const i=yield e.getCharacteristic(t.characteristicId);if(!i)return u.warn(`---- write特征值实例 [${t.characteristicId}] 获取失败！`),Te.processFailResult({statusCode:3,resultInfo:"特征值获取失败"},t);this.mWriteCharacter=i}if(this.mWriteCharacter&&t.data){const e=`[P ${t.pkgIndex} - SUP ${t.subPkgIndex}]`;return this.mShowWriteLog&&u.log(`#### 【Request】writeBLECharacteristicValue[${t.data.length}]: ${e}`),this.mWriteCharacter.writeValueWithoutResponse(t.data).then((t=>{this.mShowWriteLog&&u.log(t,`#### 【Response.success】uni.writeBLECharacteristicValue: ${e}`),this.mShowWriteLog=!1})).catch((t=>{u.warn(t),this.mShowWriteLog=!0})),Te.processSuccessResult({statusCode:0,resultInfo:void 0},t)}return Te.processFailResult({statusCode:4,resultInfo:"参数错误"},t)}))}}Xe.MTU_MAX=512,(Ke||(Ke={})).text="text";class Ze{static createDomParser(){try{return window.DOMParser?new DOMParser:void 0}catch(t){return void u.warn(t)}}static parseXmlDocument(t,e){if(t)try{const i=e||new DOMParser;return t?i.parseFromString(t,"text/xml"):void 0}catch(t){u.warn("xml parse error:"),u.warn(t)}}static splitEqual(t,e){const i=(t||"").split(";");for(const t of i)if(t===e)return!0;return!1}static getElementsByTagName(t,e,i,s){if(!t||!e||t.childElementCount<=0)return[];if(i){const i=[],r=t.children||t.childNodes;let n="";for(let t=0;t<r.length&&(n=r[t].tagName||r[t].nodeName,n.toLowerCase()!==e.toLocaleLowerCase()||(i.push(r[t]),!s));t++);return i}return t.getElementsByTagName(e)}static getElementByTagName(t,e,i){return this.getElementsByTagName(t,e,i,!0)[0]}static getElementValue(t){try{return t.childNodes[0].nodeValue||""}catch(t){return""}}static getElementValueByTagName(t,e){if(!e)return"";const i=e.split(";");for(let e=0;e<i.length;e++){const s=i[e];if(!s)continue;const r=this.getElementsByTagName(t,s,!0,!0);if(r.length>0)return this.getElementValue(r[0])}return""}static isNumberString(t){return!(!t||!(t=t.trim()).match(/^[-+]?[0-9]*\.?[0-9]+$/)&&!t.match(/^(0x)?[0-9a-f]+$/i))}static optNumber(t,e){return"number"==typeof t?t:(t=(t||"").trim(),this.isNumberString(t)?Number(t):e||0)}static isBooleanString(t){return!!t&&!!(t=t.trim()).match(/^(yes)|(no)|(true)|(false)$/i)}static optBoolean(t,e){return!!(t=(t||"").trim()).match(/^(yes)|(true)$/i)||!t.match(/^(no)|(false)$/i)&&"boolean"==typeof e&&e}static decodeBase64File(t,e){const i=Te.decodeBase64(t);if(i){try{return new File([i],e)}catch(t){u.warn(t)}try{return new Blob([i])}catch(t){u.warn(t)}}}static parseEmbeddedImg(t,e){if(!t)return t;const i=t.indexOf(Ze.EmbeddedSeparator);if(i<0)return t;let s=t.substring(0,i).trim().toLowerCase();return"."===s[0]&&(s=s.substring(1)),"svg"===s&&(s="svg+xml"),t=t.substring(i+Ze.EmbeddedSeparator.length),e&&e(s,t),`data:image/${s};base64,${t}`}static parseEmbeddedFile(t){if(!(t=(t||"").trim()))return;const e=t.indexOf(Ze.EmbeddedSeparator);let i;if(e>=0){const s=t.substring(0,e),r=t.substring(e+Ze.EmbeddedSeparator.length);i=Ze.decodeBase64File(r,s)}else i=Ze.decodeBase64File(t,"");return i}}Ze.EmbeddedSeparator="*****";class qe{static isWdfxJob(t){const e=t||{};return!(!e.layerClass||!e.templateID)&&"LPAPI"===e.layerClass.toUpperCase()&&(e.Page||e.page||[]).length>0}static getDefaultBarcodeText(t){return t===A.EAN13?"690123456789":t===A.EAN8?"6901234":t===A.UPC_A?"69012345678":t===A.UPC_E?"0123456":t===A.ISBN?"978012345678":"12345678"}static getDefaultQrcodeText(){return"http://detonger.com/wdbq"}static getContentText(t,e){const i="number"==typeof e.contentType?e.contentType:0,s=e.content||e.text;if(t=t.toLowerCase(),2===i){if(!s){if(t===Ie.barcode){const t=Ze.optNumber(e.barcodeType);return qe.getDefaultBarcodeText(t)}if(t===Ie.qrcode)return qe.getDefaultQrcodeText()}return s}if(i>0||s)return s;if(t===Ie.text)return s||"双击输入内容";if(t===Ie.qrcode)return s||qe.getDefaultQrcodeText();if(t===Ie.barcode){const t=Ze.optNumber(e.barcodeType);return qe.getDefaultBarcodeText(t)}return s}static rotateRect(t,e){(e=(e+360)%360)>3&&(e=Math.floor(e/90));const i=t.x||0,s=t.y||0,r=t.width||0,n=t.height||0,a=i+.5*r,o=s+.5*n;return 1!==e&&3!==e||(t.x=a-.5*n,t.y=o-.5*r,t.width=n,t.height=r),t}static getDateTime(t){const e=t.dateFormat||"",s=t.timeFormat||"",r=t.dateOffset||t.dateDiff||0,n=t.hourOffset||t.hourDiff||0,a=t.minuteOffset||t.minuteDiff||0,o=t.secondOffset||t.secondDiff||0,h=new Date;if(r||n||a||o){let t=h.getTime();r&&(t+=864e5*r),n&&(t+=36e5*n),a&&(t+=6e4*a),o&&(t+=1e3*o),h.setTime(t)}return e&&s?i.formatDate(`${e} ${s}`):e||s?i.formatDate(e||s,h):i.formatDate("yyyy年MM月dd日")}static parseWdxNode(t,e,i){if(t.children&&t.children.length>0){const s={},r=[];for(let n=0;n<t.children.length;n++){const a=t.children[n],o="function"==typeof e&&e(a,t),h=this.parseWdxNode(a,e,o);i?("object"!=typeof h||Array.isArray(h)||(h.layerClass=a.tagName),r.push(h)):s[a.tagName]=h}return i?r:s}if(t.childNodes&&t.childNodes.length>1){const s={},r=[];for(let n=0;n<t.childNodes.length;n++){const a=t.childNodes[n];if(a.nodeName===qe.NODE_NAME_TEXT)continue;const o="function"==typeof e&&e(a,t),h=this.parseWdxNode(a,e,o);i?("object"!=typeof h||Array.isArray(h)||(h.layerClass=a.nodeName),r.push(h)):s[a.nodeName]=h}return i?r:s}return t.childNodes&&t.childNodes.length>0&&t.childNodes[0].nodeValue||""}static getAutoValue(t){const e=t?t.toLowerCase():"";return"true"===e||"yes"===e||"false"!==e&&"no"!==e&&(Ze.isNumberString(e)?Number(e):t)}static loadWdfxContent(t,e){const i=Ze.parseXmlDocument(t||"",e),s=i?i.getElementsByTagName("LPAPI"):void 0,r=s&&s.length>0?s[0]:void 0;if(!r)return;const n=this.parseWdxNode(r,((t,e)=>{const i=(t.tagName||t.nodeName||"").toLowerCase(),s=(e.tagName||e.nodeName||"").toLowerCase();return e===r&&"page"===i||"table"===s&&"cells"===i}));return"string"==typeof n||Array.isArray(n)?void 0:(n.layerClass=r.tagName,n)}static updateWdfOptions(t,e){const s=t,r=t;for(const t of Object.keys(r)){let e=r[t];"string"==typeof e&&(r[t]=e=qe.getAutoValue(e)),"labelWidth"===t?(s.jobWidth=e,delete r[t]):"labelHeight"===t?(s.jobHeight=e,delete r[t]):"printOrientation"===t&&(s.orientation=e,delete r[t])}"string"==typeof s.background&&(s.background=Ze.parseEmbeddedImg(s.background));let n=[];if(t.Page?(n=t.Page,delete t.Page):t.page&&(n=t.page,delete t.page),s.border&&"string"==typeof s.border)try{const t=Ze.parseEmbeddedImg(s.border,((t,r)=>{if(s.borderType=t,e||"undefined"==typeof DOMParser||(e=new DOMParser),"svg+xml"===t&&e){"number"!=typeof s.borderScale&&(s.borderScale=1);try{const t=e.parseFromString(Te.atob(r),"image/svg+xml"),n=t?t.getElementsByTagName("svg"):void 0,a=n&&n.length>0?n[0]:void 0;if(a&&(s.svgElement=a,a.width&&(s.svgWidth=a.width.baseVal.value),a.height&&(s.svgHeight=a.height.baseVal.value),a.viewBox&&(s.svgViewBox=a.viewBox.baseVal),!s.svgViewBox&&a.attributes&&a.attributes.length>0))for(let t=0;t<a.attributes.length;t++){const e=a.attributes[t],r=e.name||e.nodeName||e.localName;if("width"===r){const t=e.value||e.nodeValue||"";!s.svgWidth&&Ze.isNumberString(t)&&(s.svgWidth=Ze.optNumber(t))}else if("height"===r){const t=e.value||e.nodeValue||"";!s.svgHeight&&Ze.isNumberString(t)&&(s.svgHeight=Ze.optNumber(t))}else if("viewBox"===r){const t=e.value||e.nodeValue||"";!s.svgViewBox&&t&&(s.svgViewBox=i.parseRect(t))}}}catch(t){u.warn(t)}}}));s.border=t}catch(t){u.warn(t)}const a=[];if(n&&n.length>0)for(const t of n){const e=t,i=e.layerClass?e.layerClass.toLowerCase():"";for(const t of Object.keys(e)){const i=e[t];"content"!==t&&"lineSpace"!==t&&"dataColumnName"!==t&&"columnName"!==t&&"string"==typeof i&&(e[t]=qe.getAutoValue(i))}if(void 0===e.horizontalAlignment&&"number"==typeof e.horAlignment&&(e.horizontalAlignment=e.horAlignment),void 0===e.verticalAlignment&&"number"==typeof e.verAlignment&&(e.verticalAlignment=e.verAlignment),i===Ie.line){const t=e.x1||0,i=e.y1||0,s=e.x2||0,r=e.y2||0;"number"==typeof e.x1&&delete e.x1,"number"==typeof e.y1&&delete e.y1,"number"==typeof e.x2&&delete e.x2,"number"==typeof e.y2&&delete e.y2,e.x=Math.min(t,s),e.y=Math.min(i,r),e.width=Math.abs(t-s),e.height=Math.abs(i-r)}if(e.width&&e.height&&e.orientation&&qe.rotateRect(e,-e.orientation),i===Ie.text)e.text=qe.getContentText(i,e),a.push(Object.assign(Object.assign({},e),{lineSpaceMode:g.getLineMode(e.lineSpace),lineSpace:g.valueOf(e.lineSpace,e.fontHeight||2.5),type:Ie.text}));else if("time"===i||"date"===i)e.text=qe.getDateTime(e),a.push(Object.assign(Object.assign({},e),{type:Ie.text}));else if("arctext"===i)e.text||(e.text=qe.getContentText(Ie.text,e)),a.push(Object.assign(Object.assign({},e),{lineWidth:"number"==typeof e.lineWidth?e.lineWidth:.5,type:Ie.arcText}));else if(i===Ie.barcode){e.text=qe.getContentText(i,e),"number"!=typeof e.horizontalAlignment&&(e.horizontalAlignment=1);const t="number"==typeof e.flags?e.flags:2,s="number"==typeof e.fontHeight&&e.fontHeight>0?e.fontHeight:0,r="number"==typeof e.textHeight?e.textHeight:s;a.push(Object.assign(Object.assign({},e),{textHeight:0===t?0:r,barcodeType:"number"==typeof e.type?e.type:e.barcodeType,type:Ie.barcode}))}else if(i===Ie.qrcode){e.text=qe.getContentText(i,e),"number"!=typeof e.horizontalAlignment&&(e.horizontalAlignment=1),"number"!=typeof e.verticalAlignment&&(e.verticalAlignment=1);let t=Ie.qrcode;1===e.type?(t=Ie.pdf417,"number"==typeof e.pdf417EccLevel&&(e.eccLevel=e.pdf417EccLevel),"number"==typeof e.pdf417Cols&&(e.cols=e.pdf417Cols)):2===e.type?(t=Ie.dataMatrix,"number"==typeof e.dmCodeShape&&(e.codeShape=e.dmCodeShape)):3===e.type?t="gridMatrix":("number"==typeof e.mask&&(e.qrMask=e.mask),"number"==typeof e.mode&&(e.qrMode=e.mode)),a.push(Object.assign(Object.assign({},e),{type:t}))}else if("image"===i||"logo"===i){const t=e.content||e.text;e.content&&delete e.content,e.text&&delete e.text,a.push(Object.assign(Object.assign({},e),{image:Ze.parseEmbeddedImg(t),type:Ie.image}))}else if("rectangle"===i||"rect"===i){let t=Ie.rect;"boolean"==typeof e.filled&&e.filled&&(e.fill=!0,delete e.filled),("number"!=typeof e.lineWidth||e.lineWidth<=0)&&(e.lineWidth=.5),1===e.type?("number"!=typeof e.cornerWidth||e.cornerWidth<=0)&&(e.cornerWidth=1):2===e.type?t=Ie.ellipse:3===e.type?t=Ie.circle:e.cornerWidth&&delete e.cornerWidth,a.push(Object.assign(Object.assign({},e),{type:t}))}else if(i===Ie.line){const t="number"==typeof e.type?e.type:0,i="number"==typeof e.dashGap&&e.dashGap>0?e.dashGap:1;1===t&&(e.dashLens=[i]),e.dashGap&&delete e.dashGap,a.push(Object.assign(Object.assign({},e),{type:Ie.line}))}else if(i===Ie.table){const t=e.Cells||e.cells||[];if(e.Cells&&delete e.Cells,Array.isArray(t)&&t.length>0)for(const e of t)for(const t of Object.keys(e)){const i=e[t];"content"!==t&&"lineSpace"!==t&&"dataColumnName"!==t&&"columnName"!==t&&"string"==typeof i&&(e[t]=qe.getAutoValue(e[t]))}a.push(Object.assign(Object.assign({},e),{rowCount:e.rows,columnCount:e.cols,cells:t,type:Ie.table}))}else i?a.push(Object.assign(Object.assign({},e),{type:i})):"string"==typeof e.type&&a.push(e)}return{jobInfo:t,jobPage:a}}static parseWdfx(t,e){let i;i="object"==typeof t?t:{content:t,domParser:e};const s=qe.loadWdfxContent(i.content,i.domParser);return s&&qe.isWdfxJob(s)?qe.updateWdfOptions(s,i.domParser):void 0}}qe.NODE_NAME_TEXT="#text";class Ye{get Models(){return this.models}constructor(t){this.includeModels=new Set,this.excludeModels=new Set,this.models=t||"";const e=this.models.split(";");let i="";for(const t of e)switch(t[0]){case"+":i=t.substring(1),i&&this.includeModels.add(i.toUpperCase());break;case"-":i=t.substring(1),i&&this.excludeModels.add(i.toUpperCase());break;default:t&&this.includeModels.add(t.toUpperCase())}}isSupport(t){if(!t)return!1;if(this.includeModels.size<=0&&this.excludeModels.size<=0)return!0;if(Ye.patternSuperD.exec(t))return!0;if(Ye.patternSuperO.exec(t))return!0;const e=ti.getModelName(t),i=e[0]?e[0].toUpperCase():"";return!this.excludeModels.has(i)&&(!(this.includeModels.size>0)||(!!this.includeModels.has(i)||void 0!==Array.from(this.includeModels).find((e=>t===e||t.match(new RegExp(e))))))}}Ye.patternSuperD=/^(.*)-(D[0-9]{4,5}[0-9A-Z]{2,5}[0-9]{2})$/,Ye.patternSuperO=/^(.*)-(O[0-9]{4,5}[0-9A-Z]{2,5}[0-9]{2})$/;const Qe={cancelCloseTimer(){this.closeTimer&&this.closeTimer>0&&(clearTimeout(this.closeTimer),this.closeTimer=0,this.closeAction&&this.closeAction(!1))}};class ti{static getInstance(t){const e=t||{};return this.sInstance?t&&this.sInstance.setOptions(t):this.sInstance=new ti(e),e.adapter&&(this.sInstance.BleAdapter=e.adapter),e.context&&(this.sInstance.mContext=e.context),e.models&&this.sInstance.setSupportPrefixes(e.models),this.sInstance}static create(t){return new ti(t)}static isPreviewJobName(t){return!!t&&Re.isPreviewJobName(t)}static isTransPrevJob(t){return!!t&&Re.isTransPrevJob(t)}static isWhitePrevJob(t){return!!t&&Re.isWhitePrevJob(t)}static registerBarcode1DEncoder(t,e){Bt.registerBarcodeCreator(t,e)}static registerBarcode2DEncoder(t){be.getInstance().register(t)}static getModelName(t){const e=(t||"").lastIndexOf("-");return e>0?[t.substring(0,e),t.substring(e+1)]:[]}static isSupperTrade(t){return"D"===t||"O"===t}static isTradeSupported(t,e){return!e||!!t&&(!!ti.isSupperTrade(t)||e.split(";").indexOf(t)>=0)}static getPrinterNameInfo(t){t=t||"";const e=ti.patternGeneral.exec(t);if(!e)return;let i=e[2];const s=/^\d+$/.exec(i);let r="",n=0;if(i[1]<"0"||i[1]>"9"?(r=i.substring(0,2),i=i.substring(2),n+=11*r.charCodeAt(0),n+=13*r.charCodeAt(1)):(i[0]<"0"||i[0]>"9")&&(r=i.substring(0,1),i=i.substring(1),n+=17*r.charCodeAt(0)),!(i.length<8)){if(!s||i.length>=9||i.charCodeAt(3)!=P.num_0){if(s){n+=2*parseInt(i.charAt(0)),n+=3*parseInt(i.charAt(1)),n+=5*parseInt(i.charAt(2));for(var a=4;a<i.length;++a)n+=parseInt(i.charAt(a))*(1&a?9:7)}else for(n+=2*Number(i.charAt(0)),n+=3*Number(i.charAt(1)),n+=5*Number(i.charAt(2)),a=4;a<i.length;++a)n+=i.charCodeAt(a)*(1&a?9:7);if("5682904137".charAt(n%10)!=i.charAt(3))return}return{model:e[0],serials:i,trade:r,checkSum:n}}}static isSupportedDevice(t){const e=ti.getPrinterNameInfo(t);return!(!e||!ti.isSupperTrade(e.trade)&&(ti.trades&&!ti.isTradeSupported(e.trade,ti.trades)||ti.matcher&&!ti.matcher.isSupport(t)))}static trimDeviceName(t){return t.replace(/[\ufffd\u007f\u0000-\u001f]/g,"")}static loadImageSrc(t){return Re.loadImageSrc(t)}static loadHtmlImage(t){return Re.html2Image(t)}static getRawData(t){return ke.encodeImageData({imageData:t,printerDPI:203,printerWidth:384})}static test(){}constructor(t){this.mDeviceMap=new Map,this.mJsonMode=!1,this.mLineHeight=0,this.mCurrPosX=0,this.mCurrPosY=0,this.mJobPage=[],this.mTotalPages=1,this.mPageIndex=0,this.mPageList=[];const e=t||{};this.mInitInfo=e,this.BleAdapter=e.adapter||Xe.getInstance(),this.setOptions(t)}setOptions(t){const e=t||{};this.mInitInfo=Object.assign(this.mInitInfo,e),e.adapter&&(this.BleAdapter=e.adapter),e.context&&this.setDrawContext(e.context),e.models&&this.setSupportPrefixes(e.models),e.clientType&&e.clientType>0&&(this.mPrinter.ClientType=e.clientType),"boolean"==typeof e.enablePageKey&&(this.mPrinter.isPageKeyEnable=e.enablePageKey),"boolean"==typeof e.enableMcuId&&(this.mPrinter.EnableMcuId=e.enableMcuId),"number"==typeof e.requestMTUs&&(this.mPrinter.InitMTUs=e.requestMTUs),"number"==typeof e.writeWaits&&(this.mPrinter.InitWriteWaits=e.writeWaits),"number"==typeof e.extraWriteTimes&&(this.mPrinter.ExtraWriteTimes=e.extraWriteTimes),"number"==typeof e.logLevel&&u.setLevel(e.logLevel)}getBarcode2DEncoder(t){return t?be.getEncoder(t):void 0}setBarcode2DEncoder(t){return"object"==typeof t&&be.setEncoder(t.barcodeType,t)}get BleAdapter(){return this.mPrinter.Adapter}set BleAdapter(t){this.mPrinter?this.mPrinter.Adapter=t:this.mPrinter=new ze(t)}get Context(){if(!this.mContext){let t=this.mInitInfo.createContext?this.mInitInfo.createContext({}):void 0;t||(t=new Re({})),this.setDrawContext(t)}return this.mContext}setDrawContext(t){t&&(this.abortJob(),t.IsApiMode=!0,this.mContext=t)}createDrawContext(t){return this.mInitInfo.createContext?this.mInitInfo.createContext(t):new Re({apiMode:!0})}getResultMessage(t){switch(t){case we.ASYNC_WAIT:return"异步等待中，请稍后";case we.OK:return"OK";case we.ERROR_PARAM:return"参数错误！";case we.ERROR_NO_PRINTER:return"未检测到打印机或者未指定打印机";case we.ERROR_DISCONNECTED:return"打印机未连接";case we.ERROR_CONNECT_FAILED:return"打印机链接失败";case we.ERROR_START_NOTIFICATION:return"数据Notify特征值启动失败";case we.ERROR_DATA_SEND_ERROR:return"数据发送失败";case we.ERROR_DATA_RECEIVE_ERROR:return"数据接收异常，打印机无响应";case we.ERROR_IS_PRINTING:return"打印机正在打印过程中不能打印其他标签";case we.ERROR_RESPONSE_TIMEOUT:return"指令发送响应超时";case we.ERROR_JOB_CREATE:return"打印任务创建失败";case we.ERROR_JOB_CANCELED:return"打印任务被取消";case we.ERROR_GET_IMAGE_DATA:return"打印数据获取失败";case we.ERROR_PRINTER_NOT_AVAILABLE:return"打印机状态异常";case we.ERROR_OTHER:return"其他未知异常";default:return"其他异常！"}}getPrinterStatus(t){const e=t||this.mPrinter.Printable;switch(e){case $e.DZIP_PRINTABLE:return"OK";case $e.DZIP_ISPRINTING:return"正在打印";case $e.DZIP_ISROTATING:return"正在转动马达";case $e.DZIP_NOJOB:return"没有打印任务";case $e.DZIP_PAGENOTREADY:return"页面数据还没有接收完全";case $e.DZIP_JOBCANCELED:return"当前打印任务被取消";case $e.DZIP_VOLTOOLOW:return"打印电压太低了";case $e.DZIP_VOLTOOHIGH:return"打印电压太高了";case $e.DZIP_TPHNOTFOUND:return"没有检测到打印头";case $e.DZIP_TPHTOOHOT:return"打印头温度太高了";case $e.DZIP_COVEROPENED:return"打印机盖子打开了";case $e.DZIP_NO_PAPER:return"未检测到纸张";case $e.DZIP_RIBBONCANOPENED:return"碳带盒未锁紧";case $e.DZIP_NO_RIBBON:return"未检测到碳带";case $e.DZIP_UNMATCHED_RIBBON:return"不匹配的碳带";case $e.DZIP_TPHTOOCOLD:return"环境温度过低";case $e.DZIP_USEDUP_RIBBON:return"用完的碳带";case $e.DZIP_USEDUP_RIBBON2:return"用完的色带";case $e.DZIP_LABELCANOPENED:return"标签盒未锁紧";default:return"未知异常:"+e}}loadImage(t,e){return this.Context.loadImage(t).then((t=>(e&&e(t),t)))}loadHtml(t){return this.Context.loadHtml("string"==typeof t?t:t?t.innerHTML:"")}setSupportPrefixes(t){const e=Array.isArray(t)?t.join(";"):t||"";ti.matcher=e?new Ye(e):void 0}setSupportTrades(t){ti.trades=Array.isArray(t)?t.join(";"):t||"",ti.trades&&(ti.trades=ti.trades.toUpperCase())}updateDeviceList(t,e,i){const s=[];if(t&&t.length>0)for(const r of t){if(!r.name||!r.deviceId)continue;r.name=ti.trimDeviceName(r.name||r.localName||"");const t=ti.getPrinterNameInfo(r.name);t?(r.advertisData&&"string"==typeof r.advertisData&&(r.advertisData=Te.getBytes(r.advertisData)),ti.isSupperTrade(t.trade)?(this.mDeviceMap.set(r.deviceId,r),s.push(r)):e&&!ti.isTradeSupported(t.trade,e)?u.info(`---- ----: 受限的行业编码：${r.name}`):i&&!i.isSupport(r.name)?u.info(`---- ----: 受限的打印机型号：${r.name}`):(this.mDeviceMap.set(r.deviceId,r),s.push(r))):r.name}return s}startBleDiscovery(t){return e(this,void 0,void 0,(function*(){const e=t||{};u.log("----- lpapi.startBleDiscovery:"),u.log(JSON.stringify(e));const i=e.models?new Ye(e.models):ti.matcher,s=e.trades||ti.trades;return this.BleAdapter.getConnectedBleDevices().then((r=>{let n="",a="",o="",h=[];const c="number"==typeof e.timeout?e.timeout:void 0;return this.mDiscovering=!0,this.mDeviceMap.clear(),r&&r.length>0&&this.updateDeviceList(r,s,i),this.BleAdapter.startBleDiscovery({interval:e.interval,resetAdapter:e.resetAdapter,deviceFound:t=>{this.updateDeviceList(t,s,i),h=this.getPrinters(),a=h.map((t=>t.deviceId)).join(";"),o=h.map((t=>t.name)).join(";"),a!==n&&(u.info(`---- ---- 检测到新设备：【${o}】`),n=a,e.deviceFound&&e.deviceFound(h),"number"==typeof c&&c<=0&&(u.info(`---- 检测到打印机，自动停止搜索(timeout: ${c})！`),this.stopBleDiscovery()))},adapterStateChange:t=>{this.mDiscovering=t.discovering,e.adapterStateChange&&e.adapterStateChange(t)},complete:r=>{if(0===r.statusCode&&(c&&c>0&&(u.info("---- 启动蓝牙搜索定时器："),Qe.scanTimer=setTimeout((()=>{u.info(`---- 蓝牙搜索超时时间到：[timeout = ${c}]`),Qe.scanTimer=0,this.stopBleDiscovery()}),c)),this.mPrinter.IsConnected)){const t=this.mPrinter.CurrentDevice;t&&(u.info(`---- 检测到已连接设备：${t.name}`),this.updateDeviceList([t],s,i),h=this.getPrinters(),h.length>0&&e.deviceFound&&e.deviceFound(h))}0===r.statusCode?(u.log("---- 蓝牙搜索启动成功！"),Te.processSuccessResult(r,t)):Te.processFailResult(r,t)}})}))}))}stopBleDiscovery(){return e(this,void 0,void 0,(function*(){return Qe.scanTimer&&Qe.scanTimer>0&&(u.info("---- 停止扫描超时定时器："),clearTimeout(Qe.scanTimer),Qe.scanTimer=0),this.BleAdapter.stopBleDiscovery()}))}searchPrinter(t){return e(this,void 0,void 0,(function*(){const e=t||{};return new Promise((t=>{let i=!1;this.startBleDiscovery({timeout:e.timeout||0,models:e.models,deviceFound:s=>{if((null==s?void 0:s.length)>0){let r;for(const t of s){if(!e.name&&!e.deviceId){r=t;break}if(e.name&&e.name===t.name){r=t;break}if(e.deviceId&&e.deviceId===t.deviceId){r=t;break}}r&&(i=!0,t(r),this.stopBleDiscovery())}},adapterStateChange:e=>{e.discovering||i||(i=!0,t(void 0))}}).then((e=>{0!==e.statusCode&&t(void 0)}))}))}))}getPrinters(){return Array.from(this.mDeviceMap.values()).sort(((t,e)=>t.RSSI&&e.RSSI?e.RSSI-t.RSSI:e.RSSI?1:t.RSSI?-1:0))}getPrinterInfo(){return this.mPrinter.getPrinterInfo()}getPrinterWidthMM(){const t=this.mPrinter.getPrinterInfo();return t&&t.printerWidth>0&&t.printerDPI>0?t.printerWidth/t.printerDPI*25.4:48}isPrinterOpened(){return this.mPrinter.IsConnected}isJobPaused(){return this.mPrinter.ConnectStatus===Ge.Paused}openPrinter(t){return e(this,void 0,void 0,(function*(){u.log(t,"#### 【openPrinter】options:"),Qe.cancelCloseTimer();const e="function"==typeof t?t:void 0,i="string"==typeof t?t:void 0,s=!!i&&ti.isSupportedDevice(i),r="object"==typeof t?t:{name:s?i:"",deviceId:s?"":i,complete:e};!r.deviceId&&r.id&&(r.deviceId=r.id);const n=yield this.BleAdapter.getConnectedBleDevices(),a=this.updateDeviceList(n,ti.trades,ti.matcher),o=this.getPrinters();let h;if(r.name||r.deviceId)for(const t of o){if(t.deviceId===r.deviceId||t.name===r.name){h=t;break}if(r.name&&t.name.startsWith(r.name.toUpperCase())){h=t;break}}else if(this.isPrinterOpened())return Te.processSuccessResult({statusCode:we.OK},r);if(h||(r.name&&r.deviceId&&ti.isSupportedDevice(r.name)?h={name:r.name,deviceId:r.deviceId}:a.length>0&&(h=a[0])),!h){if("boolean"!=typeof r.autoScan&&(r.autoScan=!0),!r.autoScan)return Te.processFailResult({statusCode:we.ERROR_NO_PRINTER,resultInfo:"未指定目标打印机"},r);if(u.info(`#### 未检测到缓存的蓝牙设备，正在进行重新搜索：{ name: ${r.name}, deviceId: ${r.deviceId} }`),h=yield this.searchPrinter({name:r.name,deviceId:r.deviceId,models:r.models,timeout:5e3}),!h)return u.warn("#### 未检测到匹配的蓝牙设备！"),Te.processFailResult({statusCode:we.ERROR_NO_PRINTER,resultInfo:"未搜索到到打印机设备！"},r)}u.info(`#### 正在链接打印机设备：{ name: ${h.name}, deviceId: ${h.deviceId} }...`),this.mDiscovering&&(yield this.stopBleDiscovery()),r.clientType&&(this.mPrinter.ClientType=r.clientType);const c=yield this.mPrinter.connect({device:h,timeout:r.timeout,connectionStateChange:r.connectionStateChange,tryTimes:r.tryTimes,delayTime:r.delayTime});return c.statusCode===we.OK?(u.info("★★★★★★ 打印机链接成功！★★★★★★"),Te.processSuccessResult(c,r)):(u.info(`★★★★★★ 打印机链接失败[${c.statusCode}]！★★★★★★`),Te.processFailResult(c,r))}))}closePrinter(t,i){return e(this,void 0,void 0,(function*(){const e="number"==typeof t?{delay:t,callback:i}:t||{};return Qe.cancelCloseTimer(),e.force?this.mPrinter.disconnect(void 0,!0):new Promise((t=>{Qe.closeAction=e=>{i&&i(e),Qe.closeAction=void 0,t(e)},Qe.closeTimer=setTimeout((()=>{Qe.closeTimer=0,Qe.closeAction=void 0,this.mPrinter.disconnect().then((e=>{i&&i(e),t(e)}))}),e.delay)}))}))}stopPrint(){this.mPrinter.stopPrint(),this.mPausedPrintInfo&&(this.mPausedPrintInfo=void 0)}isPrintJob(){return this.Context.isPrintJob()}startJob(t){t||(t={}),"number"!=typeof t.width&&(t.width=0),"number"!=typeof t.height&&(t.height=0),u.log(t,"#### 【startJob】:");const e=t.jobName;if(t.context&&t.context!==this.mContext&&this.setDrawContext(t.context),!this.Context)return void u.warn("---- 未检测到绘制上下文！");if(this.mJobPrintOptions){if(!t.autoAbort)return void u.warn("---- 有未完成的打印任务！");this.abortJob()}t.height<=0&&"boolean"!=typeof t.jsonMode&&(t.jsonMode=!0);const i="boolean"==typeof t.jsonMode&&t.jsonMode;if(this.mJsonMode=i,this.isPrinterOpened()){const e=this.mPrinter.getPrinterInfo();t.dpi=e.printerDPI,t.printerDpi=e.printerDPI,t.printerWidth=e.printerWidth}else t.dpi||(t.dpi=this.mPrinter.PrinterDPI),t.printerDpi||(t.printerDpi=this.mPrinter.PrinterDPI),t.printerWidth||(t.printerWidth=this.mPrinter.PrinterWidth);!t.backgroundColor&&ti.isWhitePrevJob(e)&&(t.backgroundColor=ti.COLOR_WHITE);const s=Re.getMargins(t);return this.mLineHeight="number"==typeof t.lineHeight?t.lineHeight:0,this.mLineHeight<0&&(this.mLineHeight=0),this.mJobPrintOptions={jobInfo:Object.assign(Object.assign({},t),{jobWidth:t.width,jobHeight:t.height}),jobPages:[],jobArguments:t.jobArguments,onJobCreated:t.onJobCreated},this.mJobPage.splice(0),i?this.mCurrPosY=s[0]>0?s[0]:0:(t.width<=0&&(t.width=this.getPrinterWidthMM()),this.mPageList.splice(0),this.mTotalPages=t.printPages||0,this.mPageIndex=0,this.mPageInfo=void 0),this.startPage()}startPrintJob(t,i){return e(this,void 0,void 0,(function*(){if("function"==typeof t.callback&&(i=t.callback),"boolean"==typeof t.printMode?t.printMode:!Re.isPreviewJobName(t.jobName)){const e=yield this.openPrinter({name:t.printerName||t.deviceName,deviceId:t.deviceId,autoScan:!1});if(e.statusCode!==we.OK)return i&&i(e),e}const e=this.startJob(t),s={statusCode:e?we.OK:we.ERROR_JOB_CREATE,resultInfo:e};return i&&i(s),s}))}abortJob(){this.mPageInfo=void 0,this.mJobPrintOptions=void 0}toDataURL(){return this.Context.CanvasElement.toDataURL()}nextLine(t){"number"==typeof t&&(this.mLineHeight=t),this.mCurrPosY+=this.mLineHeight}getPosY(){return this.mCurrPosY}getCommandDatas(){}commitJob(t){return e(this,void 0,void 0,(function*(){u.log(t,"#### 【commitJob - start】options:");const e=t||{};return this.endPage(Object.assign({},e)).then((i=>{const s=this.mJobPrintOptions;if(this.mJobPrintOptions=void 0,u.log(`$$$$ 【commitJob - end】 statusCode: ${i.statusCode}`),i.statusCode===we.OK){if(this.mJsonMode){if(s&&s.jobPages&&s.jobPages.length>0){const t=s.jobInfo||{};let i;const r=e;for(const e of Object.keys(r))i=r[e],"function"==typeof i?s[e]=i:null!=i&&(t[e]=i);const n=e.jobArguments?e.jobArguments:s.jobArguments;return this.print(Object.assign(Object.assign({},s),{jobArguments:n}))}return Te.processFailResult({statusCode:we.ERROR_PARAM,resultInfo:"接口调用错误，未检测到有效的打印参数"})}const r=this.mPageList.map((t=>t.printData)).filter((t=>void 0!==t)),n=ti.getJobPrintResult(i,this.mPageList,i,r.length>0?r:void 0);return Te.processSuccessResult(n,t)}return Te.processFailResult(i,t)}))}))}startPage(){var t;if(u.log("#### 【startPage】"),this.mJsonMode)return this.mJobPage.length>0&&this.endPage({}),{};const e=null===(t=this.mJobPrintOptions)||void 0===t?void 0:t.jobInfo;if(!e)return void u.warn("---- 未检测到打印任务相关信息！");if(this.mPageInfo)return this.mPageInfo;const i=this.Context,s=i.startJob(Object.assign({},e));return s?(this.mInitInfo.fontName&&i.setFontName(this.mInitInfo.fontName),this.mInitInfo.fontHeight&&i.setFontHeight(this.mInitInfo.fontHeight),this.mInitInfo.lineWidth&&i.setLineWidth(this.mInitInfo.lineWidth),this.mTotalPages<=this.mPageIndex&&(this.mTotalPages=this.mPageIndex+1),this.mPageResult=void 0,this.mPageInfo=s):(e.onJobComplete&&e.onJobComplete({statusCode:we.ERROR_JOB_CANCELED}),e.onPagePrintComplete&&e.onPagePrintComplete({statusCode:we.ERROR_PARAM}),void u.warn(`---- 打印任务创建失败: {width: ${e.width}, height: ${e.height}}`))}endPage(t){return e(this,void 0,void 0,(function*(){var e;const i=t||{},s=!this.isPrintJob(),r=this.mPageInfo,n=null===(e=this.mJobPrintOptions)||void 0===e?void 0:e.jobInfo,a={statusCode:we.OK,printPages:this.mTotalPages,pageIndex:this.mPageIndex};if(this.mJsonMode){const t=this.mJobPage||[];if(this.mJobPrintOptions&&t.length>0){const e=this.mJobPrintOptions.jobPages||[];this.mJobPrintOptions.jobPages||(this.mJobPrintOptions.jobPages=e),e.push([...t]),t.splice(0)}else a.statusCode=we.ERROR_PARAM;return a}if(!r)return this.mJobPrintOptions&&this.mPageResult?Te.processSuccessResult(this.mPageResult):(a.statusCode=we.ERROR_OTHER,a);if(u.log("#### 【endPage】"),!n)return a.statusCode=we.ERROR_OTHER,a;i.orientation||(i.orientation=this.Context.Orientation);const o=this.Context.JobInfo;return o&&("boolean"==typeof i.antiColor&&(o.antiColor=i.antiColor),"boolean"==typeof i.horizontalFlip&&(o.horizontalFlip=i.horizontalFlip)),this.Context.commitJob().then((t=>{if(this.mPageInfo=void 0,this.mPageIndex++,!t)return a.statusCode=we.ERROR_OTHER,a;if(Object.assign(a,t),s)return u.info("---- 预览任务处理完毕！"),a.statusCode=we.OK,a;{const e=t.imageData;if(e){if(!this.mPrinter.IsConnected)return a.statusCode=we.ERROR_DISCONNECTED,a;u.info("---- 开始打印......");const t=Te.filter(i,((t,e)=>"function"!=typeof e));return this.printImageData(Object.assign(Object.assign({},t),{imageData:e,onPageComplete:t=>{n.onPageComplete&&n.onPageComplete(t)},onPagePrintComplete:t=>{n.onPagePrintComplete&&n.onPagePrintComplete(t)}})).then((t=>(a.statusCode=t.statusCode,t.statusCode===we.OK&&t.printData&&(a.printData=t.printData[0]),a)))}return u.warn("---- imageData获取失败！"),a.statusCode=we.ERROR_GET_IMAGE_DATA,a}})).then((t=>(this.mPageInfo=void 0,this.mPageResult=t,this.mPageList.push(t),t)))}))}setItemHorizontalAlignment(t){this.Context.setItemHorizontalAlignment(t)}setItemVerticalAlignment(t){this.Context.setItemVerticalAlignment(t)}setItemOrientation(t){this.Context.setItemOrientation(t)}drawText(t){if(u.log(t,"#### 【drawText】options:"),void 0===t.y&&(t.y=this.mCurrPosY),!this.mJsonMode)return this.Context.drawText(t);this.mJobPage.push(Object.assign(Object.assign({},t),{type:Ie.text}))}drawBarcode(t){return"string"==typeof t.barcodeType?this.draw2DBarcode(t):this.draw1DBarcode(t)}draw1DBarcode(t){return u.log(t,`#### 【draw1DBarcode】type[${t.barcodeType}], options:`),void 0===t.y&&(t.y=this.mCurrPosY),this.mJsonMode?(this.mJobPage.push(Object.assign(Object.assign({},t),{type:Ie.barcode})),!0):this.Context.draw1DBarcode(t)}draw2DBarcode(t){return u.log(t,`#### 【draw2DBarcode】type[${t.barcodeType}], options:`),void 0===t.y&&(t.y=this.mCurrPosY),this.mJsonMode?(this.mJobPage.push(Object.assign(Object.assign({},t),{type:Ie.qrcode})),!0):this.Context.draw2DBarcode(t)}drawQRCode(t){if(u.log(t,"#### 【drawQRCode】options:"),void 0===t.y&&(t.y=this.mCurrPosY),!this.mJsonMode)return this.Context.drawQRCode(t);this.mJobPage.push(Object.assign(Object.assign({},t),{type:Ie.qrcode}))}draw2DQRCode(t){return this.drawQRCode(t)}drawPDF417(t){if(u.log(t,"#### 【drawPDF417】options:"),void 0===t.y&&(t.y=this.mCurrPosY),!this.mJsonMode)return this.Context.drawPDF417(t);this.mJobPage.push(Object.assign(Object.assign({},t),{type:Ie.pdf417}))}draw2DPdf417(t){return this.drawPDF417(t)}drawDataMatrix(t){return u.log(t,"#### 【drawDataMatrix】options:"),void 0===t.y&&(t.y=this.mCurrPosY),this.mJsonMode?(this.mJobPage.push(Object.assign(Object.assign({},t),{type:Ie.dataMatrix})),!0):this.Context.drawDataMatrix(t)}draw2DDataMatrix(t){return this.drawDataMatrix(t)}drawRectangle(t){return u.log(t,"#### 【drawRectangle】options:"),void 0===t.y&&(t.y=this.mCurrPosY),this.mJsonMode?(this.mJobPage.push(Object.assign(Object.assign({},t),{type:Ie.rect})),!0):t.cornerWidth||t.cornerHeight?this.Context.drawRoundRect(t):this.Context.drawRect(t)}drawRect(t){return this.drawRectangle(t)}drawEllipse(t){if(u.log(t,"#### 【drawEllipse】options:"),void 0===t.y&&(t.y=this.mCurrPosY),!this.mJsonMode)return this.Context.drawEllipse(t);this.mJobPage.push(Object.assign(Object.assign({},t),{type:Ie.ellipse}))}drawCircle(t){if(u.log(t,"#### 【drawCircle】options:"),void 0===t.y&&(t.y=this.mCurrPosY),!this.mJsonMode)return this.Context.drawCircle(t);this.mJobPage.push(Object.assign(Object.assign({},t),{type:Ie.circle}))}drawLine(t){if(u.log(t,"#### 【drawLine】options:"),"number"!=typeof t.y1&&"number"!=typeof t.y2&&(t.y1=t.y2=this.mCurrPosY),!this.mJsonMode)return this.Context.drawLine(t);this.mJobPage.push(Object.assign(Object.assign({},t),{type:Ie.line}))}drawImage(t){return u.log(t,"#### 【drawImage】options:"),void 0===t.y&&(t.y=this.mCurrPosY),this.mJsonMode?(this.mJobPage.push(Object.assign(Object.assign({},t),{type:Ie.image})),!0):this.Context.drawImage(t)}drawImageAsync(t){return e(this,void 0,void 0,(function*(){if(this.mJsonMode||"string"!=typeof t.image)return this.drawImage(t);{const e="number"==typeof t.y?t.y:this.mCurrPosY;return this.Context.loadImage(t.image).then((i=>this.drawImage(Object.assign(Object.assign({},t),{y:e,image:i}))))}}))}drawImagePath(t){const e=t;return"string"==typeof e.src&&(e.image=e.src),this.drawImageAsync(t)}drawTable(t){if(u.log(t,"#### 【drawTable】options:"),void 0===t.y&&(t.y=this.mCurrPosY),("number"==typeof t.height&&t.height>0?t.height:0)<=0){const e=t.columnCount||t.columns||t.cols||0;if(!t.tableRows)if(Array.isArray(t.rows))t.tableRows=t.rows;else if(t.cells&&e>0){t.tableRows=[];for(let i=0;i<t.cells.length;i+=e)t.tableRows.push(t.cells.slice(i,i+e))}if(t.tableRows){let e=t.rowCount||("number"==typeof t.rows?t.rows:0);if(("number"!=typeof e||e<=0)&&(e=t.tableRows.length),t.rowHeights&&Array.isArray(t.rowHeights)||("number"==typeof t.rowHeights&&t.rowHeights>0?t.rowHeights=[t.rowHeights]:"number"==typeof t.rowHeight&&t.rowHeight>0&&(t.rowHeights=[t.rowHeight])),t.rowHeights&&Array.isArray(t.rowHeights)&&t.rowHeights.length>0){const i=t.rowHeights.length;if(i<e){const s=t.rowHeights[i-1];t.rowHeights=t.rowHeights.concat(Array(e-i).fill(s))}t.height=t.rowHeights.reduce(((t,e)=>t+e),0)}}}return this.mJsonMode?(this.mJobPage.push(Object.assign(Object.assign({},t),{type:Ie.table})),!0):this.Context.drawTable(t)}printImage(t){return this.openPrinter({name:t.printerName||t.deviceName||t.name,deviceId:t.deviceId,autoScan:t.autoScan}).then((e=>e.statusCode!==we.OK?e:("string"==typeof t.image&&(t.src=t.image,t.image=void 0),Promise.resolve(t.image||this.Context.loadImage(t.src||"")).then((e=>{if(!e)return Te.processFailResult({statusCode:we.ERROR_PARAM});const i=!(t.width&&t.height),s=i?e.width:t.width,r=i?e.height:t.height;if(!s&&!r)return Te.processFailResult({statusCode:we.ERROR_PARAM});const n=this.startJob({width:s,height:r,orientation:t.orientation,scaleUnit:i?l.Pix:l.MM});return n?Promise.resolve("function"!=typeof t.onJobCreated||t.onJobCreated(n)).then((()=>(this.drawImage({image:e,width:s,height:r,sx:t.sx,sy:t.sy,swidth:t.swidth,sheight:t.sheight}),this.commitJob(Te.filter_NoFuncAndNone(t))))):Te.processFailResult({statusCode:we.ERROR_JOB_CREATE})}))))).then((e=>e.statusCode!==we.OK?Te.processFailResult(e,t):Te.processSuccessResult(e,t)))}pushImageData(t){const e=t||{};return t.imageData||t.data?("boolean"!=typeof e.enableSuperBitmap&&(this.mInitInfo.enableSuperBitmap,1)&&(e.enableSuperBitmap=this.mInitInfo.enableSuperBitmap),this.mPrinter.pushImageData(Object.assign(Object.assign({},e),{onJobComplete:e=>{e.statusCode===we.OK?Te.processSuccessResult(e,t):Te.processFailResult(e,t)}}))):{statusCode:we.ERROR_PARAM}}printImageData(t){u.log("#### 【printImageData】options:");const e=t||{};return"boolean"!=typeof e.enableSuperBitmap&&"boolean"==typeof this.mInitInfo.enableSuperBitmap&&(e.enableSuperBitmap=this.mInitInfo.enableSuperBitmap),this.mPrinter.printImageData(e).then((e=>e.statusCode===we.OK?(u.info("#### 【printImageData.resp】打印成功！ "),Te.processSuccessResult(e,t)):(u.warn(`#### 【printImageData.resp】打印失败, statusCode: ${e.statusCode}, printable: ${this.mPrinter.Printable}`),Te.processFailResult(e,t))))}static getJobPrintResult(t,e,i,s){const r=e.map((t=>t.dataUrl||"")),n=Object.assign(Object.assign({},i),{statusCode:t.statusCode,printable:t.printable,dataUrls:r,previewData:r,pages:e.slice(0),printData:s});return n.resultInfo=n,n}print(t){return e(this,void 0,void 0,(function*(){u.log(t,"#### 【print】options:");const e=t.printerInfo||{};if(this.mPausedPrintInfo)t.continueJob||this.mPrinter.ConnectStatus!==Ge.Paused||this.mPrinter.stopPrint(),this.mPausedPrintInfo=void 0;else{let i;if(t.jobPages&&t.jobPages.length>0?Array.isArray(t.jobPages[0])||(t.jobPages=[t.jobPages]):t.jobPage&&t.jobPage.length>0&&Array.isArray(t.jobPage[0])&&(t.jobPages=t.jobPage,delete t.jobPage),t.content)try{const e=JSON.parse(t.content);qe.isWdfxJob(e)?t.wdfData=e:"object"!=typeof e||Array.isArray(e)||(i=e),delete t.content}catch(e){u.warn("---- content 格式异常，解析失败！"),u.warn(e)}t.wdfData&&(i=qe.updateWdfOptions(t.wdfData,t.domParser),delete t.wdfData),i&&("object"==typeof i.jobInfo&&(t.jobInfo=Object.assign(Object.assign({},i.jobInfo),t.jobInfo||{})),i.jobPages&&i.jobPages.length>0?Array.isArray(i.jobPages[0])||(i.jobPages=[i.jobPages]):i.jobPage&&i.jobPage.length>0&&Array.isArray(i.jobPage[0])&&(i.jobPages=i.jobPage,delete i.jobPage),i.jobPages&&i.jobPages.length>0?t.jobPages&&t.jobPages.length>0?t.jobPages=[...i.jobPages,...t.jobPages]:t.jobPage&&t.jobPage.length>0?(t.jobPages=[...i.jobPages,t.jobPage],delete t.jobPage):t.jobPages=[...i.jobPages]:i.jobPage&&i.jobPage.length>0&&(t.jobPages&&t.jobPages.length>0?t.jobPages=[i.jobPage,...t.jobPages]:t.jobPage&&t.jobPage.length>0?(t.jobPages=[[...i.jobPage,...t.jobPage]],delete t.jobPage):t.jobPages=[i.jobPage]),!t.jobArguments&&i.jobArguments&&(t.jobArguments=i.jobArguments)),t.jobPage&&t.jobPage.length>0&&(!t.jobPages||t.jobPages.length<=0)&&(t.jobPages=[t.jobPage],delete t.jobPage);const s=t.jobPages||[];if(!t.jobInfo||s.length<=0){const e={statusCode:we.ERROR_PARAM};return t.onJobComplete&&t.onJobComplete(e),Te.processFailResult(e)}yield this.Context.autoLoadImage(t)}const i=t.jobInfo,s=t.action||0;1&s||4096&s?i.printMode=!0:2===s?i.jobName=ti.JOB_NAME_PREV:130===s&&(i.jobName=ti.JOB_NAME_TRANS);const r=t.jobInfo;i.jobWidth||("number"==typeof r.width&&r.width>0?i.jobWidth=r.width:r.labelWidth&&(i.jobWidth=1*r.labelWidth)),i.jobHeight||("number"==typeof r.height&&r.height>0?i.jobHeight=r.height:r.labelHeight&&(i.jobHeight=1*r.labelHeight)),"number"!=typeof i.orientation&&("number"==typeof r.printOrientation?i.orientation=r.printOrientation:r.printOrientation&&(i.orientation=1*r.printOrientation));const n=1===s,a=i.printMode||!Re.isPreviewJobName(i.jobName);if(a&&!n){const i=yield this.openPrinter({name:e.printerName||e.name,deviceId:e.deviceId,autoScan:!1});if(i.statusCode!==we.OK)return t.onJobComplete&&t.onJobComplete(i),Te.processFailResult(i);e.printerDPI=this.mPrinter.PrinterDPI}const o=t.onJobCreated||t.jobStarted;if(o){if("number"!=typeof i.jobHeight||i.jobHeight<=0){const e=t.jobPages||[],s=Re.getMargins(i);i.jobHeight=Re.calcPageHeight(e[0],s[2])}const e=this.startJob({width:i.jobWidth||0,height:i.jobHeight,orientation:i.orientation,jobName:i.jobName});if(this.abortJob(),!e){const e={statusCode:we.ERROR_JOB_CREATE};return t.onJobComplete&&t.onJobComplete(e),Te.processFailResult(e)}yield Promise.resolve(o(e,t))}return new Promise((e=>{const s=[],r=[];t.syncMode?this.Context.drawJob(Te.filterAndAssign(t,{onPageComplete:o=>{u.log(`#### drawJob.onPageComplete: pages = [${o.pageIndex+1} / ${o.printPages}], copies = [${o.copyIndex+1} / ${o.printCopies}]`);let h=o.copyIndex;if(a)return this.pushImageData(Object.assign(Object.assign({},i),{printCopies:i.autoPage?0:i.printCopies,onlyGetRawData:n,imageData:o.imageData,onPageComplete:e=>{h=i.autoPage?o.copyIndex:e.copyIndex,n&&e.printData&&r.push(e.printData);const a=Object.assign(Object.assign({},o),{pageKey:e.pageKey,statusCode:e.statusCode,printable:e.printable,copyIndex:h,isEndCopy:i.autoPage?o.isEndCopy:h+1>=o.printCopies});s.push(a),t.onPageComplete&&t.onPageComplete(a)},onPagePrintComplete:e=>{h=i.autoPage?o.copyIndex:e.copyIndex,t.onPagePrintComplete&&t.onPagePrintComplete(Object.assign(Object.assign({},o),{pageKey:e.pageKey,statusCode:e.statusCode,printable:e.printable,copyIndex:h,isEndCopy:i.autoPage?o.isEndCopy:h+1>=o.printCopies}));const s=o.isEndCopy&&o.isEndPage&&h+1>=o.printCopies;(e.statusCode!==we.OK||s)&&t.onJobPrintComplete&&t.onJobPrintComplete({statusCode:we.OK,printable:e.printable})},onJobComplete:i=>{if(i.statusCode!==we.OK){if(i.statusCode===we.ERROR_PRINTER_NOT_AVAILABLE){const e=t.jobInfo||{};this.mPausedPrintInfo={printInfo:t,autoPage:e.autoPage,printPages:o.printPages,printCopies:o.printCopies,pageIndex:o.pageIndex,copyIndex:o.copyIndex}}const r=ti.getJobPrintResult(i,s,o);t.onJobComplete&&t.onJobComplete(r),e(r)}else{const n=ti.getJobPrintResult(i,s,o,r);o.isEndCopy&&o.isEndPage&&(h+1>=o.printCopies&&t.onJobComplete&&t.onJobComplete(n),e(n))}}})).statusCode;if(s.push(o),t.onPageComplete&&t.onPageComplete(Object.assign(Object.assign({},o),{statusCode:we.OK,pageKey:0})),o.isEndCopy&&o.isEndPage){const i=ti.getJobPrintResult({statusCode:we.OK},s,o);t.onJobComplete&&t.onJobComplete(i),e(i)}return we.OK}})):this.Context.drawAsyncJob(Te.filterAndAssign(t,{onPageComplete:o=>{let h=o.copyIndex;if(u.log(`#### drawAsyncJob.onPageComplete: pages = [${o.pageIndex+1} / ${o.printPages}], copies = [${o.copyIndex+1} / ${o.printCopies}]`),t.onPageCreated&&t.onPageCreated(o),a){const a=o.imageData;return o.imageData=void 0,this.printImageData(Object.assign(Object.assign({},i),{printCopies:i.autoPage?0:o.printCopies,imageData:a,onlyGetRawData:n,onPageComplete:e=>{h=i.autoPage?o.copyIndex:e.copyIndex,n&&e.printData&&r.push(e.printData);const a=Object.assign(Object.assign({},o),{statusCode:e.statusCode,printable:e.printable,pageKey:e.pageKey,copyIndex:h,isEndCopy:i.autoPage?o.isEndCopy:h+1>=o.printCopies});s.push(a),t.onPageComplete&&t.onPageComplete(a)},onPagePrintComplete:e=>{h=i.autoPage?o.copyIndex:e.copyIndex,t.onPagePrintComplete&&t.onPagePrintComplete(Object.assign(Object.assign({},o),{statusCode:e.statusCode,printable:e.printable,pageKey:e.pageKey,copyIndex:h,isEndCopy:i.autoPage?o.isEndCopy:h+1>=o.printCopies}));const s=o.isEndCopy&&o.isEndPage&&h+1>=o.printCopies;(e.statusCode!==we.OK||s)&&t.onJobPrintComplete&&t.onJobPrintComplete({statusCode:e.statusCode,printable:e.printable})},onJobComplete:i=>{if(i.statusCode!==we.OK){if(i.statusCode===we.ERROR_PRINTER_NOT_AVAILABLE){const e=t.jobInfo||{};this.mPausedPrintInfo={printInfo:t,autoPage:e.autoPage,printPages:o.printPages,printCopies:o.printCopies,pageIndex:o.pageIndex,copyIndex:o.copyIndex}}const r=ti.getJobPrintResult(i,s,o);t.onJobComplete&&t.onJobComplete(r),e(r)}else{const n=ti.getJobPrintResult(i,s,o,r);o.isEndCopy&&o.isEndPage&&(h+1>=o.printCopies&&t.onJobComplete&&t.onJobComplete(n),e(n))}}})).then((t=>t.statusCode))}if(s.push(o),t.onPageComplete&&t.onPageComplete(Object.assign(Object.assign({},o),{statusCode:we.OK,pageKey:0})),o.isEndCopy&&o.isEndPage){const i=ti.getJobPrintResult({statusCode:we.OK},s,o);t.onJobComplete&&t.onJobComplete(i),e(i)}return we.OK}}))}))}))}continuePrint(){return e(this,void 0,void 0,(function*(){const t=this.mPausedPrintInfo;if(yield this.mPrinter.continuePrint(),!t||!t.printInfo.jobInfo)return!0;const e=t.printInfo,i=e.jobInfo,s=t.pageIndex,r=t.copyIndex,n=t.printPages,a=t.printCopies,o=a-r-1;if(t.autoPage){const t=n-s-1;if(t>0&&t<n&&(yield this.print(Object.assign(Object.assign({},e),{continueJob:!0,jobInfo:Object.assign(Object.assign({},i),{autoPage:!0,startCopy:r,printCopies:1,remainCopies:o,startPage:s+1,printPages:t})}))).statusCode!==we.OK)return!1;if(a>r+1)return(yield this.print(Object.assign(Object.assign({},e),{continueJob:!0,jobInfo:Object.assign(Object.assign({},i),{autoPage:!0,startCopy:r+1,printCopies:o,remainCopies:0,startPage:0,printPages:n})}))).statusCode===we.OK}else if(n>s+1)return(yield this.print(Object.assign(Object.assign({},e),{continueJob:!0,jobInfo:Object.assign(Object.assign({},i),{printCopies:a,startPage:s+1,printPages:n-s-1})}))).statusCode===we.OK;return!0}))}printWdfx(t){return e(this,void 0,void 0,(function*(){if(u.log("#### 【printWdfx】options:"),!t.content||"string"!=typeof t.content)return u.warn("---- 未检测到 wdfx 字符串！"),u.warn(t.content),Te.processFailResult({statusCode:we.ERROR_PARAM});const e=qe.loadWdfxContent(t.content,t.domParser);return e?this.print(Object.assign(Object.assign({},t),{wdfData:e,content:void 0})):(u.warn("---- wdfx内容解析错误, content:"),u.warn(t.content.length>200?`${t.content.substring(0,200)}...`:t.content),Te.processFailResult({statusCode:we.ERROR_DATA_PARSE}))}))}quit(){return this.closePrinter().then((()=>(this.mInitInfo.onQuit&&this.mInitInfo.onQuit(),this.stopPrint(),this.abortJob(),this.mPausedPrintInfo=void 0,this.mContext=void 0,this.mPrinter.quit(),!0)))}}ti.trades="",ti.patternGeneral=/^(.*)-([A-Z]{0,2}[0-9]{4,5}[0-9A-Z]{2,5}[0-9]{2})$/,ti.JOB_NAME_TRANS="#!#transparent#!#",ti.JOB_NAME_PREV="#!#preview#!#",ti.JOB_NAME_PRINT="LPAPI",ti.COLOR_WHITE="#fff";class ei extends Re{static createInstance(t){const e=t||{};try{let t=e.canvas;return t||("function"==typeof wx.createOffscreenCanvas?(t=wx.createOffscreenCanvas({type:"2d"}),u.info("=================================="),u.info("==== OffscreenCanvas创建成功！ ===="),u.info("==================================")):(u.error("================================"),u.error("==== 真机调试请使用 2.0 模式 ===="),u.error("================================"))),t?new ei({canvas:t}):void 0}catch(t){return void u.warn(t,"---- failed to create WXContext instance!")}}constructor(t){super({apiMode:!0}),this.mOptions=t||{}}createCanvas(){const t=this.mOptions;return this.mCanvas||(t.canvas?this.mCanvas=t.canvas:(u.error("================================"),u.error("==== 未检测到目标 Canvas 对象 ===="),u.error("================================"))),this.mCanvas}createImage(t,e,i){return new Promise((s=>{const r=e.startsWith("http")?`${e}?${(new Date).getTime()}${Math.random()}`:e,n=t.createImage(),a=r.length>100?`${r.substring(0,100)}...`:r;let o=!1;u.info(`---- canvas.createImage: ${a}`),n.onload=()=>{u.info("---- image.onload"),o?u.warn("图片加载时间太长了，已经进行超时处理了！"):(o=!0,s(n))},n.onerror=t=>{u.warn(t,`---- image.onerror：${a}`),o=!0,s(r)},setTimeout((()=>{o||(u.warn("---- 图片加载超时："),o=!0,s(""))}),i||3e3),n.src=r}))}loadImage(e){const i=Object.create(null,{loadImage:{get:()=>super.loadImage}});return t(this,void 0,void 0,(function*(){u.log("---- WXContext.loadImage:");const t="string"==typeof e?{src:e}:e;if(!t||!t.src)return null;const s=this.createCanvas();return s?this.createImage(s,t.src,0):i.loadImage.call(this,t.src)}))}startJob(t){return u.log("#### WXContext.startJob:"),super.startJob(t)}commitJob(){return super.commitJob().then((t=>t))}}class ii{static getInstance(){return this._instance||(this._instance=new ii)}constructor(){this.mShowWriteLog=!1,"function"==typeof wx.getSystemInfo&&wx.getSystemInfo({complete:t=>{this.mSystemInfo=t}})}isAndroid(){var t;return"android"===((null===(t=this.mSystemInfo)||void 0===t?void 0:t.platform)||"").toLowerCase()}openBleAdapter(){return new Promise((t=>{this.mIsBleAdapterOpened?t(!0):wx.openBluetoothAdapter({success:()=>{this.mIsBleAdapterOpened=!0,u.info("#### 【Response.success】蓝牙适配器打开成功！"),t(!0)},fail:e=>{e.errCode<=0?(u.info("#### 【Response.fail】蓝牙适配器已打开！"),t(!0)):(u.warn("#### 【Response.fail】蓝牙适配器打开失败！"),u.warn(JSON.stringify(e)),t(!1))}})}))}closeBleAdapter(){return new Promise((t=>{wx.closeBluetoothAdapter({fail:t=>{u.warn(t,"#### 【Response.fail】wx.closeBluetoothAdapter")},complete:()=>{this.mIsBleAdapterOpened=!1,this.mConnectionStateChange&&this.mConnectionStateChange({deviceId:this.mDeviceId||"",connected:!1}),t(!0)}})}))}resetBluetoothAdapter(t){return this.closeBleAdapter().then((()=>this.openBleAdapter())).then((e=>(t&&t(e),e)))}startBleDiscovery(t){const e=t||{};return this.openBleAdapter().then((t=>t?(u.info("### 【Request】 wx.startBluetoothDevicesDiscovery: "),new Promise((t=>{wx.startBluetoothDevicesDiscovery({interval:e.interval||200,allowDuplicatesKey:!0,success:i=>{this.mDeviceFoundAction=e.deviceFound,this.mBleAdapterStateChange=e.adapterStateChange,u.info("#### 【Response.success】: wx.startBluetoothDevicesDiscovery:"),wx.onBluetoothDeviceFound((t=>{const e=(!t.devices&&Array.isArray(t)?t:t.devices||[]).filter((t=>t.name||t.localName));e.length>0&&this.mDeviceFoundAction&&this.mDeviceFoundAction(e)})),wx.onBluetoothAdapterStateChange((t=>{u.info("==== onBluetoothAdapterStateChange: ===="),u.info(JSON.stringify(t)),this.mBleAdapterStateChange&&this.mBleAdapterStateChange(t)})),Te.processSuccessResult({statusCode:0,resultInfo:i},e,t)},fail:i=>{u.error("#### 【Response.fail】: wx.startBluetoothDevicesDiscovery:"),u.error(JSON.stringify(i)),Te.processFailResult({statusCode:i.errCode,resultInfo:i},e,t)}})}))):(u.error("========== 蓝牙适配器打开失败！ =========="),Te.processFailResult({statusCode:1,resultInfo:"蓝牙适配器打开失败！"},e))))}stopBleDiscovery(){return new Promise((t=>{u.info("###【Request】wx.stopBluetoothDevicesDiscovery:"),wx.stopBluetoothDevicesDiscovery({success:e=>{u.info("###【Response.success】stopBluetoothDevicesDiscovery:"),t(e),"function"==typeof wx.offBluetoothDeviceFound&&(u.info("#### wx.offBluetoothDeviceFound:"),wx.offBluetoothDeviceFound(),this.mDeviceFoundAction=void 0),"function"==typeof wx.offBluetoothAdapterStateChange&&(u.info("#### wx.offBluetoothAdapterStateChange:"),wx.offBluetoothAdapterStateChange(),this.mBleAdapterStateChange&&this.mBleAdapterStateChange({discovering:!1}),this.mBleAdapterStateChange=void 0)},fail:e=>{u.warn("###【Response.fail】stopBluetoothDevicesDiscovery:"),u.warn(JSON.stringify(e)),t(e)}})}))}createBleConnection(t){const e=t.deviceId||this.mDeviceId||"";return u.info("###【Request】createBleConnection:"),new Promise((i=>{wx.createBLEConnection({deviceId:e,success:()=>{u.info("---- 【Response.success】 createBLEConnection:"),this.mDeviceId=e,this.mConnected=!0,this.mConnectionStateChange=t.connectionStateChange,wx.onBLEConnectionStateChange((t=>{u.info("========== onBLEConnectionStateChange =========="),u.info(JSON.stringify(t)),t.deviceId===this.mDeviceId&&(this.mConnected=t.connected),t.deviceId===e&&this.mConnectionStateChange&&this.mConnectionStateChange(t)})),setTimeout((()=>{Te.processSuccessResult(we.OK,t,i)}),300)},fail:e=>{u.warn("---- 【Response.fail】 createBLEConnection:"),u.warn(JSON.stringify(e)),Te.processFailResult(e.errCode,t,i)}})}))}closeBleConnection(t){const e=t.deviceId||this.mDeviceId||"";return new Promise((i=>{u.info("---- wx.offBLEConnectionStateChange:"),wx.offBLEConnectionStateChange(),u.info("###【Request】closeBleConnection:"),e?this.mIsBleAdapterOpened&&this.mConnected?wx.closeBLEConnection({deviceId:e,success:()=>{u.log("####【Response.success】- wx.closeBLEConnection:"),Te.processSuccessResult(we.OK,t,i)},fail:e=>{u.error("####【Response.fail】- wx.closeBLEConnection:"),u.error(JSON.stringify(e)),Te.processFailResult(e.errCode,t,i)}}):(u.info("#### 设备未连接！"),Te.processSuccessResult(we.OK,t,i)):(u.warn("---- 【参数错误】- closeBleConnection:"),u.warn(JSON.stringify(t)),Te.processFailResult(we.ERROR_PARAM,t,i))}))}getConnectedBleDevices(){return new Promise((t=>{this.openBleAdapter().then((()=>{wx.getConnectedBluetoothDevices({services:[],success:e=>{u.info(e,"#### 【Response.success】: getConnectedBluetoothDevices"),t(e?e.devices:[])},fail:e=>{u.info(e,"#### 【Response.fail】: getConnectedBluetoothDevices"),t([])}})}))}))}setBleMtu(t){const e=t.deviceId||this.mDeviceId||"";return new Promise((i=>{u.info(`#### 【Request: setMTU】: { deviceId: ${e}, mtu: ${t.mtu}}`),this.isAndroid()?t.chipType&&!t.required?i({status:-1}):"function"==typeof wx.setBLEMTU?wx.setBLEMTU({deviceId:e,mtu:t.mtu,success:e=>{u.info("#### 【Response.success】: setBleMTU"),u.info(JSON.stringify(e)),i({status:0,mtu:e.mtu||t.mtu})},fail:t=>{u.warn("#### 【Response.fail】 setBLEMtu: MTU协商失败！"),u.warn(JSON.stringify(t)),i({status:2})}}):(u.warn("不支持的方法： wx.setBLEMTU"),i({status:1})):i({status:-1})}))}getGATTServices(t){const e=t.deviceId||this.mDeviceId||"";return new Promise((i=>{u.info(`#### 【Request】getGattService: ${e}`),wx.getBLEDeviceServices({deviceId:e,success:s=>{u.info(`#### 【Response.success】getGattService: ${e}`);const r=(s.services||[]).map((t=>Object.assign(t,{uuid:t.uuid||t.serviceId||""})));r.length<=0&&u.warn(`---- 服务列表为空：${JSON.stringify(s)}`),Te.processSuccessResult(r,t,i)},fail:s=>{u.warn(`#### 【Response.fail】getGattService: ${e}`),u.warn(JSON.stringify(s)),Te.processFailResult([],t,i)}})}))}getGATTCharacteristics(t){const e=t.deviceId||this.mDeviceId||"";return new Promise((i=>{u.info(`#### 【Request】getCharacteristicList(${t.serviceId})`),wx.getBLEDeviceCharacteristics({deviceId:e,serviceId:t.serviceId||"",success:e=>{u.info(`#### 【Response.success】getCharacteristicList(${t.serviceId})`);const s=e.characteristics||[];s.length<=0&&u.warn(`---- 特征值列表为空：${JSON.stringify(e)}`);const r=s.map((e=>{const i=e.properties||{};let s=0;return i.read&&(s|=Ve.READ),i.write&&(i.writeNoResponse?s|=Ve.WRITE_NO_RESPONSE:s|=Ve.WRITE),i.notify&&(s|=Ve.NOTIFY),i.indicate&&(s|=Ve.INDICATE),Object.assign(e,{serviceId:t.serviceId,uuid:e.uuid||e.characteristicId||"",properties:s})}));Te.processSuccessResult(r,t,i)},fail:e=>{u.warn(`#### 【Response.fail】getCharacteristicList(${t.serviceId})`),u.warn(JSON.stringify(e)),Te.processFailResult([],t,i)}})}))}readCharacteristic(t){return new Promise((e=>{const i=t.deviceId||this.mDeviceId||"",s=t.characteristicId||"";u.info(`#### 【Request】readCharacteristic(uuid: ${s})`),i&&t.serviceId&&s?(wx.onBLECharacteristicValueChange((i=>{u.info(`#### 【Response】onReadCharacteristicValueChange: [${i.value}]`),i.characteristicId==s&&(Te.processSuccessResult(i.value,t,e),wx.offBLECharacteristicValueChange())})),wx.readBLECharacteristicValue({deviceId:i,serviceId:t.serviceId,characteristicId:s,success:t=>{const e=t;u.info(e,`#### 【Response.success】wx.readBLECharacteristicValue: [${s}]`)},fail:i=>{u.error(`---- 【Response.fail】wx.readBLECharacteristicValue: [${s}]`),u.error(JSON.stringify(i)),Te.processFailResult("",t,e)}})):(u.error("---- 【参数错误】: readCharacteristic"),u.error(JSON.stringify(t)),Te.processFailResult("",t,e))}))}notifyCharacteristic(t){const e=t.deviceId||this.mDeviceId;return new Promise((i=>{const s="boolean"!=typeof t.state||t.state;s?u.info("#### 【Request.start】 - notifyCharacteristic:"):("function"==typeof wx.offBLECharacteristicValueChange&&(u.info("#### wx.offBLECharacteristicValueChange:"),wx.offBLECharacteristicValueChange()),u.info("#### 【Request.stop 】 - notifyCharacteristic:")),this.mConnected&&e&&t.serviceId&&t.characteristicId?wx.notifyBLECharacteristicValueChange({deviceId:e,serviceId:t.serviceId,characteristicId:t.characteristicId,state:s,success:()=>{s&&(u.info("#### wx.onBLECharacteristicValueChange:"),wx.onBLECharacteristicValueChange((e=>{u.log(`#### 【Response】onNotifyCharacteristicValueChange: [${e.value}]`),t.characteristicId===e.characteristicId&&t.dataReceived&&t.dataReceived(e.value)}))),u.info("#### 【Response.success】: wx.notifyBLECharacteristicValueChange:"),Te.processSuccessResult(!0,t,i)},fail:e=>{u.error(e,"#### 【Response.fail】: wx.notifyBLECharacteristicValueChange:"),Te.processFailResult(!1,t,i)}}):(this.mConnected?(u.error("#### 【参数错误】: notifyCharacteristic"),u.error(t)):u.warn("#### 【设备链接已断开】: notifyCharacteristic"),Te.processFailResult(!1,t,i))}))}writeCharacteristic(t){const e=t.deviceId||this.mDeviceId||"",i=t.serviceId||"",s=t.characteristicId||"",r=t.data,n=`[P ${t.pkgIndex} - SUP ${t.subPkgIndex}]`;return this.mShowWriteLog&&u.log(`#### 【Request】writeBLECharacteristicValue: ${n}`),new Promise((a=>{e&&i&&s&&r?wx.writeBLECharacteristicValue({deviceId:e,serviceId:i,characteristicId:s,value:r.buffer,success:e=>{this.mShowWriteLog&&u.log(e,`#### 【Response.success】wx.writeBLECharacteristicValue: ${n}`),Te.processSuccessResult({statusCode:0,resultInfo:e},t,a),this.mShowWriteLog=!1},fail:e=>{u.error(e,`#### 【Response.fail】wx.writeBLECharacteristicValue: ${n}`),u.error(t),Te.processFailResult({statusCode:e.errCode,resultInfo:e},t,a),this.mShowWriteLog=!0}}):(u.error("---- 【参数错误】:"),u.error(`---- deviceId         : ${e}`),u.error(`---- serviceId        : ${t.serviceId}`),u.error(`---- characteristicId : ${t.characteristicId}`),Te.processFailResult({statusCode:1,resultInfo:"参数错误"},t,a))}))}}const si=ii.getInstance();class ri{static setLogLevel(t){"number"==typeof t?u.setLevel(t):u.setLevel(t?3:0)}static getInstance(t){const e=t||{};return void 0!==e.showLog&&ri.setLogLevel(e.showLog),u.info(e,"======== LPAPIFactory.getInstance() ========"),ti.getInstance(Object.assign(Object.assign({},e),{adapter:si,createContext:t=>ei.createInstance(t)}))}static createInstance(t){const e=t||{};return void 0!==e.showLog&&ri.setLogLevel(e.showLog),u.info(e,"======== LPAPIFactory.getInstance() ========"),ti.create(Object.assign(Object.assign({},e),{adapter:si,createContext:t=>ei.createInstance(t)}))}}export{o as Alignment,I as Barcode,Bt as Barcode1DEncoder,be as Barcode2DEncoder,Pe as Barcode2DType,r as BarcodeTextPos,A as BarcodeType,C as BitMatrix,h as BorderAlign,Ve as CharacteristicProperty,ye as DegreeContent,Re as DrawContext,Ie as DrawType,p as DzCanvas,b as DzChar,P as DzCharCodes,ze as DzPrinter,ve as DzTextDecoder,y as DzTextEncoder,i as DzUtil,Mt as EccLevel,fe as FinderPattern,a as FontStyle,xe as GBKUtils,s as LOG_LEVEL,ti as LPAPI,ri as LPAPIFactory,Te as LPAUtils,we as LPA_Result,Ee as LabelContext,d as LineSpaceMode,g as LineSpaceParser,Ce as QRCode,Nt as QRUtils,m as ScaleCanvas,l as ScaleUnit,n as TextDecoration,qe as WdfxParser,c as WrapMode,u as logger};

/**index.wxss**/
page,
.app {
    width: 100%;
    height: 100%;
}

.app {
    display: flex;
    flex-direction: column;
}

.app .app-header {
    height: auto;
}

.app .app-main {
    flex: 1;
}

.app .app-footer {
    height: auto;
}

.btn {
    margin: 0.5em 1em;
}

.section-group {
    margin: 1em;
}

.picker {
    padding: 5px 13px;
    background-color: #FFFFFF;
}

.print-canvas {
    position: fixed;
    left: -999999rpx;
    top: -999999rpx;
}

.preview-image {
    border: lightgray;
}
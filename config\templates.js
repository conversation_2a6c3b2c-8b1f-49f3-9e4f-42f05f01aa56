/**
 * 标签模板配置文件
 * 适配德佟P2打印机的模板数据
 * 德佟P2特点：自动适配打印间隙，不支持手动设置Gap
 */

const defaultTemplates = [
  {
    "TemplateName": "40 * 30 mm",
    "Width": 40,
    "Height": 30,
    "Rotate": 0,
    "Copies": 1,
    "Density": 2,
    "HorizontalNum": 0,
    "VerticalNum": 0,
    "PaperType": 1,
    "Gap": 0, // 德佟P2自动适配间隙，设置为0表示自动
    "Speed": 25,
    "FirstCut": 1,
    "CutType": 1,
    "DeviceSn": "P2", // 德佟P2设备标识
    "ImageWidth": 40,
    "ImageHeight": 30,
    "PreviewPath": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/static/blank.png",
    // 德佟P2特有属性
    "AutoGap": true, // 自动间隙检测
    "PrinterModel": "DETONG_P2", // 打印机型号
    "SupportedSizes": ["40x30"], // 支持的尺寸
    "DrawObjects": [
      {
        "AntiColor": false,
        "X": 2,
        "Y": 6,
        "Width": 35,
        "Height": 4,
        "Content": "品名：____________",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 1,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 14,
        "Y": 5,
        "Width": 28,
        "Height": 4,
        "Content": "品名",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 1,
        "FontSize": "5",
        "Format": "TEXT",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 2,
        "Y": 14,
        "Width": 36,
        "Height": 4,
        "Content": "操作员：__________",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 1,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 18,
        "Y": 13,
        "Width": 22,
        "Height": 4,
        "Content": "操作人",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 1,
        "FontSize": "5",
        "Format": "TEXT",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 2,
        "Y": 22,
        "Width": 36,
        "Height": 4,
        "Content": "日期：_____________",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 1,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 14,
        "Y": 22,
        "Width": 24,
        "Height": 4,
        "Content": "2025-06-17",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 1,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0
      }
    ]
  },
  {
    "TemplateName": "50 * 30 mm",
    "Width": 50,
    "Height": 30,
    "Rotate": 0,
    "Copies": 1,
    "Density": 2,
    "HorizontalNum": 0,
    "VerticalNum": 0,
    "PaperType": 1,
    "Gap": 3,
    "Speed": 25,
    "FirstCut": 1,
    "CutType": 1,
    "DeviceSn": "P2",
    "ImageWidth": 50,
    "ImageHeight": 30,
    "PreviewPath": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/static/blank.png",
    "DrawObjects": [
      {
        "AntiColor": false,
        "X": 3,
        "Y": 5,
        "Width": 45,
        "Height": 4,
        "Content": "品名：_________________",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 1,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 15,
        "Y": 4,
        "Width": 33,
        "Height": 4,
        "Content": "品名",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 1,
        "FontSize": "5",
        "Format": "TEXT",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 3,
        "Y": 13,
        "Width": 45,
        "Height": 4,
        "Content": "操作员：_______________",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 1,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 19,
        "Y": 12,
        "Width": 30,
        "Height": 4,
        "Content": "操作人",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 1,
        "FontSize": "5",
        "Format": "TEXT",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 3,
        "Y": 21,
        "Width": 45,
        "Height": 4,
        "Content": "日期：_________________",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 1,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 15,
        "Y": 20,
        "Width": 33,
        "Height": 4,
        "Content": "2025-06-17",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 1,
        "FontSize": "5",
        "Format": "TEXT",
        "Orientation": 0
      }
    ]
  },
  {
    "TemplateName": "50 * 30 mm(B)",
    "Width": 50,
    "Height": 30,
    "Rotate": 0,
    "Copies": 1,
    "Density": 2,
    "HorizontalNum": 0,
    "VerticalNum": 0,
    "PaperType": 1,
    "Gap": 6,
    "Speed": 25,
    "FirstCut": 1,
    "CutType": 1,
    "DeviceSn": "P2",
    "ImageWidth": 50,
    "ImageHeight": 30,
    "PreviewPath": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/static/blank.png",
    "DrawObjects": [
      {
        "AntiColor": false,
        "X": 3,
        "Y": 5,
        "Width": 45,
        "Height": 4,
        "Content": "品名：_________________",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 1,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 15,
        "Y": 4,
        "Width": 33,
        "Height": 4,
        "Content": "品名",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 1,
        "FontSize": "5",
        "Format": "TEXT",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 3,
        "Y": 13,
        "Width": 45,
        "Height": 4,
        "Content": "操作员：_______________",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 1,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 19,
        "Y": 12,
        "Width": 30,
        "Height": 4,
        "Content": "操作人",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 1,
        "FontSize": "5",
        "Format": "TEXT",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 3,
        "Y": 21,
        "Width": 45,
        "Height": 4,
        "Content": "日期：_________________",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 1,
        "FontSize": "4",
        "Format": "TEXT",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 15,
        "Y": 20,
        "Width": 33,
        "Height": 4,
        "Content": "2025-06-17",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 1,
        "FontSize": "5",
        "Format": "TEXT",
        "Orientation": 0
      }
    ]
  },
];

/**
 * 获取默认模板列表
 * @returns {Array} 模板数组的深拷贝
 */
function getDefaultTemplates() {
  return JSON.parse(JSON.stringify(defaultTemplates));
}

/**
 * 获取德佟P2兼容的模板列表
 * @returns {Array} 德佟P2兼容的模板数组
 */
function getDetongP2Templates() {
  return defaultTemplates.filter(template =>
    template.PrinterModel === 'DETONG_P2' || !template.PrinterModel
  );
}

/**
 * 根据尺寸获取匹配的模板
 * @param {number} width 宽度(mm)
 * @param {number} height 高度(mm)
 * @returns {Object|null} 匹配的模板或null
 */
function getTemplateBySize(width, height) {
  return defaultTemplates.find(template =>
    template.Width === width && template.Height === height
  );
}

/**
 * 检查模板是否与德佟P2兼容
 * @param {Object} template 模板对象
 * @returns {boolean} 是否兼容
 */
function isDetongP2Compatible(template) {
  if (!template) return false;

  // 德佟P2支持的尺寸
  const supportedSizes = [
    { width: 40, height: 30 },
    { width: 50, height: 30 },
    { width: 60, height: 40 }
  ];

  return supportedSizes.some(size =>
    size.width === template.Width && size.height === template.Height
  );
}

/**
 * 为德佟P2适配模板参数
 * @param {Object} template 原始模板
 * @returns {Object} 适配后的模板
 */
function adaptTemplateForDetongP2(template) {
  const adaptedTemplate = JSON.parse(JSON.stringify(template));

  // 德佟P2特有的适配
  adaptedTemplate.Gap = 0; // 自动间隙
  adaptedTemplate.AutoGap = true;
  adaptedTemplate.PrinterModel = 'DETONG_P2';
  adaptedTemplate.DeviceSn = 'DETONG_P2';

  // 确保密度和速度在合理范围内
  adaptedTemplate.Density = Math.max(1, Math.min(5, adaptedTemplate.Density || 2));
  adaptedTemplate.Speed = Math.max(10, Math.min(50, adaptedTemplate.Speed || 25));

  return adaptedTemplate;
}

/**
 * 获取德佟P2支持的标签尺寸列表
 * @returns {Array} 支持的尺寸列表
 */
function getDetongP2SupportedSizes() {
  return [
    { width: 40, height: 30, name: '40x30mm', description: '标准小标签' },
    { width: 50, height: 30, name: '50x30mm', description: '中等标签' },
    { width: 60, height: 40, name: '60x40mm', description: '大标签' }
  ];
}

module.exports = {
  defaultTemplates,
  getDefaultTemplates,
  getDetongP2Templates,
  getTemplateBySize,
  isDetongP2Compatible,
  adaptTemplateForDetongP2,
  getDetongP2SupportedSizes
};

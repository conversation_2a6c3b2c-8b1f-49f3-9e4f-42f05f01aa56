import { LPAPIFactory, LPAPI, LPAUtils } from "../../js_sdk/lpapi-ble/index";
import { DataMatrix } from "../../libs/dz-datamatrix.esm";
import { PDF417 } from "../../libs/dz-pdf417.esm";
// 注意：微信小程序本身不支持 xml 解析功能，用户可以使用任何符合规范的DOMParser解析工具；
// 当前所用的 dom解析工具是：https://github.com/jindw/xmldom；
import { DOMParser } from "../../libs/dom-parser";
import { wdfxStr } from "./datas";
//获取应用实例
// var app = getApp();
LPAPI.registerBarcode2DEncoder(DataMatrix.getInstance());
LPAPI.registerBarcode2DEncoder(PDF417.getInstance());
//
Page({
    data: {
        canvasId: "lpapi-ble-wx",
        labelWidth: 960,
        labelHeight: 960,
        deviceList: [{ name: "未检测到打印机", deviceId: "" }],
        deviceIndex: 0,
        orientationList: [
            { name: "横向打印", value: 0 },
            { name: "右转90度", value: 90 },
            { name: "旋转180度", value: 180 },
            { name: "左转90度", value: 270 },
        ],
        orientationIndex: 0,
        gapList: [
            { name: "随打印机设置", value: 255 },
            { name: "小票纸", value: 0 },
            { name: "不干胶", value: 2 },
            { name: "卡纸", value: 3 },
        ],
        gapIndex: 0,
        darknessList: [
            { name: "随打印机设置", value: 255 },
            { name: "6 (正常)", value: 6 },
            { name: "7", value: 7 },
            { name: "8", value: 8 },
            { name: "9", value: 9 },
            { name: "10 (较浓)", value: 10 },
            { name: "11", value: 11 },
            { name: "12", value: 12 },
            { name: "13", value: 13 },
            { name: "14", value: 14 },
            { name: "15 (最浓)", value: 15 },
        ],
        darknessIndex: 0,
        speedList: [
            { name: "随打印机设置", value: 255 },
            { name: "最慢", value: 1 },
            { name: "较慢", value: 2 },
            { name: "正常", value: 3 },
            { name: "较快", value: 4 },
            { name: "最快", value: 5 },
        ],
        speedIndex: 0,
        printModes: [
            { title: "打印", value: 0 },
            { title: "预览", value: 1, checked: true },
        ],
        printMode: 1,
        previewImage: "",
        previewList: [],
        threshold: 150,
    },
    onLoad() {
        console.log(`========== onLoad ==========`);
        // 可以不指定Canvas，此时会使用离屏Canvas进行标签绘制操作；
        // this.initApi();
    },

    /**
     * 进入德佟P2打印机Demo页面
     */
    gotoPrinterDemo() {
        console.log('进入德佟P2打印机Demo页面');
        wx.navigateTo({
            url: '/pages/printer/printer',
            success: () => {
                console.log('成功跳转到printer页面');
            },
            fail: (error) => {
                console.error('跳转到printer页面失败:', error);
                wx.showToast({
                    title: '页面跳转失败',
                    icon: 'error'
                });
            }
        });
    },
    onReady() {
        console.log(`========== onReady ==========`);
        // const query = wx.createSelectorQuery();
        // query
        //     .select(`#${this.data.canvasId}`)
        //     .fields({ node: true, size: true })
        //     .exec((res) => {
        //         console.log(`---- querySelector(${this.data.canvasId}) ----`);
        //         console.log(res);
        //         const canvas = res[0].node;
        //         this.initApi(canvas);
        //     });
        this.initApi();
    },
    onHide() {
        // 页面隐藏
        this.closePrinter();
    },
    onDeviceChanged(e) {
        console.log(`--------- onDeviceChanged: ${e.detail.value} ------------`);
        this.setData({ deviceIndex: e.detail.value });
    },
    onOrientationChanged(e) {
        console.log(`--------- onOrientationChanged: ${e.detail.value} ------------`);
        this.setData({ orientationIndex: e.detail.value });
    },
    onGapTypeChanged(e) {
        console.log(`--------- onGapTypeChanged: ${e.detail.value} ------------`);
        this.setData({ gapIndex: e.detail.value });
    },
    onDarknessChanged(e) {
        console.log(`--------- onDarknessChanged: ${e.detail.value} ------------`);
        this.setData({ darknessIndex: e.detail.value });
    },
    onSpeedChanged(e) {
        console.log(`--------- onSpeedChanged: ${e.detail.value} ------------`);
        this.setData({ speedIndex: e.detail.value });
    },
    onPrintModeChanged(e) {
        console.log(`--------- onPrintModeChanged: ${e.detail.value} ------------`);
        this.setData({ printMode: e.detail.value });
    },
    initApi(canvas) {
        this.lpapi = LPAPIFactory.getInstance({
            // 日志信息显示级别，值为 0 - 4，0表示不显示调试信息，4表示显示所有调试信息
            showLog: 4,
            canvas: canvas,
            // 用于进行标签绘制的画布ID
            canvasId: this.data.canvasId,
        });
        // 搜索蓝牙设备
        this.lpapi.startBleDiscovery({
            timeout: 0,
            deviceFound: (devices) => {
                this.onDeviceFound(devices);
            },
        });
    },
    startDiscovery() {
        wx.showLoading({
            title: "正在搜索打印机...",
        });
        this.lpapi.startBleDiscovery({
            timeout: 5000,
            deviceFound: (devices) => {
                this.onDeviceFound(devices);
            },
            adapterStateChange: (result) => {
                if (!result.discovering) {
                    wx.hideLoading();
                }
            },
        });
    },
    stopDiscovery() {
        this.lpapi.stopBleDiscovery();
    },
    getDevice() {
        return this.data.deviceList[this.data.deviceIndex];
    },
    openPrinter(callback) {
        const currDevice = this.getDevice();
        if (currDevice && currDevice.deviceId) {
            wx.showLoading({
                title: "正在链接打印机...",
            });
            this.lpapi.openPrinter({
                name: currDevice.name,
                deviceId: currDevice.deviceId,
                success: (resp) => {
                    console.log(`---- 【打印机链接成功】`);
                    wx.hideLoading();
                    wx.showToast({ title: "打印机链接成功！", icon: "success" });
                    if (typeof callback === "function") {
                        callback(true);
                    }
                },
                fail: (resp) => {
                    console.warn(`---- 【打印机链接失败】`);
                    console.warn(JSON.stringify(resp));
                    wx.hideLoading();
                    wx.showToast({ title: "打印机链接失败！", icon: "fail" });
                    if (typeof callback === "function") {
                        callback(false);
                    }
                },
            });
        } else {
            console.warn("---- 未检测到打印机！");
            wx.showToast({ title: "未检测到打印机", icon: "fail" });
            if (typeof callback === "function") {
                callback(false);
            }
        }
    },
    closePrinter() {
        console.log(`---- 关闭打印机！`);
        this.lpapi.closePrinter();
    },
    onDeviceFound(devices) {
        console.log(`---- 检测到打印机：`);
        console.log(devices);
        for (const item of devices) {
            console.log(`-----------------------:`);
            console.log(item);
            // advertisData
            // serviceData
            const advertisData = new Uint8Array(item.advertisData);
            // console.log(`advertisData: [${advertisData.map(val => `00${val.toString(16)}`.slice(-2)).join(',')}]`);
            console.log(`advertisData: [${LPAUtils.getHexStringOfBytes(advertisData)}]`);
            if(item.serviceData) {
                console.log(`------- show serviceData:`);
                for (const key in item.serviceData) {
                    console.log(`serviceData.key = ${key}`);
                    // if (Object.hasOwnProperty.call(item.serviceData, key)) {
                    //     const element = item.serviceData[key];
                    // }
                }
            }
            // const serviceData = item.serviceData;
            // if(typeof serviceData.toString === "function") {
            //     console.log(serviceData.toString());
            // }
            // if(typeof serviceData.valueOf === "function") {
            //     console.log(serviceData.valueOf());
            // }
        }
        if (devices && devices.length > 0) {
            this.setData({
                deviceList: devices,
            });
        }
    },
    getJobName() {
        if (this.data.printMode > 0) {
            // return "#!#transparent#!#";
            return "#!#preview#!#";
        } else {
            return "lpapi-ble";
        }
    },
    getOrientation() {
        return this.data.orientationList[this.data.orientationIndex].value;
    },
    getGapType() {
        return this.data.gapList[this.data.gapIndex].value;
    },
    getPrintDarkness() {
        return this.data.darknessList[this.data.darknessIndex].value;
    },
    getPrintSpeed() {
        return this.data.speedList[this.data.speedIndex].value;
    },
    getThreshold() {
        return Number(this.data.threshold);
    },
    /**
     * @returns {Promise<boolean>}
     */
    checkAndOpenPrinter(callback) {
        return new Promise((resolve) => {
            if (this.data.printMode > 0) {
                // 预览模式不需要链接打印机
                callback && callback(true);
                resolve(true);
            } else {
                // 打印模式自动链接打印机
                this.openPrinter((res) => {
                    callback && callback(res);
                    resolve(res);
                });
            }
        });
    },
    previewLabel(result) {
        if (result.statusCode === 0 && Array.isArray(result.dataUrls)) {
            console.log(`---- 打印/预览成功！`);
            const imageList = result.dataUrls.map((v) => {
                return {
                    value: v,
                    key: `${Math.random()}`,
                };
            });
            this.setData({
                previewList: imageList,
            });
        } else {
            console.warn(`---- 打印/预览失败， statusCode = ${result.statusCode}`);
            console.warn(result);
        }
    },
    clearPreview() {
        this.setData({
            previewList: [],
        });
    },
    appendPreview(result) {
        const oldList = this.data.previewList;
        if (result.dataUrl) {
            oldList.push({
                value: result.dataUrl,
                key: `${Math.random()}`,
            });
            this.setData({
                previewList: oldList,
            });
        }
    },
    textPrintTest() {
        const api = this.lpapi;
        //
        const labelWidth = 50;
        const labelHeight = 30;
        const text1 = "上海道臻信息技术有限公司http://www.dothantch.com";
        // 将整个标签高度平均分成4个区域，分别显示四种绘制模式
        const cellHeight = labelHeight / 4;
        // 如果打印机未连接，则自动链接打印机
        this.checkAndOpenPrinter(() => {
            let pos = 0;
            // 创建 40mm x 30mm 大小标签的打印任务；
            api.startJob({
                width: labelWidth,
                height: labelHeight,
                jobName: this.getJobName(),
            });
            // 1. 最简单的绘制方式：只需要指定绘制内容和字体高度即可，默认绘制在左上角，字体高度为3毫米；
            api.drawText({
                text: text1,
                fontHeight: 3,
            });
            // 分割线
            pos += cellHeight;
            api.drawLine({
                x1: 0,
                y1: pos,
                x2: labelWidth,
                y2: pos,
                lineWidth: 0.3,
            });
            // 2. 通过指定绘制区域的宽度，来进行换行处理；
            api.drawText({
                text: text1,
                x: 0,
                y: pos,
                width: labelWidth,
                fontHeight: 3,
            });
            // 分割线
            pos += cellHeight;
            api.drawLine({
                x1: 0,
                y1: pos,
                x2: labelWidth,
                y2: pos,
                lineWidth: 0.3,
            });
            // 3. 不要自动换行，通过压缩方式将数据显示到一行
            api.drawText({
                text: text1,
                x: 0,
                y: pos,
                width: labelWidth,
                autoReturn: 0,
                fontHeight: 3,
            });
            // 分割线
            pos += cellHeight;
            api.drawLine({
                x1: 0,
                y1: pos,
                x2: labelWidth,
                y2: pos,
                lineWidth: 0.3,
            });
            // 4. 当文本内容超多的时候，可以通过指定显示区域的高度，来进行字体的自动缩小处理；如果不需要自动缩小，可以不指定宽度；
            api.drawText({
                text: text1,
                x: 0,
                y: pos,
                width: labelWidth,
                height: cellHeight,
                fontHeight: 3,
            });
            //
            api.commitJob({
                gapType: this.getGapType(),
                darkness: this.getPrintDarkness(),
            }).then((resp) => {
                this.previewLabel(resp);
            });
        });
    },
    textPrintTest2() {
        const api = this.lpapi;
        //
        // 模板数据
        const template = {
            "TemplateName": "50 * 30 mm",
            "Width": 50,
            "Height": 30,
            "Density": 2,
            "Gap": 3,
            "Speed": 25,
            "DeviceSn": "T0145B",
            "DrawObjects": [
                {
                    "X": 2, "Y": 5, "Width": 45, "Height": 4,
                    "Content": "品名：_________________",
                    "FontSize": 4, "FontName": "", "FontStyle": 1
                },
                {
                    "X": 14, "Y": 4, "Width": 33, "Height": 4,
                    "Content": "测试内容", "FontSize": 5,
                    "FontName": "", "FontStyle": 1
                },
                {
                    "X": 2, "Y": 13, "Width": 45, "Height": 4,
                    "Content": "操作员：_______________",
                    "FontSize": 4, "FontName": "", "FontStyle": 1
                },
                {
                    "X": 18, "Y": 12, "Width": 30, "Height": 4,
                    "Content": "操作人", "FontSize": 5,
                    "FontName": "", "FontStyle": 1
                },
                {
                    "X": 2, "Y": 21, "Width": 45, "Height": 4,
                    "Content": "日期：_________________",
                    "FontSize": 4, "FontName": "", "FontStyle": 1
                },
                {
                    "X": 14, "Y": 20, "Width": 33, "Height": 4,
                    "Content": "2025-07-10", "FontSize": 5,
                    "FontName": "", "FontStyle": 1
                }
            ]
        };

        // 设置支持的打印机型号
        // api.setSupportPrefixes(["T0145B"]);

        if(this.data.printMode > 0){
            // 创建打印任务
            api.startJob({
                width: template.Width,
                height: template.Height,
                jobName: this.getJobName(), // 示例：返回 "label_20250617"
                speed: template.Speed,
                gap: template.Gap,
                density: template.Density
            });

            // 绘制所有文本元素
            template.DrawObjects.forEach(item => {
                api.drawText({
                    text: item.Content,
                    x: item.X,
                    y: item.Y,
                    width: item.Width,
                    // height: item.Height,
                    fontHeight: item.FontSize,
                    fontName: item.FontName,
                    fontStyle: item.FontStyle,
                    // 可选参数
                    autoReturn: 1,
                    lineSpace: 0,
                    charSpace: 0
                });
            });

            // 提交打印任务
            api.commitJob({
                gapType: this.getGapType(), // 示例：返回 2（间隙纸）
                darkness: this.getPrintDarkness(), // 示例：返回 6（正常浓度）
                printSpeed: template.Speed
            }).then((resp) => {
                // 预览或打印成功处理
                if (resp.statusCode === 0) {
                    console.log("打印成功");
                    this.previewLabel(resp);
                } else {
                    console.error("打印失败", resp);
                }
            }).catch(err => {
                console.error("提交任务失败", err);
            });
        }else{
            // 搜索并连接打印机
            this.checkAndOpenPrinter(() => {
                // 创建打印任务
                api.startJob({
                    width: template.Width,
                    height: template.Height,
                    jobName: this.getJobName(), // 示例：返回 "label_20250617"
                    speed: template.Speed,
                    gap: template.Gap,
                    density: template.Density
                });

                // 绘制所有文本元素
                template.DrawObjects.forEach(item => {
                    api.drawText({
                        text: item.Content,
                        x: item.X,
                        y: item.Y,
                        width: item.Width,
                        // height: item.Height,
                        fontHeight: item.FontSize,
                        fontName: item.FontName,
                        fontStyle: item.FontStyle,
                        // 可选参数
                        autoReturn: 1,
                        lineSpace: 0,
                        charSpace: 0
                    });
                });

                // 提交打印任务
                api.commitJob({
                    gapType: this.getGapType(), // 示例：返回 2（间隙纸）
                    darkness: this.getPrintDarkness(), // 示例：返回 6（正常浓度）
                    printSpeed: template.Speed
                }).then((resp) => {
                    // 预览或打印成功处理
                    if (resp.statusCode === 0) {
                        console.log("打印成功");
                        this.previewLabel(resp);
                    } else {
                        console.error("打印失败", resp);
                    }
                }).catch(err => {
                    console.error("提交任务失败", err);
                });
            });
        }

        
    },
    qrcodePrintTest(data, callback) {
        const api = this.lpapi;
        const labelWidth = 40;
        const labelHeight = 30;
        const margin = 2;
        // 二维码底部文本的高度
        const textHeight = 4;
        // 二维码大小
        const codeWidth = labelHeight - margin * 2 - textHeight;
        const text = typeof data === "string" ? data : "8888888888888888";
        //
        this.checkAndOpenPrinter(() => {
            // 创建 40mm x 30mm 大小的标签纸
            api.startJob({
                width: labelWidth,
                height: labelHeight,
                jobName: this.getJobName(),
            });
            // 绘制二维码
            api.draw2DQRCode({
                text: text,
                x: (labelWidth - codeWidth) * 0.5,
                y: margin,
                width: codeWidth,
            });
            // 以文本形式，将二维码内容绘制到二维码底部
            api.drawText({
                text: text,
                x: 0,
                y: margin + codeWidth,
                width: labelWidth,
                height: textHeight,
                fontHeight: 3.5,
                horizontalAlignment: this.isLark ? 0 : 1,
            });
            //
            api.commitJob({
                gapType: this.getGapType(),
                darkness: this.getPrintDarkness(),
            }).then((resp) => {
                this.previewLabel(resp);
                // 执行打印完毕回调函数
                if (typeof callback === "function") {
                    callback(true);
                }
            });
        });
    },
    pdf417PrintTest() {
        const api = this.lpapi;
        //
        const labelWidth = 40;
        const labelHeight = 30;
        const margin = 2;
        const text = "上海道臻信息技术有限公司";
        const textHeight = 4;
        const codeHeight = labelHeight - margin * 2 - textHeight;
        //
        this.checkAndOpenPrinter(() => {
            api.startJob({
                width: labelWidth,
                height: labelHeight,
                jobName: this.getJobName(),
            });
            //
            api.draw2DPdf417({
                text: text,
                x: margin,
                y: margin,
                width: labelWidth - margin * 2,
                height: codeHeight,
            });
            //
            api.drawText({
                text: text,
                x: 0,
                y: margin + codeHeight,
                width: labelWidth,
                height: textHeight,
                fontHeight: 3.5,
                horizontalAlignment: this.isLark ? 0 : 1,
            });
            //
            api.commitJob({
                gapType: this.getGapType(),
                darkness: this.getPrintDarkness(),
            }).then((resp) => {
                this.previewLabel(resp);
            });
        });
    },
    dataMatrixPrintTest() {
        const api = this.lpapi;
        //
        const labelWidth = 40;
        const labelHeight = 30;
        const textHeight = 4;
        const margin = 2;
        const codeWidth = labelHeight - margin * 2 - textHeight;
        const text = "上海道臻信息技术有限公司";
        //
        this.checkAndOpenPrinter(() => {
            api.startJob({
                width: labelWidth,
                height: labelHeight,
                jobName: this.getJobName(),
            });
            api.draw2DDataMatrix({
                text: text,
                x: (labelWidth - codeWidth) * 0.5,
                y: margin,
                width: codeWidth,
                height: codeWidth,
            });
            api.drawText({
                text: text,
                x: 0,
                y: margin + codeWidth,
                width: labelWidth,
                height: textHeight,
                fontHeight: 3.5,
                horizontalAlignment: this.isLark ? 0 : 1,
            });
            //
            api.commitJob({
                gapType: this.getGapType(),
                darkness: this.getPrintDarkness(),
            }).then((resp) => {
                this.previewLabel(resp);
            });
        });
    },
    barcodePrintTest() {
        const api = this.lpapi;
        //
        const labelWidth = 40;
        const labelHeight = 30;
        const margin = 2;
        //
        this.checkAndOpenPrinter(() => {
            api.startJob({
                width: labelWidth,
                height: labelHeight,
                jobName: this.getJobName(),
            });
            // 绘制外边框
            api.drawRectangle({
                width: labelWidth,
                height: labelHeight,
            });
            api.draw1DBarcode({
                text: "12345678",
                x: margin,
                y: margin,
                width: labelWidth - margin * 2,
                height: labelHeight - margin * 2,
                textHeight: 5,
                type: 22,
            });
            //
            api.commitJob({
                gapType: this.getGapType(),
                darkness: this.getPrintDarkness(),
            }).then((resp) => {
                this.previewLabel(resp);
            });
        });
    },
    localImagePrintTest() {
        const api = this.lpapi;
        const labelWidth = 40;
        const labelHeight = 40;
        const margin = 2;
        const url = "/static/yinlifun.png";
        //
        this.checkAndOpenPrinter(() => {
            api.loadImage(url, (image) => {
                api.startJob({
                    width: labelWidth,
                    height: labelHeight,
                    jobName: this.getJobName(),
                });
                // 绘制标签外边框
                api.drawRectangle({
                    width: labelWidth,
                    height: labelHeight,
                    lineWidth: 0.3,
                });
                // 绘制图片
                api.drawImage({
                    image: image,
                    x: margin,
                    y: margin,
                    width: labelWidth - margin * 2,
                    height: labelHeight - margin * 2,
                });
                //
                api.commitJob({
                    gapType: this.getGapType(),
                    darkness: this.getPrintDarkness(),
                    threshold: this.getThreshold(),
                }).then((resp) => {
                    this.previewLabel(resp);
                });
            });
        });
    },
    remoteImagePrintTest() {
        const api = this.lpapi;
        //
        const labelWidth = 30;
        const labelHeight = 30;
        const margin = 2;
        const url = "https://www.detonger.com/img/QRCode_OfficialAccounts.png";
        //
        this.checkAndOpenPrinter(() => {
            api.loadImage(url, (image) => {
                api.startJob({
                    width: labelWidth,
                    height: labelHeight,
                    jobName: this.getJobName(),
                });
                // 绘制标签外边框
                api.drawRectangle({
                    width: labelWidth,
                    height: labelHeight,
                    lineWidth: 0.3,
                });
                // 绘制图片
                api.drawImage({
                    image: image,
                    x: margin,
                    y: margin,
                    width: labelWidth - margin * 2,
                    height: labelHeight - margin * 2,
                });
                //
                api.commitJob({
                    gapType: this.getGapType(),
                    darkness: this.getPrintDarkness(),
                    threshold: this.getThreshold(),
                }).then((resp) => {
                    this.previewLabel(resp);
                });
            });
        });
    },
    rectanglePrintTest() {
        const api = this.lpapi;
        //
        const labelWidth = 40;
        const labelHeight = 30;
        const itemWidth = labelWidth * 0.5;
        const itemHeight = labelHeight * 0.5;
        const margin = 1;
        const padding = 2;
        const lineWidth = 0.3;
        //
        this.checkAndOpenPrinter(() => {
            api.startJob({
                width: labelWidth,
                height: labelHeight,
                jobName: this.getJobName(),
            });
            // 绘制中间横线
            api.drawLine({
                x1: 0,
                y1: itemHeight,
                x2: labelWidth,
                y2: itemHeight,
                lineWidth: 0.3,
            });
            // 绘制中间竖线(绘制虚线)
            api.drawLine({
                x1: itemWidth,
                y1: 0,
                x2: itemWidth,
                y2: labelHeight,
                lineWidth: 0.3,
                dashLens: [1, 0.5],
            });
            //====================================================//
            //===== 常规矩形测试
            //====================================================//
            // 在左上角绘制矩形框
            api.drawRectangle({
                x: margin,
                y: margin,
                width: itemWidth - margin * 2,
                height: itemHeight - margin * 2,
                lineWidth: lineWidth,
            });
            // 左上角绘制填充矩形
            api.drawRectangle({
                x: margin + padding,
                y: margin + padding,
                width: itemWidth - (margin + padding) * 2,
                height: itemHeight - (margin + padding) * 2,
                fill: true,
            });
            //====================================================//
            //===== 圆角矩形测试
            //====================================================//
            // 在右上角绘制圆角矩形框
            api.drawRectangle({
                x: margin + itemWidth,
                y: margin,
                width: itemWidth - margin * 2,
                height: itemHeight - margin * 2,
                lineWidth: lineWidth,
                cornerWidth: 1.5,
            });
            // 左上角绘制填充矩形
            api.drawRectangle({
                x: margin + padding + itemWidth,
                y: margin + padding,
                width: itemWidth - (margin + padding) * 2,
                height: itemHeight - (margin + padding) * 2,
                fill: true,
                cornerWidth: 1.5,
            });
            //====================================================//
            //===== 椭圆测试
            //====================================================//
            // 在右上角绘制圆角矩形框
            api.drawEllipse({
                x: margin,
                y: margin + itemHeight,
                width: itemWidth - margin * 2,
                height: itemHeight - margin * 2,
                lineWidth: lineWidth,
            });
            // 左上角绘制填充矩形
            api.drawEllipse({
                x: margin + padding,
                y: margin + padding + itemHeight,
                width: itemWidth - (margin + padding) * 2,
                height: itemHeight - (margin + padding) * 2,
                fill: true,
            });
            //====================================================//
            //===== 正圆测试
            //====================================================//
            // 在右下角绘制圆形边框
            api.drawCircle({
                x: itemWidth * 1.5,
                y: itemHeight * 1.5,
                radius: Math.min(itemWidth, itemHeight) * 0.5 - margin,
            });
            // 左下角绘制填充圆形
            api.drawCircle({
                x: itemWidth * 1.5,
                y: itemHeight * 1.5,
                radius: Math.min(itemWidth, itemHeight) * 0.5 - margin - padding,
                fill: true,
            });
            // 提交任务
            api.commitJob({
                gapType: this.getGapType(),
                darkness: this.getPrintDarkness(),
            }).then((resp) => {
                this.previewLabel(resp);
            });
        });
    },
    alignmentPrintTest() {
        const api = this.lpapi;
        //
        const labelWidth = 60;
        const labelHeight = 40;
        const itemWidth = labelWidth * 0.5;
        const itemHeight = labelHeight * 0.5;
        const text = "上海道臻信息技术有限公司";
        const fontHeight = 4;
        // 标签整体右转90度
        const orientation = 90;
        //
        this.checkAndOpenPrinter(() => {
            api.startJob({
                width: labelWidth,
                height: labelHeight,
                orientation: orientation,
                jobName: this.getJobName(),
            });
            // 将正张标签分割成四个显示区域
            api.drawRectangle({
                x: 0,
                y: 0,
                width: labelWidth,
                height: labelHeight,
                lineWidth: 0.3,
            });
            api.drawLine({
                y1: itemHeight,
                x2: labelWidth,
                lineWidth: 0.3,
            });
            api.drawLine({
                x1: itemWidth,
                y2: labelHeight,
                lineWidth: 0.3,
            });
            // 水平居左，垂直居右对齐
            api.drawText({
                text: text,
                x: 0,
                y: 0,
                width: itemWidth,
                height: itemHeight,
                horizontalAlignment: 0,
                verticalAlignment: 0,
                fontHeight: fontHeight,
                // 常规字体样式
                fontStyle: 0,
            });
            // 水平居中，垂直居中
            api.drawText({
                text: text,
                x: itemWidth,
                y: 0,
                width: itemWidth,
                height: itemHeight,
                horizontalAlignment: 1,
                verticalAlignment: 1,
                fontHeight: fontHeight,
                // 字体样式：黑体
                fontStyle: 1,
            });
            // 水平居右，垂直居下
            api.drawText({
                text: text,
                x: itemWidth,
                y: itemHeight,
                width: itemWidth,
                height: itemHeight,
                horizontalAlignment: 2,
                verticalAlignment: 2,
                fontHeight: fontHeight,
                // 字体样式：斜体
                fontStyle: 2,
            });
            // 水平拉伸，垂直拉伸
            api.drawText({
                text: text,
                x: 0,
                y: itemHeight,
                width: itemWidth,
                height: itemHeight,
                horizontalAlignment: 3,
                verticalAlignment: 3,
                fontHeight: fontHeight,
                // 字体样式：粗斜体
                fontStyle: 3,
            });
            //
            api.commitJob({
                gapType: this.getGapType(),
                darkness: this.getPrintDarkness(),
            }).then((resp) => {
                this.previewLabel(resp);
            });
        });
    },
    rotationPrintTest() {
        const api = this.lpapi;
        //
        const labelWidth = 60;
        const labelHeight = 40;
        const itemWidth = labelWidth * 0.5;
        const itemHeight = labelHeight * 0.5;
        const orientation = 90;
        const text = "上海道臻信息技术有限公司";
        //
        this.checkAndOpenPrinter(() => {
            api.startJob({
                width: labelWidth,
                height: labelHeight,
                orientation: orientation,
                jobName: this.getJobName(),
            });
            // 将整个打印区域分割成四个区域，分别进行四种旋转测试
            api.drawRectangle({
                width: labelWidth,
                height: labelHeight,
                lineWidth: 0.3,
            });
            api.drawLine({
                y: itemHeight,
                width: labelWidth,
                height: 0.3,
            });
            api.drawLine({
                x: itemWidth,
                width: 0.3,
                height: labelHeight,
            });
            //
            api.drawText({
                text: text,
                x: 0,
                y: 0,
                width: itemWidth,
                height: itemHeight,
                // 0：表示不旋转
                orientation: 0,
                fontHeight: 4,
            });
            //
            api.drawText({
                text: text,
                x: itemWidth,
                y: 0,
                width: itemWidth,
                height: itemHeight,
                // 90：表示右转90度
                orientation: 90,
                fontHeight: 4,
            });
            //
            api.drawText({
                text: text,
                x: itemWidth,
                y: itemHeight,
                width: itemWidth,
                height: itemHeight,
                // 180：表示旋转180度
                orientation: 180,
                fontHeight: 4,
            });
            //
            api.drawText({
                text: text,
                x: 0,
                y: itemHeight,
                width: itemWidth,
                height: itemHeight,
                // 270：表示左转90度
                orientation: 270,
                fontHeight: 4,
            });
            //
            api.commitJob({
                gapType: this.getGapType(),
                darkness: this.getPrintDarkness(),
            }).then((resp) => {
                this.previewLabel(resp);
            });
        });
    },
    tablePrintTest() {
        const api = this.lpapi;
        //
        const labelWidth = 60;
        const labelHeight = 40;
        const margin = 1.5;
        //
        return this.checkAndOpenPrinter().then(() => {
            api.startJob({
                width: labelWidth,
                height: labelHeight,
                orientation: this.getOrientation(),
                jobName: this.getJobName(),
            });
            // 将整个打印区域分割成四个区域，分别进行四种旋转测试
            api.drawTable({
                x: margin,
                y: margin,
                width: labelWidth - margin * 2,
                height: labelHeight - margin * 2,
                rows: 3,
                columns: 3,
                lineWidth: 0.4,
                // 指定单元格的宽度，未指定或者小于1表示按照一定的比例分配剩余空间
                columnWidths: [18, 0, 25],
                horizontalAlignment: 0,
                cells: [
                    {
                        type: "text",
                        text: "表格打印测试",
                        fontStyle: 1,
                        fontHeight: 3.5,
                        columnSpan: 3,
                        horizontalAlignment: 1,
                    },
                    "",
                    "",
                    "设备名称",
                    "打印机",
                    {
                        type: "qrcode",
                        text: "8888888888",
                        rowSpan: 2,
                    },
                    "设备型号",
                    "DT60",
                    "",
                ],
            });
            //
            api.commitJob({
                gapType: this.getGapType(),
                darkness: this.getPrintDarkness(),
            }).then((resp) => {
                this.previewLabel(resp);
                return resp.statusCode === 0;
            });
        });
    },
    jsonPrintTest() {
        const device = this.getDevice() || {};
        // 有个问题：无法更新画布大小
        const api = this.lpapi;
        this.clearPreview();
        //
        api.print({
            jobInfo: {
                jobWidth: 40,
                jobHeight: 30,
                jobName: this.getJobName(),
                orientation: this.getOrientation(),
                gapType: this.getGapType(),
                printDarkness: this.getPrintDarkness(),
                printSpeed: this.getPrintSpeed(),
            },
            printerInfo: {
                name: device.name,
                deviceId: device.deviceId,
            },
            jobPages: [
                [
                    // 第一张标签，打印一维码
                    { type: "rect", x: 1.5, y: 1.5, width: 40 - 3, height: 30 - 3, lineWidth: 0.4 },
                    {
                        type: "barcode",
                        x: 2,
                        y: 6,
                        width: 36,
                        height: 22,
                        textHeight: 5,
                        barcodeType: 60,
                        horizontalAlignment: 1,
                        text: "www.dothantech.com",
                    },
                ],
                [
                    // 第二章标签，打印二维码
                    { type: "rect", x: 1.5, y: 1.5, width: 40 - 3, height: 30 - 3, lineWidth: 0.4 },
                    {
                        type: "qrcode",
                        x: 1.5,
                        y: 2.5,
                        width: 37,
                        height: 20,
                        horizontalAlignment: 1,
                        text: "上海道臻信息技术有限公司",
                    },
                    {
                        type: "text",
                        x: 1.5,
                        y: 23,
                        width: 37,
                        height: 5,
                        fontHeight: 3,
                        horizontalAlignment: 1,
                        text: "上海道臻信息技术有限公司",
                    },
                ],
            ],
            onPageComplete: (res) => {
                // 通过 pageComplete可以监控打印或者预览的进度。
                this.appendPreview(res);
                console.log(`----- 打印进度：[${res.pageIndex} / ${res.printPages}]`);
            },
        }).then((resp) => {
            if (resp.statusCode !== 0) {
                console.warn(`---- 打印失败，statusCode = ${resp.statusCode}`);
            } else {
                console.log(`---- 打印成功！`);
            }
            // this.previewLabel(resp);
        });
    },
    dataBindPrintTest() {
        const api = this.lpapi;
        const device = this.getDevice() || {};
        this.clearPreview();
        //
        api.print({
            jobInfo: {
                jobWidth: 50,
                jobHeight: 70,
                jobName: this.getJobName(),
                orientation: this.getOrientation(),
                gapType: this.getGapType(),
                printDarkness: this.getPrintDarkness(),
                printSpeed: this.getPrintSpeed(),
            },
            printerInfo: {
                name: device.name,
                deviceId: device.deviceId,
            },
            jobPage: [
                { type: "rect", x: 1, y: 1, width: 48, height: 68, lineWidth: 0.3 },
                {
                    type: "text",
                    // 打印内容可以通过 columnName 来绑定到 jobArguments 参数列表中相关数据
                    columnName: "column1",
                    x: 1,
                    y: 1,
                    width: 48,
                    height: 68,
                    fontHeight: 3,
                },
            ],
            jobArguments: [
                { column1: "1德佟印立方是一家集研发、生产、销售和技术服务于一体的专业标签打印机生产厂家，旗下拥有德佟电子和印立方两个不同系列的品牌。 公司创立于2016年，主要生产和销售移动便携式标签打印机及配套耗材，并为顾客量身定制标签标识解决方案。公司总部位于中国上海，并在广州和苏州张家港设立分公司。公司生产基地位于苏州张家港总面积为8000平，主要生产标签打印机及其使用耗材。公司虽然成立时间不长，但已推出多款标签打印机，突破产品研发壁垒，填补了市场产品空缺，推动便携式标签打印机市场发展。公司进入市场之后，凭借切合市场需求的产品、过硬的软硬件技术能力、良好的线上线下代理商合作模式， 已经成为该市场领域事实上的标杆领头羊，在保持和客户多方共赢的同时，自身销售额每年也在以超过50%的速度高速增长。业务已覆盖：通讯电力，政企教育，烟草管理，危废管理，商业零售，办公生活。" },
                { column1: "2德佟印立方是一家集研发、生产、销售和技术服务于一体的专业标签打印机生产厂家，旗下拥有德佟电子和印立方两个不同系列的品牌。 公司创立于2016年，主要生产和销售移动便携式标签打印机及配套耗材，并为顾客量身定制标签标识解决方案。公司总部位于中国上海，并在广州和苏州张家港设立分公司。公司生产基地位于苏州张家港总面积为8000平，主要生产标签打印机及其使用耗材。公司虽然成立时间不长，但已推出多款标签打印机，突破产品研发壁垒，填补了市场产品空缺，推动便携式标签打印机市场发展。公司进入市场之后，凭借切合市场需求的产品、过硬的软硬件技术能力、良好的线上线下代理商合作模式， 已经成为该市场领域事实上的标杆领头羊，在保持和客户多方共赢的同时，自身销售额每年也在以超过50%的速度高速增长。业务已覆盖：通讯电力，政企教育，烟草管理，危废管理，商业零售，办公生活。" },
                { column1: "3德佟印立方是一家集研发、生产、销售和技术服务于一体的专业标签打印机生产厂家，旗下拥有德佟电子和印立方两个不同系列的品牌。 公司创立于2016年，主要生产和销售移动便携式标签打印机及配套耗材，并为顾客量身定制标签标识解决方案。公司总部位于中国上海，并在广州和苏州张家港设立分公司。公司生产基地位于苏州张家港总面积为8000平，主要生产标签打印机及其使用耗材。公司虽然成立时间不长，但已推出多款标签打印机，突破产品研发壁垒，填补了市场产品空缺，推动便携式标签打印机市场发展。公司进入市场之后，凭借切合市场需求的产品、过硬的软硬件技术能力、良好的线上线下代理商合作模式， 已经成为该市场领域事实上的标杆领头羊，在保持和客户多方共赢的同时，自身销售额每年也在以超过50%的速度高速增长。业务已覆盖：通讯电力，政企教育，烟草管理，危废管理，商业零售，办公生活。" },
                { column1: "4德佟印立方是一家集研发、生产、销售和技术服务于一体的专业标签打印机生产厂家，旗下拥有德佟电子和印立方两个不同系列的品牌。 公司创立于2016年，主要生产和销售移动便携式标签打印机及配套耗材，并为顾客量身定制标签标识解决方案。公司总部位于中国上海，并在广州和苏州张家港设立分公司。公司生产基地位于苏州张家港总面积为8000平，主要生产标签打印机及其使用耗材。公司虽然成立时间不长，但已推出多款标签打印机，突破产品研发壁垒，填补了市场产品空缺，推动便携式标签打印机市场发展。公司进入市场之后，凭借切合市场需求的产品、过硬的软硬件技术能力、良好的线上线下代理商合作模式， 已经成为该市场领域事实上的标杆领头羊，在保持和客户多方共赢的同时，自身销售额每年也在以超过50%的速度高速增长。业务已覆盖：通讯电力，政企教育，烟草管理，危废管理，商业零售，办公生活。" },
                { column1: "5德佟印立方是一家集研发、生产、销售和技术服务于一体的专业标签打印机生产厂家，旗下拥有德佟电子和印立方两个不同系列的品牌。 公司创立于2016年，主要生产和销售移动便携式标签打印机及配套耗材，并为顾客量身定制标签标识解决方案。公司总部位于中国上海，并在广州和苏州张家港设立分公司。公司生产基地位于苏州张家港总面积为8000平，主要生产标签打印机及其使用耗材。公司虽然成立时间不长，但已推出多款标签打印机，突破产品研发壁垒，填补了市场产品空缺，推动便携式标签打印机市场发展。公司进入市场之后，凭借切合市场需求的产品、过硬的软硬件技术能力、良好的线上线下代理商合作模式， 已经成为该市场领域事实上的标杆领头羊，在保持和客户多方共赢的同时，自身销售额每年也在以超过50%的速度高速增长。业务已覆盖：通讯电力，政企教育，烟草管理，危废管理，商业零售，办公生活。" },
            ],
            onPageComplete: (res) => {
                // 通过 pageComplete可以监控打印或者预览的进度。
                this.appendPreview(res);
                console.log(`----- 打印进度：[${res.pageIndex} / ${res.printPages}]`);
            },
        }).then((resp) => {
            if (resp.statusCode !== 0) {
                console.warn(`---- 打印失败，statusCode = ${resp.statusCode}`);
            } else {
                console.log(`---- 打印成功！`);
            }
            // this.previewLabel(resp);
        });
    },
    wdfxPrintTest() {
        const device = this.getDevice() || {};
        // 清空预览列表
        this.clearPreview();
        //
        this.lpapi.printWdfx({
            domParser: new DOMParser(),
            content: wdfxStr,
            jobInfo: {
                jobName: this.getJobName(),
                orientation: this.getOrientation(),
                gapType: this.getGapType(),
                printDarkness: this.getPrintDarkness(),
                printSpeed: this.getPrintSpeed(),
            },
            printerInfo: {
                name: device.name,
                deviceId: device.deviceId,
            },
            onPageComplete: (res) => {
                // 通过 pageComplete可以监控打印或者预览的进度。
                // this.previewLabel(res);
                this.appendPreview(res);
                console.log(`----- 打印进度：[${res.pageIndex} / ${res.printPages}]`);
            },
        }).then((resp) => {
            if (resp.statusCode !== 0) {
                console.warn(`---- 打印失败，statusCode = ${resp.statusCode}`);
            } else {
                console.log(`---- 打印成功！`);
            }
            // this.previewLabel(resp);
        });
    },
    async imagesPrintTest(url) {
        const api = this.lpapi;
        //
        const labelWidth = 40;
        const labelHeight = 40;
        const margin = 2;
        //
        const image = await api.loadImage(url);
        api.startJob({
            width: labelWidth,
            height: labelHeight,
            jobName: this.getJobName(),
        });
        // 绘制标签外边框
        api.drawRectangle({
            width: labelWidth,
            height: labelHeight,
            lineWidth: 0.3,
        });
        // 绘制图片
        api.drawImage({
            image: image,
            x: margin,
            y: margin,
            width: labelWidth - margin * 2,
            height: labelHeight - margin * 2,
        });
        //
        const resp = await api.commitJob({
            gapType: this.getGapType(),
            darkness: this.getPrintDarkness(),
        });
        this.previewLabel(resp);
        return true;
    },
    /**
     * 循环打印方式1，通过嵌套调用来实现。
     *
     * @param {Object[]} list 打印数据列表
     * @param {number} index 当前打印数据的索引
     * @param {(result: any) => void} 打印结果回调函数
     */
    printQrcodePages(list, index, callback) {
        if (index >= list.length) {
            console.log(`------ 打印完毕！ -------`);
            if (callback) {
                callback("打印完毕！");
            }
        } else {
            // 未打印完毕，则继续打印下一张标签
            this.qrcodePrintTest(list[index], () => {
                console.log(`------ 打印进度：[${index + 1} / ${list.length}]`);
                this.printPages1(list, index + 1, callback);
            });
        }
    },
    async multiPagePrintTest() {
        const imageList = [
            "/static/yinlifun.png",
            "https://www.detonger.com/img/QRCode_OfficialAccounts.png",
            "/static/yinlifun.png",
            "https://www.detonger.com/img/QRCode_OfficialAccounts.png",
            "/static/yinlifun.png",
        ];
        // 链接打印机
        const res = await this.checkAndOpenPrinter();
        if (!res) {
            console.warn(`---- 打印机打开失败！`);
            wx.showToast({ title: "打印机链接失败！", icon: "error" });
            return;
        }
        // 循环打印
        for (let i = 0; i < imageList.length; i++) {
            await this.imagesPrintTest(imageList[i]);
            console.log(`------ 打印进度：[${i + 1} / ${imageList.length}]`);
        }
        console.log(`------ 打印完毕! -------`);
    },
});

/**
 * 德佟SDK Constants适配层
 * 适配原有SUPVANAPIT50PRO Constants接口到德佟SDK
 */

/**
 * 全局结果码定义
 * 保持与原有SUPVANAPIT50PRO SDK兼容的结果码
 */
const globalResultCode = {
  // 成功状态码
  ResultCodeSuccess: 0,
  ResultCode1: 1,
  
  // 模板相关
  ResultCode100: 100, // 模板信息回调
  
  // 蓝牙相关错误码 (101-117)
  ResultCode101: 101, // 初始化蓝牙模块异常
  ResultCode102: 102, // 获取本机蓝牙适配器状态异常
  ResultCode103: 103, // 开始搜寻附近的蓝牙外围设备异常
  ResultCode104: 104, // 蓝牙寻找到新设备的事件异常
  ResultCode105: 105, // 蓝牙适配器不可用
  ResultCode106: 106, // 断开蓝牙失败异常
  ResultCode107: 107, // 蓝牙序列号为空
  ResultCode108: 108, // 文本Canvas不能为空
  ResultCode109: 109, // 连接蓝牙异常
  ResultCode110: 110, // 连接硕方蓝牙异常
  ResultCode111: 111, // blemtu异常
  ResultCode112: 112, // 获取蓝牙设备服务异常
  ResultCode113: 113, // 获取蓝牙特征值异常
  ResultCode114: 114, // 获取特征值变化异常
  ResultCode115: 115, // 获取notify异常
  ResultCode116: 116, // 模板对象不能为空
  ResultCode117: 117, // 停止搜索蓝牙设备成功
  
  // 图像处理相关错误码 (118-124)
  ResultCode118: 118, // 获取条形码对象数据异常
  ResultCode119: 119, // 生成图片失败异常
  ResultCode120: 120, // 二维码转换成图片异常
  ResultCode121: 121, // 图片下载异常
  ResultCode122: 122, // 获取rgba字模数据异常
  ResultCode123: 123, // 下载本地图片异常
  ResultCode124: 124, // 生成图片数据异常
  
  // 打印相关错误码 (125-135)
  ResultCode125: 125, // 机器启动打印异常
  ResultCode126: 126, // 请关闭耗材仓盖
  ResultCode127: 127, // 耗材未装好
  ResultCode128: 128, // 请检查耗材余量
  ResultCode129: 129, // 未检测到耗材
  ResultCode130: 130, // 未识别到耗材
  ResultCode131: 131, // 耗材已用完
  ResultCode132: 132, // 打印异常终止
  ResultCode133: 133, // 色带错误
  ResultCode134: 134, // 压缩失败
  ResultCode135: 135  // 打印字模数据不能为空
};

/**
 * 德佟P2打印机特定的错误码映射
 * 将德佟SDK的错误信息映射到标准错误码
 */
const detongErrorMapping = {
  // 连接相关错误
  'connection_failed': globalResultCode.ResultCode109,
  'device_not_found': globalResultCode.ResultCode107,
  'bluetooth_unavailable': globalResultCode.ResultCode105,
  'scan_failed': globalResultCode.ResultCode103,
  
  // 打印相关错误
  'print_failed': globalResultCode.ResultCode132,
  'template_error': globalResultCode.ResultCode116,
  'canvas_error': globalResultCode.ResultCode108,
  'material_error': globalResultCode.ResultCode129,
  
  // 耗材相关错误
  'cover_open': globalResultCode.ResultCode126,
  'material_not_installed': globalResultCode.ResultCode127,
  'material_low': globalResultCode.ResultCode128,
  'material_empty': globalResultCode.ResultCode131,
  'material_unknown': globalResultCode.ResultCode130,
  'ribbon_error': globalResultCode.ResultCode133
};

/**
 * 打印机型号定义
 */
const printerModels = {
  DETONG_P2: 'DETONG_P2',
  DETONG_SERIES: ['P2', 'P2-Pro', 'P2-Mini']
};

/**
 * 打印参数常量
 */
const printConstants = {
  // 默认打印参数
  DEFAULT_DENSITY: 2,
  DEFAULT_SPEED: 25,
  DEFAULT_COPIES: 1,
  
  // 德佟P2支持的纸张类型
  PAPER_TYPES: {
    LABEL: 1,      // 标签纸
    CONTINUOUS: 2  // 连续纸
  },
  
  // 打印方向
  ORIENTATIONS: {
    NORMAL: 0,     // 正常
    ROTATE_90: 90, // 右转90度
    ROTATE_180: 180, // 旋转180度
    ROTATE_270: 270  // 右转270度
  },
  
  // 字体样式
  FONT_STYLES: {
    NORMAL: 0,
    BOLD: 1,
    ITALIC: 2,
    BOLD_ITALIC: 3
  }
};

/**
 * 德佟P2特定配置
 */
const detongP2Config = {
  // 支持的标签尺寸 (mm)
  SUPPORTED_SIZES: [
    { width: 40, height: 30, name: '40x30mm' },
    { width: 50, height: 30, name: '50x30mm' },
    { width: 60, height: 40, name: '60x40mm' }
  ],
  
  // 自动间隙检测
  AUTO_GAP_DETECTION: true,
  
  // 不支持手动设置间隙
  MANUAL_GAP_SETTING: false,
  
  // 不支持获取耗材信息
  MATERIAL_INFO_SUPPORT: false,
  
  // 默认兼容所有模板
  DEFAULT_COMPATIBILITY: true
};

/**
 * 工具函数：根据错误信息获取对应的错误码
 * @param {string} errorMessage 错误信息
 * @returns {number} 错误码
 */
function getErrorCodeByMessage(errorMessage) {
  if (!errorMessage) return globalResultCode.ResultCode132;
  
  const message = errorMessage.toLowerCase();
  
  // 检查常见错误模式
  if (message.includes('connection') || message.includes('连接')) {
    return globalResultCode.ResultCode109;
  }
  if (message.includes('bluetooth') || message.includes('蓝牙')) {
    return globalResultCode.ResultCode105;
  }
  if (message.includes('scan') || message.includes('扫描')) {
    return globalResultCode.ResultCode103;
  }
  if (message.includes('template') || message.includes('模板')) {
    return globalResultCode.ResultCode116;
  }
  if (message.includes('canvas') || message.includes('画布')) {
    return globalResultCode.ResultCode108;
  }
  if (message.includes('material') || message.includes('耗材')) {
    return globalResultCode.ResultCode129;
  }
  
  // 默认返回打印异常
  return globalResultCode.ResultCode132;
}

/**
 * 工具函数：检查是否为成功状态码
 * @param {number} code 状态码
 * @returns {boolean} 是否成功
 */
function isSuccessCode(code) {
  return code === globalResultCode.ResultCodeSuccess || 
         code === globalResultCode.ResultCode1 || 
         code === globalResultCode.ResultCode100 ||
         code === globalResultCode.ResultCode117;
}

// 导出常量对象
const constants = {
  globalResultCode,
  detongErrorMapping,
  printerModels,
  printConstants,
  detongP2Config,
  
  // 工具函数
  getErrorCodeByMessage,
  isSuccessCode
};

export default constants;
